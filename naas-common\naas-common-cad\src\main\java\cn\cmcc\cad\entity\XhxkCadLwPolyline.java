package cn.cmcc.cad.entity;

import java.io.Serializable;
import java.util.List;

import cn.cmcc.cad.service.ICadService;
import lombok.Data;

@Data
public class XhxkCadLwPolyline extends XhxkCadBaseEntity implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private int flag;
	private List<XhxkCadPoint> coordinates;
	private double minx;
	private double miny;
	private double maxx;
	private double maxy;
	private double area;
	
	public XhxkCadLwPolyline(String id, String type, String block,String layer, int flag, List<XhxkCadPoint> coordinates) {
		super(id, type, block,layer);
		this.flag = flag;
		this.coordinates = coordinates;
		if((coordinates!=null)&&!coordinates.isEmpty()) {
			this.minx = Double.MAX_VALUE;
			this.miny = Double.MAX_VALUE;
			this.maxx = -Double.MAX_VALUE;
			this.maxy = -Double.MAX_VALUE;
			for(XhxkCadPoint point:coordinates) {
				this.minx = Math.min(minx,point.getX());
				this.miny = Math.min(miny,point.getY());
				this.maxx = Math.max(maxx,point.getX());
				this.maxy = Math.max(maxy,point.getY());
			}
			this.area = (this.maxx-this.minx)*(this.maxy-this.miny);
		}
	}
	
	@Override
	public void transfer(ICadService cadService,Double scale, XhxkCadPoint minPoint) {
		// TODO Auto-generated method stub
		super.transfer(cadService,scale, minPoint);
		this.coordinates = cadService.transfer(coordinates, scale, minPoint);
	}
}
