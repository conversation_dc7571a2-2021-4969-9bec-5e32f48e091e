package cn.cmcc.common.utils.text;

import com.ibm.icu.text.CharsetDetector;
import com.ibm.icu.text.CharsetMatch;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 文件编码处理工具
 */

@Slf4j
public class EncodingConverter {

    /**
     * 将文件转换为目标编码类型
     *
     * @param file           要转换的文件
     * @param targetEncoding 目标编码类型
     */
    public static void convertToEncoding(File file, String targetEncoding) throws Exception {
        long startTime = System.currentTimeMillis();

         try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "rw");
              FileChannel fileChannel = randomAccessFile.getChannel()) {

             long fileSize = fileChannel.size();
             ByteBuffer buffer = ByteBuffer.allocate((int) fileSize);
             fileChannel.read(buffer);
             buffer.flip();
             byte[] bytes = new byte[buffer.remaining()];
             buffer.get(bytes);

             CharsetDetector detector = new CharsetDetector();
             detector.setText(bytes);

             CharsetMatch match = detector.detect();

             if (match != null) {
                 String detectedEncoding = match.getName();

                 // 如果检测到的编码与目标编码不一致时进行转换
                 if (!detectedEncoding.equalsIgnoreCase(targetEncoding)) {
                     String content = new String(bytes, detectedEncoding);
                     byte[] outputBytes = content.getBytes(targetEncoding);

                     ByteBuffer outputBuffer = ByteBuffer.allocate(outputBytes.length);
                     outputBuffer.put(outputBytes);
                     outputBuffer.flip();
                     fileChannel.write(outputBuffer, 0);
                 }
             }
         }

        long endTime = System.currentTimeMillis();
        long elapsedTime = endTime - startTime;
        log.info("转码完成，耗时：" + (elapsedTime / 1000.0) + " 秒");
    }

}
