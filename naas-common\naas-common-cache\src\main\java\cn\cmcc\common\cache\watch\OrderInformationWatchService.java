/**
 *
 *
 * @Title: OrderInformationWatch.java
 * <AUTHOR>
 * @date 2022年8月18日
 * @version V1.0
 */
package cn.cmcc.common.cache.watch;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.cache.service.CacheService;

@Component
public class OrderInformationWatchService {

	@Autowired
	private CacheService cacheService;

	public void change(String orderNo) {
		cacheService.set(CacheConstants.CacheKey.ORDER_INFO_WATCH + "_" + orderNo, 1, CachesEnums.CACHE_ORDER_INFO_WATCH.name());
	}

	public boolean isChange(String orderNo) {
		Object value = cacheService.get(CacheConstants.CacheKey.ORDER_INFO_WATCH + "_" + orderNo, CachesEnums.CACHE_ORDER_INFO_WATCH.name());
		if(value != null ) {
			return true;
		}
		return false;
	}

	public void clear(String orderNo) {
		cacheService.del(CacheConstants.CacheKey.ORDER_INFO_WATCH + "_" + orderNo, CachesEnums.CACHE_ORDER_INFO_WATCH.name());
	}

}
