package cn.cmcc.common.web.controller;

import java.beans.PropertyEditorSupport;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.owasp.encoder.Encode;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;

import cn.cmcc.common.constant.HttpStatus;
import cn.cmcc.common.utils.DateUtils;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.sql.SqlUtil;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.cmcc.common.web.page.PageDomain;
import cn.cmcc.common.web.page.PageResult;
import cn.cmcc.common.web.page.TableSupport;
import lombok.extern.slf4j.Slf4j;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */

@Slf4j
public class BaseController
{
    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder)
    {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport()
        {
            @Override
            public void setAsText(String text)
            {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage()
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageMethod.startPage(pageNum, pageSize, orderBy);
        }
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected PageResult getDataTable(List<?> list)
    {
        PageResult rspData = new PageResult();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setData(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }


    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows)
    {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }
    
    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows,Object data)
    {
        return rows > 0 ? AjaxResult.success(data) : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result)
    {
        return result ? success() : error();
    }

    /**
     * 返回成功
     */
    public AjaxResult success()
    {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error()
    {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message)
    {
        return AjaxResult.success(message);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message)
    {
        return AjaxResult.error(message);
    }

    /**
     * 页面跳转
     */
    public String redirect(String url)
    {
        return StringUtils.format("redirect:{}", url);
    }

    /**
     * 返回xml
     */
    public void writeResponse(HttpServletResponse response,String resXml) {
        response.setContentType("text/plain");
        response.setCharacterEncoding("UTF-8");
        try {
            //注意：禁止使用getWriter(),因org.apache.catalina.connector.Response的getOutputStream()方法对其判断，
            //若getWriter()正在使用则报[getWriter() has already been called for this response]异常
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(Encode.forHtml(resXml).getBytes("utf-8"));
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
        	log.error(e.getMessage(),e);
        }
    }

    public Map<String, Object> formatResut(String id,String code,String desc){
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("Id",id);
        resultMap.put("Result",code);
        resultMap.put("Desc",desc);
        return resultMap;
    }
}
