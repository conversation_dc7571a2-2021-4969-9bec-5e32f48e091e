package cn.cmcc.common.utils;

import java.io.IOException;

/**
 * 下载异常处理工具类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
public class DownloadExceptionUtil {

    /**
     * 检查是否为客户端断开连接异常
     * 
     * @param e 异常
     * @return 如果是客户端断开连接异常返回true，否则返回false
     */
    public static boolean isClientDisconnectedException(Exception e) {
        if (e == null) {
            return false;
        }
        
        // 检查异常消息
        String message = e.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            if (lowerMessage.contains("客户端断开连接") || 
                lowerMessage.contains("下载中止")) {
                return true;
            }
        }
        
        // 检查根异常
        Throwable cause = e.getCause();
        while (cause != null) {
            String causeMessage = cause.getMessage();
            if (causeMessage != null) {
                String lowerCauseMessage = causeMessage.toLowerCase();
                if (lowerCauseMessage.contains("broken pipe") ||
                    lowerCauseMessage.contains("connection reset") ||
                    lowerCauseMessage.contains("connection aborted") ||
                    lowerCauseMessage.contains("socket closed") ||
                    lowerCauseMessage.contains("connection closed") ||
                    lowerCauseMessage.contains("远程主机强迫关闭了一个现有的连接")) {
                    return true;
                }
            }
            cause = cause.getCause();
        }
        
        return false;
    }

    /**
     * 检查是否为 IOException 类型的客户端断开连接异常
     * 
     * @param e IOException异常
     * @return 如果是客户端断开连接异常返回true，否则返回false
     */
    public static boolean isClientDisconnectedException(IOException e) {
        if (e == null) {
            return false;
        }
        
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查常见的客户端断开连接异常消息
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("broken pipe") ||
               lowerMessage.contains("connection reset") ||
               lowerMessage.contains("connection aborted") ||
               lowerMessage.contains("socket closed") ||
               lowerMessage.contains("connection closed") ||
               lowerMessage.contains("远程主机强迫关闭了一个现有的连接");
    }

    /**
     * 获取友好的错误消息
     * 
     * @param e 异常
     * @return 友好的错误消息
     */
    public static String getFriendlyErrorMessage(Exception e) {
        if (isClientDisconnectedException(e)) {
            return "客户端断开连接，下载已中止";
        }
        
        if (e instanceof IOException) {
            return "文件下载失败，请检查网络连接";
        }
        
        return "下载过程中发生未知错误";
    }

    /**
     * 判断是否应该记录错误日志
     * 
     * @param e 异常
     * @return 如果应该记录错误日志返回true，否则返回false
     */
    public static boolean shouldLogError(Exception e) {
        // 客户端断开连接异常不需要记录错误日志
        return !isClientDisconnectedException(e);
    }
}
