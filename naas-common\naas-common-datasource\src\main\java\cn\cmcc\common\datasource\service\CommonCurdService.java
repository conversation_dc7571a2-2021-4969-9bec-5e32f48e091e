package cn.cmcc.common.datasource.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.cmcc.common.datasource.mapper.CommonCurdMapper;

@Component
public class CommonCurdService {

	private static final Integer BATCH_SAVE_SIZE = 50;

	@Autowired
	private CommonCurdMapper tCommonCurdMapper;

	/**
	 * 动态字段和动态值的批量插入
	 * @Title: saveBatch
	 * @Description: 批量保存
	 * @param: tableName
	 * @param: fields
	 * @param: dataList
	 * @param: @return
	 * @return: int
	 * @throws
	 */
	public int insertDynamicFieldsAndValues(String tableName, List<Map<String, Object>> dataList) {
		if (CollectionUtils.isEmpty(dataList))
			return 0;
		if (dataList.size() <= BATCH_SAVE_SIZE) {
			tCommonCurdMapper.insertDynamicFieldsAndValues(tableName, dataList.get(0), dataList);
		} else {
			List<List<Map<String, Object>>> partionList = ListUtils.partition(dataList, BATCH_SAVE_SIZE);
			partionList.forEach(pd -> {
				tCommonCurdMapper.insertDynamicFieldsAndValues(tableName, pd.get(0), pd);
			});
		}

		return dataList.size();
	}

	/**
	 * 插入的是静态字段，从对象中提取字段值
	 *
	 * @param tableName
	 * @param dataList
	 * @return int
	 */
	public int insertStaticFieldsFromObjects(String tableName, List<Map<String, Object>> dataList) {
		if (CollectionUtils.isEmpty(dataList))
			return 0;

		Set<String> fields = dataList.get(0).keySet();
		// 最大参数数量（32767）
		int max = 32767 / 3, batchSize = max / fields.size();
		if (dataList.size() <= batchSize) {
			tCommonCurdMapper.insertStaticFieldsFromObjects(tableName, dataList.get(0).keySet(), dataList);
		} else {
			List<List<Map<String, Object>>> partionList = ListUtils.partition(dataList, batchSize);
			partionList.forEach(pd -> tCommonCurdMapper.insertStaticFieldsFromObjects(tableName, pd.get(0).keySet(), pd));
		}

		return dataList.size();
	}
}
