package cn.cmcc.common.enums;

/**
 * 操作状态
 * 
 * <AUTHOR>
 *
 */
public enum BusinessStatus
{
	/**
	 * 成功
	 */
	SUCCESS(0, "成功"),

	/**
	 * 失败
	 */
	FAIL(1, "失败");

	private final Integer code;
	private final String info;

	BusinessStatus(Integer code, String info) {
		this.code = code;
		this.info = info;
	}

	public Integer getCode() {
		return code;
	}

	public String getInfo() {
		return info;
	}
}
