package cn.cmcc.common.pojo;

import java.io.Serializable;
import java.util.Set;

import lombok.Data;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */

@Data
public class LoginUser implements Serializable {
    /**
     * @Fields serialVersionUID :
     */
    private static final long serialVersionUID = 1L;

    private Long userId;

    /**
     * 回话id
     */
    private String tokenUuid;

    /**
     * 用户账号
     */
    private String account;

    /**
     * salt
     */
    private String salt;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 电话
     */
    private String phone;

    /**
     * 分公司
     */
    private Long companyId;

    /**
     * 分公司名称
     */
    private String companyName;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 状态
     */
    private String status;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 省份
     */
    @SuppressWarnings("RedundantFieldInitialization")
    private String provinceCode = null;

    public LoginUser() {

    }

    public LoginUser(Long userId, String userName) {
        this.userId = userId;
        this.userName = userName;
    }

    public boolean isAdmin() {
        return userId != null && 1L == userId;
    }
}
