package cn.cmcc.common.enums;

public enum ExportCodeTypeEnums {
	
	COMMON("common","通用查询编码"),
	CONFIG("config","系统参数编码");

	private final String code;
    private final String name;

    ExportCodeTypeEnums(String code,String name)
    {
        this.code = code;
        this.name = name;
    }

    public String getCode()
    {
        return code;
    }

    public String getName()
    {
        return name;
    }
}
