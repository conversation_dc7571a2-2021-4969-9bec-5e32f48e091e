package cn.cmcc.common.enums;

import cn.cmcc.common.utils.StringUtils;

/**
 * 数据库类型驱动
 * 
 * @version v1.0
 * <AUTHOR>
 * @date 2023年3月14日下午2:42:22
 */
public enum DBTypeEnums {
	
	PostgreSQL("org.postgresql.Driver",
            "**************************************************************",
            "SELECT tablename as label,'table' as type,'%s' as schema,true as leaf from pg_tables where schemaname = '%s' order by tablename",
            "select viewname as label,'view' as type,'%s' as schema,true as leaf from pg_views  where schemaname = '%s' order by viewname",
            "select sequencename as label,'seq' as type,'%s' as schema,true as leaf from pg_sequences  where schemaname = '%s' order by sequencename"),
	MySQL("com.mysql.cj.jdbc.Driver",
            "*******************************************************************************************************",
            "SELECT table_name as label,'table' as type,'%s' as \"schema\",true as leaf FROM information_schema.tables WHERE table_schema = '%s'",
            "SELECT table_name as label,'view' as type,'%s' as \"schema\",true as leaf FROM information_schema.views WHERE table_schema = '%s'",
            StringUtils.EMPTY),
	Oracle("oracle.jdbc.driver.OracleDriver","**************************"),
	SqlServer("com.microsoft.sqlserver.jdbc.SQLServerDriver","**************************************"),
    ClickHouse("ru.yandex.clickhouse.ClickHouseDriver","************************************************************************************************************");;

    private final String driver;
    
    private final String url;

    private final String tableSql;

    private final String viewSql;

    private final String seqSql;
    
    DBTypeEnums(String driver,String url,String tableSql,String viewSql,String seqSql) {
        this.driver = driver;
        this.url = url;
        this.tableSql = tableSql;
        this.viewSql = viewSql;
        this.seqSql = seqSql;
    }

    DBTypeEnums(String driver,String url) {
        this.driver = driver;
        this.url = url;
        this.tableSql = StringUtils.EMPTY;
        this.viewSql = StringUtils.EMPTY;
        this.seqSql = StringUtils.EMPTY;
    }
    
    public static DBTypeEnums getByValue(String value) {
        for (DBTypeEnums e : values()) {
            if (e.name().equals(value)) {
                return e;
            }
        }
        return null;
    }

	public String getDriver() {
		return driver;
	}

	public String getUrl() {
		return url;
	}

    public String getTableSql() {
        return tableSql;
    }

    public String getViewSql() {
        return viewSql;
    }

    public String getSeqSql() {
        return seqSql;
    }

    public String getUrl(String ip, String port, String dbName) {
		return String.format(url, ip,port,dbName);
	}
}
