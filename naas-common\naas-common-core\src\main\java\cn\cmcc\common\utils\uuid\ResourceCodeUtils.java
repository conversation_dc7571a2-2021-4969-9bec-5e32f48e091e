package cn.cmcc.common.utils.uuid;

import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.utils.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 资源码生成工具类
 * @date 2025/2/19 14:44
 */
public class ResourceCodeUtils {
    /**
     * <AUTHOR>  2025/2/19 16:59
     * @description: BBU/DU/CU资源码生成,基带设备资源码获取
     */
    public static String equipmentResourceCode(String xqSiteIdCmcc,Integer enodebIdPlan,String subType){
        String type = subType.toUpperCase();
        String subTypeTmp = ("CU".equals(type) || "DU".equals(type)) ? type : "baseBand";
        String resourceCode =  String.format(xqSiteIdCmcc + "S1E%s%s",enodebIdPlan,subTypeTmp);
        return resourceCode.replaceAll("-","");
    }
    /**
     * <AUTHOR>  2025/2/19 16:59
     * @description: 扩展皮站交换，资源码获取 JZXQYNKMPL55188 S1 E1 DU E1
     * @param xqSiteIdCmcc
     * @param equipSubType 设备类型：BBU/CU/DU
     * @param enodebIdPlan 基站编号
     * @param rhubIdPlan 设备编号
     */
    public static String rhubResourceCode(String xqSiteIdCmcc,String equipSubType,Integer enodebIdPlan,Integer rhubIdPlan){
        if(StringUtils.isEmpty(equipSubType)) throw new CustomException("设备子类型不能为空");
        if(!List.of("BBU","CU","DU").contains(equipSubType)){
            throw new CustomException("设备子类型只能为BBU/CU/DU");
        }
        String resourceCode = xqSiteIdCmcc + "S1E%s" + equipSubType + "E%s";
        resourceCode = String.format(resourceCode,enodebIdPlan,rhubIdPlan);
        return resourceCode.replaceAll("-","");
    }
    /**
     * <AUTHOR>  2025/2/19 16:59
     * @description: 板卡交换，资源码获取 JZXQYNKMPL55188 S1 E1 DU E1
     * @param xqSiteIdCmcc
     * @param equipSubType 设备类型：BBU/CU/DU
     * @param enodebIdPlan 基站编号
     * @param boardIdPlan 板卡编号
     */
    public static String boardResourceCode(String xqSiteIdCmcc,String equipSubType,Integer enodebIdPlan,Integer boardIdPlan){
        if(StringUtils.isEmpty(equipSubType)) throw new CustomException("设备子类型不能为空");
        if(!List.of("BBU","CU","DU").contains(equipSubType)){
            throw new CustomException("设备子类型只能为BBU/CU/DU");
        }
        String resourceCode = xqSiteIdCmcc + "S1E%s" + equipSubType + "B%s";
        resourceCode = String.format(resourceCode,enodebIdPlan,boardIdPlan);
        return resourceCode.replaceAll("-","");
    }
}
