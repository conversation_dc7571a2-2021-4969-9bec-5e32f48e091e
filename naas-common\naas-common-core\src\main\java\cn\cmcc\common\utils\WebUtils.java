package cn.cmcc.common.utils;

import java.io.*;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.ServletRequest;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.owasp.encoder.Encode;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import cn.cmcc.common.utils.html.EscapeUtil;
import cn.cmcc.common.utils.text.CharsetKit;
import cn.cmcc.common.utils.text.Convert;
import lombok.extern.slf4j.Slf4j;

/**
 * web 包里的常用的方法
 *
 * @Class Name WebUtils
 * <AUTHOR>
 * @Create In 2018年6月20日
 */

@Slf4j
public class WebUtils {
	/**
	 * 获取Boolean参数
	 *
	 * @param paramName
	 * @param request
	 * @return
	 */
	public static Boolean getBooleanParam(String paramName, HttpServletRequest request) {
		String value = request.getParameter(paramName);
		if (value == null || value.trim().isEmpty()) {
			return null;
		}
		try {
			return Boolean.valueOf(value);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 获取String参数
	 *
	 * @param paramName
	 * @param request
	 * @return
	 */
	public static String getStringParam(String paramName, HttpServletRequest request) {
		String value = request.getParameter(paramName);
		if (value == null || value.trim().isEmpty()) {
			return null;
		} else
			return value.trim().replaceAll("_", "");
	}

	public static Map<String, Object> getMapParam(HttpServletRequest request) {
		Map<String, Object> map = new HashMap<String, Object>();
		Map<String, String[]> params = request.getParameterMap();
		for (String key : params.keySet()) {
			String temp;
			try {
				String value = request.getParameter(key);
				value = value.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
				value = value.replaceAll("\\+", "%2B");
				temp = URLDecoder.decode(value, "UTF-8");
				map.put(key, "".equals(temp) ? null : temp);
			} catch (UnsupportedEncodingException e) {
				log.error(e.getMessage(),e);
			}
		}
		return map;
	}

	public static ConcurrentHashMap<String, Object> getConcurrentHashMapParam(HttpServletRequest request) {
		ConcurrentHashMap<String, Object> map = new ConcurrentHashMap<String, Object>();
		Map<String, String[]> params = request.getParameterMap();
		for (String key : params.keySet()) {
			String temp;
			try {
				String value = request.getParameter(key);
				value = value.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
				value = value.replaceAll("\\+", "%2B");
				temp = URLDecoder.decode(value, "UTF-8");
				map.put(key, "".equals(temp) ? null : temp);
			} catch (UnsupportedEncodingException e) {
				log.error(e.getMessage(),e);
			}
		}
		return map;
	}

	/**
	 * 获取Integer参数
	 *
	 * @param paramName
	 * @param request
	 * @return
	 */
	public static Integer getIntParam(String paramName, HttpServletRequest request) {
		String value = request.getParameter(paramName);
		if (value == null || value.trim().isEmpty()) {
			return null;
		}
		try {
			return Integer.valueOf(value);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 获取Double参数
	 *
	 * @param paramName
	 * @param request
	 * @return
	 */
	public static Double getDoubleParam(String paramName, HttpServletRequest request) {
		String value = request.getParameter(paramName);
		if (value == null || value.trim().isEmpty()) {
			return null;
		}
		try {
			return Double.valueOf(value);
		} catch (NumberFormatException e) {
			return null;
		}
	}

	/**
	 * 获取Float参数
	 *
	 * @param paramName
	 * @param request
	 * @return
	 */
	public static Float getFloatParam(String paramName, HttpServletRequest request) {
		String value = request.getParameter(paramName);
		if (value == null || value.trim().isEmpty()) {
			return null;
		}
		try {
			return Float.valueOf(value);
		} catch (NumberFormatException e) {
			return null;
		}
	}

	/**
	 * 获取Long参数
	 *
	 * @param paramName
	 * @param request
	 * @return
	 */
	public static Long getLongParam(String paramName, HttpServletRequest request) {
		String value = request.getParameter(paramName);
		if (value == null || value.trim().isEmpty()) {
			return null;
		}
		try {
			return Long.valueOf(value);
		} catch (NumberFormatException e) {
			return null;
		}
	}

	/**
	 * 判断Map集合非空
	 */
	public static boolean isNotNullForMap(Map<String, Object> map) {
		if (map == null || map.size() == 0) {
			return false;
		}
		return true;
	}

	/**
	 * 判断Map集合为空
	 */
	public static boolean isNullForMap(Map<String, Object> map) {
		if (map == null || map.size() == 0) {
			return true;
		}
		return false;
	}

	/**
	 * 判断对象是否不为null
	 *
	 * @param obj
	 * @return false is the param is null ,else return true.
	 */
	public static boolean isNotNull(Object obj) {
		if (obj == null) {
			return false;
		}
		return true;
	}

	/**
	 * 判断对象是否为空
	 *
	 * @param obj
	 * @return false is the parameter is null ,else return true.
	 */
	public static boolean isEmpty(Object obj) {
		if (obj == null) {
			return true;
		} else if (obj == "null") {
			return true;
		} else {
			if (obj instanceof String) {
				return ((String) obj).trim().length() == 0 ? true : false;
			}
			return false;
		}
	}

	/**
	 * 判断对象是否没有或者为空字符串
	 *
	 * @param obj
	 * @return false is the param is null ,else return true.
	 */
	public static boolean isPositiveIntValue(String str) {
		try {
			Integer value = Integer.valueOf(str);
			if (value > 0) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	public static boolean isIntValue(String value) {
		try {
			Integer.valueOf(value);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	public static boolean isDoubleValue(String value) {
		try {
			Double.valueOf(value);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	public static boolean isLongValue(String value) {
		try {
			Long.valueOf(value);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	public static boolean isBooleanValue(String value) {
		try {
			Boolean.parseBoolean(value);

			if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
				return true;

			} else {
				return false;
			}

		} catch (Exception e) {
			return false;
		}
	}

	public static boolean isPositiveDoubleValue(String str) {
		try {
			double value = Double.valueOf(str);
			if (value > 0) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 最大长度限制,字符串的最大长度限制合法
	 *
	 * @param str
	 * @return
	 */
	public static boolean isLengthValid(String str, int maxLength) {
		if (str != null && length(str.trim()) > 0 && length(str.trim()) <= maxLength) {
			return true;
		}

		return false;
	}

	/**
	 * 字符长度必须在minLength和maxLength之间
	 *
	 * @param str
	 * @param minLength
	 * @param maxLength
	 * @return
	 */
	public static boolean isBetweenLengthValid(String str, int minLength, int maxLength) {
		if (str != null && length(str.trim()) >= minLength && length(str.trim()) <= maxLength) {
			return true;
		}

		return false;
	}

	/**
	 * 价格必须在minPrice和maxPrice之间
	 *
	 * @param price
	 * @param minPrice
	 * @param maxPrice
	 * @return
	 */
	public static boolean isBetweenPriceValid(String price, double minPrice, double maxPrice) {

		try {
			int lastPoint = price.indexOf(".");// 小数点出现的位置
			if (lastPoint > 0) {// double类型
				String str = price.substring(lastPoint + 1);
				int pointAfterLenth = str.trim().length();
				if (pointAfterLenth > 2) {// 最多两位小数点
					return false;
				}
			}

			double value = Double.valueOf(price);

			if (value >= minPrice && value <= maxPrice) {
				return true;

			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 数字必须为minNumber和maxNumber之间的整数
	 *
	 * @param number
	 * @param minSort
	 * @param maxSort
	 * @return
	 */
	public static boolean isBetweenNumberValid(String number, int minNumber, int maxNumber) {

		try {
			int value = Integer.valueOf(number);

			if (value >= minNumber && value <= maxNumber) {
				return true;

			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 数量必须大于minPrice
	 *
	 * @param price
	 * @return
	 */
	public static boolean isNumberValid(String price, double minPrice) {
		try {

			double value = Double.valueOf(price);

			if (value >= minPrice && value < Double.MAX_VALUE) {

				return true;

			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 特殊字符的验证，不能包含特殊字符
	 *
	 * @param str
	 * @param spechars
	 * @return
	 */
	public static boolean isContainSpechars(String str, String spechars) {

		if (str != null && str.trim().length() > 0 && str.contains(spechars)) {
			return false;
		}

		return true;
	}

	public static double[] getGeoCoordinate(String cooridnateStr) {

		if (cooridnateStr == null) {
			return null;
		}

		cooridnateStr = cooridnateStr.trim();

		if (!cooridnateStr.startsWith("[") || !cooridnateStr.endsWith("]")) {
			return null;
		}

		cooridnateStr = cooridnateStr.trim().replaceAll("\\[|\\]", "");

		if (!cooridnateStr.contains(","))
			return null;

		double[] coordinate = new double[2];
		try {
			coordinate[0] = Double.parseDouble(cooridnateStr.split(",")[0]);
			coordinate[1] = Double.parseDouble(cooridnateStr.split(",")[1]);
			if (coordinate[0] >= 180 || coordinate[0] < -180 || coordinate[1] > 90 || coordinate[1] < -90) {
				return null;
			}
		} catch (Exception e) {
			return null;
		}
		return coordinate;
	}

	/**
	 * 将给定字符串按照逗号分隔
	 *
	 * @return
	 */
	public static List<String> split(String str) {
		List<String> list = new ArrayList<String>();

		if (str == null) {
			return list;
		}
		String[] results = str.split(",");

		for (String s : results) {
			s = s.trim();
			if (!s.isEmpty()) {
				list.add(s);
			}
		}

		return list;
	}

	/** 格式话字符串 **/
	public static String formatStr(String str) {
		if (str == null) {
			return "";
		} else {
			return str;
		}
	}

	public static Number formatNum(Number number) {
		if (number == null) {
			return 0;
		} else {
			return number;
		}
	}

	/** 去空格 **/
	public static String trim(String str) {
		if (str == null) {
			return null;
		} else {
			return str.trim();
		}
	}

	/** 统计字符串的字符数,一个汉字两个字符 ,一个字母一个字符 **/
	public static int length(String str) {
		int count = 0;
		for (int i = 0; i < str.length(); i++) {
			if (String.valueOf(str.charAt(i)).getBytes().length > 1) {
				count += 2;
			} else {
				count += 1;
			}
		}

		return count;
	}

	/** 获得根路径 */
	public static String getBaseUrl(HttpServletRequest request) {
		String realPath = null;

		if (request.getServerPort() == 80) {

			realPath = "http://" + request.getServerName() + "/";

		} else {

			realPath = "http://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath()
					+ request.getServletPath().substring(0, request.getServletPath().lastIndexOf("/") + 1);
		}

		return realPath;
	}

	/**
	 * 更改cookie的maxAge
	 */
	public static void changeCookieMaxAge(HttpServletRequest request, HttpServletResponse response, String name,
			int maxAge) {

		Cookie cookie = getCookieByName(request, name);

		if (cookie != null) {
			cookie.setMaxAge(maxAge);
			response.addCookie(cookie);
		}
	}

	/**
	 * 更改cookie的value
	 */
	public static void changeCookieValue(HttpServletRequest request, HttpServletResponse response, String name,
			String value) {

		Cookie cookie = getCookieByName(request, name);

		if (cookie != null) {
			cookie.setValue(value);
			response.addCookie(cookie);
		}
	}

	/**
	 * 销毁cookie
	 *
	 * @param request
	 * @param response
	 * @param name
	 */
	public static void destoryCookie(HttpServletRequest request, HttpServletResponse response, Cookie cookie) {

		if (cookie != null) {
			cookie.setValue(null);
			cookie.setMaxAge(0);
			// cookie.setPath(B2CConstant.COOKIE_PATH);
			response.addCookie(cookie);
		}

	}

	/**
	 * 保存一个cookie
	 *
	 * @param response
	 * @param name
	 * @param value
	 */
	public static void saveCookie(HttpServletResponse response, String name, String value, int maxAge) {

		Cookie cookie = new Cookie(name, value);
		cookie.setSecure(true);
		cookie.setHttpOnly(true);
		cookie.setMaxAge(maxAge);
		// cookie.setPath(B2CConstant.COOKIE_PATH);

		response.addCookie(cookie);
	}

	/**
	 * 根据名字获取cookie
	 *
	 * @param request
	 * @param name
	 *            cookie名字
	 * @return
	 */
	public static Cookie getCookieByName(HttpServletRequest request, String name) {
		Map<String, Cookie> cookieMap = ReadCookieMap(request);
		if (cookieMap.containsKey(name)) {
			Cookie cookie = cookieMap.get(name);
			return cookie;
		} else {
			return null;
		}
	}

	/**
	 * 将cookie封装到Map里面
	 *
	 * @param request
	 * @return
	 */
	private static Map<String, Cookie> ReadCookieMap(HttpServletRequest request) {
		Map<String, Cookie> cookieMap = new HashMap<String, Cookie>();
		Cookie[] cookies = request.getCookies();
		if (null != cookies) {
			for (Cookie cookie : cookies) {
				cookieMap.put(cookie.getName(), cookie);
			}
		}
		return cookieMap;
	}

	/**
	 * 获得唯一键
	 *
	 * @return
	 */
	public static String getUUID() {
		return java.util.UUID.randomUUID().toString().trim();
	}

	/**
	 * 控制台输出参数
	 *
	 * @param request
	 */
	public static void consoleParamInfo(HttpServletRequest request) {
		Map<?, ?> map = request.getParameterMap();
		for (Object key : map.keySet()) {
			System.out.println(key + " : " + request.getParameter(key.toString()));
		}
	}

	/**
	 * 对字符串中的一些值进行替换
	 *
	 * @param codeValue
	 *            是原字符串
	 * @param argsMap
	 *            是原字符串中要替换的信息
	 * @return 替换后的字符串
	 */
	public static String replace(String codeValue, Map<String, Object> argsMap) {
		if (codeValue == null) {
			return null;
		}

		if (argsMap == null) {
			return codeValue.trim();
		}

		Set<String> set = argsMap.keySet();
		for (String st : set) {
			Object value = argsMap.get(st);
			codeValue = codeValue.replace(st, value + "");
		}
		return codeValue.trim();
	}

	/** 将空字符串处理null **/
	public static String dealTheNull(String str) {
		if (str == null) {
			return null;
		}
		if (str.isEmpty()) {
			return null;
		}
		if (str.trim().length() == 0) {
			return null;
		}
		if (str.trim().equals("null")) {
			return null;
		}
		return str;
	}

	/** 将空字符串处理0 **/
	public static String dealTheZero(String str) {
		if (str == null) {
			return "0";
		}
		if (str.isEmpty()) {
			return "0";
		}
		if (str.trim().length() == 0) {
			return "0";
		}
		if (str.trim().equals("null")) {
			return "0";
		}
		return str;
	}

	/**
	 * 左补位
	 *
	 * @param oStr
	 *            原字符串
	 * @param len
	 *            目标字符串长度
	 * @param alexin
	 *            补位字符
	 * @return 目标字符串
	 */
	public static String padLeft(String oStr, int len, char alexin) {
		int strlen = oStr.length();
		String str = "";
		if (strlen < len) {
			for (int i = 0; i < len - strlen; i++) {
				str = str + alexin;
			}
		}
		str = str + oStr;
		return str;
	}

	/**
	 * 右补位
	 *
	 * @param oStr
	 *            原字符串
	 * @param len
	 *            目标字符串长度
	 * @param alexin
	 *            补位字符
	 * @return 目标字符串
	 */
	public static String padRight(String oStr, int len, char alexin) {
		int strlen = oStr.length();
		String str = "";
		if (strlen < len) {
			for (int i = 0; i < len - strlen; i++) {
				str = str + alexin;
			}
		}
		str = oStr + str;
		return str;
	}

	/**
	 * get the list required by MYSQL IN query.
	 *
	 * @param strArr
	 * @return
	 */
	public static String getSqlList(String[] strArr) {
		String str = "(";
		for (String string : strArr) {
			str += "'" + string + "',";
		}

		return str.substring(0, str.length() - 1) + ")";
	}

	/**
	 * get the list required by MYSQL IN query.
	 *
	 * @param strList
	 * @return
	 */
	public static String getSqlList(List<String> strList) {
		String str = "(";
		for (String string : strList) {
			str += "'" + string + "',";
		}
		return str.substring(0, str.length() - 1) + ")";
	}

	/**
	 * 将字符串时间格式转换为十位时间戳
	 */
	public static String getDate(String Input_date) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String res_date = null;
		Date date;
		try {
			date = sdf.parse(Input_date);
			long l_date = date.getTime();
			String date_temp = String.valueOf(l_date);
			res_date = date_temp.substring(0, 10);
		} catch (ParseException e) {
			log.error(e.getMessage(),e);
		}
		return res_date;
	}

	public static String getRemoteAddr(HttpServletRequest req) {
		String ip = req.getHeader("X-Forwarded-For");
		if (StringUtils.isNotBlank(ip)) {
			String[] ips = StringUtils.split(ip, ',');
			if (ips != null) {
				for (String tmpip : ips) {
					if (StringUtils.isBlank(tmpip))
						continue;
					tmpip = tmpip.trim();
					if (isIPAddr(tmpip) && !tmpip.startsWith("10.") && !tmpip.startsWith("192.168.")
							&& !"127.0.0.1".equals(tmpip)) {
						return tmpip.trim();
					}
				}
			}
		}
		ip = req.getHeader("x-real-ip");
		if (isIPAddr(ip))
			return ip;
		ip = req.getRemoteAddr();
		if (ip.indexOf('.') == -1)
			ip = "127.0.0.1";
		return ip;
	}

	/**
	 * 判断字符串是否是一个IP地址
	 *
	 * @param addr
	 * @return
	 */
	public static boolean isIPAddr(String addr) {
		if (StringUtils.isEmpty(addr))
			return false;
		String[] ips = StringUtils.split(addr, '.');
		if (ips.length != 4)
			return false;
		try {
			int ipa = Integer.parseInt(ips[0]);
			int ipb = Integer.parseInt(ips[1]);
			int ipc = Integer.parseInt(ips[2]);
			int ipd = Integer.parseInt(ips[3]);
			return ipa >= 0 && ipa <= 255 && ipb >= 0 && ipb <= 255 && ipc >= 0 && ipc <= 255 && ipd >= 0 && ipd <= 255;
		} catch (Exception e) {
		}
		return false;
	}

	public static String getIpAddr()
    {
		HttpServletRequest request = WebUtils.getRequest();
        if (request == null)
        {
            return "unknown";
        }
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("X-Real-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getRemoteAddr();
        }
        return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : EscapeUtil.clean(ip);
    }

    public static String getIpAddr(ServerHttpRequest request) {
        HttpHeaders headers = request.getHeaders();
        String ip = headers.getFirst("x-forwarded-for");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            if (ip.indexOf(",") != -1) {
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddress().getAddress().getHostAddress();
        }
        return ip;
    }

    public static boolean internalIp(String ip)
    {
        byte[] addr = textToNumericFormatV4(ip);
        if(addr != null) {
            return internalIp(addr) || "127.0.0.1".equals(ip);
        }
        return false;
    }

    private static boolean internalIp(byte[] addr)
    {
        if (StringUtils.isNull(addr) || addr.length < 2)
        {
            return true;
        }
        final byte b0 = addr[0];
        final byte b1 = addr[1];
        // 10.x.x.x/8
        final byte SECTION_1 = 0x0A;
        // 172.16.x.x/12
        final byte SECTION_2 = (byte) 0xAC;
        final byte SECTION_3 = (byte) 0x10;
        final byte SECTION_4 = (byte) 0x1F;
        // 192.168.x.x/16
        final byte SECTION_5 = (byte) 0xC0;
        final byte SECTION_6 = (byte) 0xA8;
        switch (b0)
        {
            case SECTION_1:
                return true;
            case SECTION_2:
                if (b1 >= SECTION_3 && b1 <= SECTION_4)
                {
                    return true;
                }
            case SECTION_5:
                switch (b1)
                {
                    case SECTION_6:
                        return true;
                }
            default:
                return false;
        }
    }

    /**
     * 将IPv4地址转换成字节
     *
     * @param text IPv4地址
     * @return byte 字节
     */
    public static byte[] textToNumericFormatV4(String text)
    {
        if (text.length() == 0)
        {
            return null;
        }

        byte[] bytes = new byte[4];
        String[] elements = text.split("\\.", -1);
        try
        {
            long l;
            int i;
            switch (elements.length)
            {
                case 1:
                    l = Long.parseLong(elements[0]);
                    if ((l < 0L) || (l > 4294967295L)) {
                        return null;
                    }
                    bytes[0] = (byte) (int) (l >> 24 & 0xFF);
                    bytes[1] = (byte) (int) ((l & 0xFFFFFF) >> 16 & 0xFF);
                    bytes[2] = (byte) (int) ((l & 0xFFFF) >> 8 & 0xFF);
                    bytes[3] = (byte) (int) (l & 0xFF);
                    break;
                case 2:
                    l = Integer.parseInt(elements[0]);
                    if ((l < 0L) || (l > 255L)) {
                        return null;
                    }
                    bytes[0] = (byte) (int) (l & 0xFF);
                    l = Integer.parseInt(elements[1]);
                    if ((l < 0L) || (l > 16777215L)) {
                        return null;
                    }
                    bytes[1] = (byte) (int) (l >> 16 & 0xFF);
                    bytes[2] = (byte) (int) ((l & 0xFFFF) >> 8 & 0xFF);
                    bytes[3] = (byte) (int) (l & 0xFF);
                    break;
                case 3:
                    for (i = 0; i < 2; ++i)
                    {
                        l = Integer.parseInt(elements[i]);
                        if ((l < 0L) || (l > 255L)) {
                            return null;
                        }
                        bytes[i] = (byte) (int) (l & 0xFF);
                    }
                    l = Integer.parseInt(elements[2]);
                    if ((l < 0L) || (l > 65535L)) {
                        return null;
                    }
                    bytes[2] = (byte) (int) (l >> 8 & 0xFF);
                    bytes[3] = (byte) (int) (l & 0xFF);
                    break;
                case 4:
                    for (i = 0; i < 4; ++i)
                    {
                        l = Integer.parseInt(elements[i]);
                        if ((l < 0L) || (l > 255L)) {
                            return null;
                        }
                        bytes[i] = (byte) (int) (l & 0xFF);
                    }
                    break;
                default:
                    return null;
            }
        }
        catch (NumberFormatException e)
        {
            return null;
        }
        return bytes;
    }

    public static String getHostIp()
    {
        try
        {
            return InetAddress.getLocalHost().getHostAddress();
        }
        catch (UnknownHostException e)
        {
        }
        return "127.0.0.1";
    }

    public static String getHostName()
    {
        try
        {
            return InetAddress.getLocalHost().getHostName();
        }
        catch (UnknownHostException e)
        {
        }
        return "未知";
    }

	/**
     * 获取完整的请求路径，包括：域名，端口，上下文访问路径
     *
     * @return 服务地址
     */
    public static String getUrl()
    {
        HttpServletRequest request = WebUtils.getRequest();
        if(request != null ) return getDomain(request);
        return StringUtils.EMPTY;

    }

    public static String getDomain(HttpServletRequest request)
    {
        StringBuffer url = request.getRequestURL();
        String contextPath = request.getServletContext().getContextPath();
        return url.delete(url.length() - request.getRequestURI().length(), url.length()).append(contextPath).toString();
    }

    /**
     * 获取String参数
     */
    public static String getParameter(String name)
    {
    	HttpServletRequest request = getRequest();
    	if(request != null ) return request.getParameter(name);
        return StringUtils.EMPTY;
    }

    /**
     * 获取String参数
     */
    public static String getParameter(String name, String defaultValue)
    {
    	HttpServletRequest request = getRequest();
    	if(request != null) return Convert.toStr(request.getParameter(name), defaultValue);
    	return StringUtils.EMPTY;
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name)
    {
    	HttpServletRequest request = getRequest();
    	if(request != null) return Convert.toInt(request.getParameter(name));
    	return null;
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name, Integer defaultValue)
    {
    	HttpServletRequest request = getRequest();
    	if(request != null) return Convert.toInt(request.getParameter(name), defaultValue);
    	return null;
    }

    /**
     * 获取request
     */
    public static HttpServletRequest getRequest()
    {
    	ServletRequestAttributes attributes = getRequestAttributes();
    	if(attributes != null)  return attributes.getRequest();
    	return null;
    }

    /**
     * 获取response
     */
    public static HttpServletResponse getResponse()
    {
    	ServletRequestAttributes attributes = getRequestAttributes();
    	if(attributes != null) return attributes.getResponse();
    	return null;
    }

    /**
     * 获取session
     */
    public static HttpSession getSession()
    {
    	HttpServletRequest request = getRequest();
    	if(request != null) return request.getSession();
    	return null;
    }

    public static ServletRequestAttributes getRequestAttributes()
    {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        return (ServletRequestAttributes) attributes;
    }

    public static String getHeader(HttpServletRequest request, String name)
    {
        String value = request.getHeader(name);
        if (StringUtils.isEmpty(value))
        {
            return StringUtils.EMPTY;
        }
        return urlDecode(value);
    }

    public static Map<String, String> getHeaders(HttpServletRequest request)
    {
        Map<String, String> map = new LinkedHashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        if (enumeration != null)
        {
            while (enumeration.hasMoreElements())
            {
                String key = enumeration.nextElement();
                String value = request.getHeader(key);
                map.put(key, value);
            }
        }
        return map;
    }

    /**
     * 内容编码
     *
     * @param str 内容
     * @return 编码后的内容
     */
    public static String urlEncode(String str)
    {
        try
        {
            return URLEncoder.encode(str, CharsetKit.UTF_8);
        }
        catch (UnsupportedEncodingException e)
        {
            return StringUtils.EMPTY;
        }
    }

    /**
     * 内容解码
     *
     * @param str 内容
     * @return 解码后的内容
     */
    public static String urlDecode(String str)
    {
        try
        {
            return URLDecoder.decode(str, CharsetKit.UTF_8);
        }
        catch (UnsupportedEncodingException e)
        {
            return StringUtils.EMPTY;
        }
    }

    /**
     * 将字符串渲染到客户端
     *
     * @param response 渲染对象
     * @param string 待渲染的字符串
     * @return null
     */
    public static String renderString(HttpServletResponse response, String string) {
        try
        {
			response.setStatus(200);
			response.setContentType("application/json");
			response.setCharacterEncoding("utf-8");
			//禁止使用response.getWriter()方法，会导致所有接口异常【getWriter() has already been called for this response】
			OutputStream out = response.getOutputStream();
			out.write(Encode.forHtml(string).getBytes("utf-8"));
			out.flush();
			out.close();
        } catch (IOException e) {
        	log.error(e.getMessage(),e);
        }
        return null;
    }

    /**
     * 是否是Ajax异步请求
     *
     * @param request
     */
    public static boolean isAjaxRequest(HttpServletRequest request)
    {
        String accept = request.getHeader("accept");
        if (accept != null && accept.indexOf("application/json") != -1)
        {
            return true;
        }

        String xRequestedWith = request.getHeader("X-Requested-With");
        if (xRequestedWith != null && xRequestedWith.indexOf("XMLHttpRequest") != -1)
        {
            return true;
        }

        String uri = request.getRequestURI();
        if (StringUtils.inStringIgnoreCase(uri, ".json", ".xml"))
        {
            return true;
        }

        String ajax = request.getParameter("__ajax");
        if (StringUtils.inStringIgnoreCase(ajax, "json", "xml"))
        {
            return true;
        }
        return false;
    }

    public static String getBodyString(ServletRequest request)
    {
        StringBuilder sb = new StringBuilder();
        try (InputStream inputStream = request.getInputStream();
        		BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, CharsetKit.CHARSET_UTF_8)))
        {
            String line = "";
            while ((line = reader.readLine()) != null)
            {
                sb.append(line);
            }
        }
        catch (IOException e)
        {
            log.warn("getBodyString出现问题！");
        }
        return sb.toString();
    }

    /**
     *
     * @param response
     * @param url
     * void
     */
    public static void redirect(HttpServletResponse response,String url) {
    	try {
			response.sendRedirect(url); // NOSONAR
		} catch (IOException e) {
			log.error(e.getMessage(),e);
		}
    }
}
