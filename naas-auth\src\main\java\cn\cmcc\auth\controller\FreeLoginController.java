package cn.cmcc.auth.controller;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.WebUtils;
import cn.cmcc.common.web.controller.BaseController;

/**
* <AUTHOR>
* @date 2021年7月14日 上午10:53:51
* @Description 老版本免密码登录，集团，湖南等在用
* @version 1.0
*/

@Controller
@RequestMapping({"/interfaceCont"})
public class FreeLoginController extends BaseController{

	/**
     * URL
     */
    @Value("${cas.serverUrl:..}")
    private String serverUrl;

	@CrossOrigin
    @GetMapping({"/getLoginInfo"})
	public void checkLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String userName = WebUtils.getParameter("userName");
		if (StringUtils.isNotEmpty(userName)) {
			WebUtils.redirect(response,serverUrl +  "/naas_hn/ssoLogin?username=" + userName + "&key=tl+PSzVVdMP3ooewECJYrg==");
		}else {
			WebUtils.redirect(response,serverUrl + "/naas_hn");
		}

	}
}
