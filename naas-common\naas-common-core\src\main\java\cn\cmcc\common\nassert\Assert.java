package cn.cmcc.common.nassert;

import java.util.Collection;
import java.util.Map;

import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.utils.StringUtils;

/**
 *
 *
 * <AUTHOR>
 * @date 2023年2月22日
 * @version V1.0
 */
public class Assert extends org.springframework.util.Assert {

	public static void isTrue(boolean expression, String message) {
		if (!expression) {
			throw new CustomException(message);
		}
	}
	
	public static void notNull(@Nullable Object object, String message) {
		if (object == null) {
			throw new CustomException(message);
		}
	}

	public static void notEmpty(@Nullable String object, String message) {
		if (StringUtils.isEmpty(object)) {
			throw new CustomException(message);
		}
	}
	
	public static void notEmpty(@Nullable Map<?, ?> map, String message) {
		if (CollectionUtils.isEmpty(map)) {
			throw new CustomException(message);
		}
	}
	
	public static void allNoEmpty(String message,final CharSequence... css) {
		if(StringUtils.isAnyEmpty(css)) {
			throw new CustomException(message);
		}
	}
		
	public static void notEmpty(@Nullable Collection<?> collection, String message) {
		if (CollectionUtils.isEmpty(collection)) {
			throw new CustomException(message);
		}
	}
	
	public static void ce(String message) {
		throw new CustomException(message);
	}
}
