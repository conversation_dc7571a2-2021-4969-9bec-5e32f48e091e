package cn.cmcc.cad.entity;

import java.io.Serializable;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.LinkedList;
import java.util.List;

import cn.cmcc.cad.service.ICadService;
import lombok.Data;

@Data
public class XhxkCadLine extends XhxkCadBaseEntity implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private XhxkCadPoint firstPoint;
	private XhxkCadPoint secondPoint;
	private double minx;
	private double miny;
	private double maxx;
	private double maxy;
	private double length;
	private int angle;
	private int startX;
	private int startY;
	private int endX;
	private int endY;
	private String startYAndLengh;
	
	public XhxkCadLine(String id, String type, String block,String layer, XhxkCadPoint firstPoint, XhxkCadPoint secondPoint) {
		super(id, type, block,layer);
		this.firstPoint = firstPoint;
		this.secondPoint = secondPoint;
		this.minx = Math.min(firstPoint.getX(),secondPoint.getX());
		this.maxx = Math.max(firstPoint.getX(),secondPoint.getX());
		this.miny = Math.min(firstPoint.getY(),secondPoint.getY());
		this.maxy = Math.max(firstPoint.getY(),secondPoint.getY());
		this.length = Math.sqrt(Math.pow(maxx-minx, 2)+Math.pow(maxy-miny, 2));
		this.startX=format(firstPoint.getX());
		this.startY=format(firstPoint.getY());
		this.endX=format(secondPoint.getX());
		this.endY=format(secondPoint.getY());
		this.init();
		this.startYAndLengh = this.endY+"_"+this.length+"_"+this.angle;
	}
	
	public void init() {
		if(startX-endX==0) {
			this.angle=0;
		}else {
			this.angle = format(Math.toDegrees(Math.atan((double)(startY-endY)/(startX-endX))));
			if(this.angle<0) {
				//this.angle=this.angle+360;
			}
		}
	}

	public int format(double value) {
		try {
			DecimalFormat df = new DecimalFormat("#.#");
	        df.setRoundingMode(RoundingMode.HALF_UP);
	        String result = df.format(value).replace(".", "");
	        return Integer.parseInt(result);
		} catch (Exception e) {
			return 0;
		}
	}
	
	@Override
	public void transfer(ICadService cadService,Double scale, XhxkCadPoint minPoint) {
		// TODO Auto-generated method stub
		super.transfer(cadService,scale, minPoint);
		this.firstPoint = cadService.transfer(firstPoint, scale, minPoint);
		this.secondPoint = cadService.transfer(secondPoint, scale, minPoint);
	}
	
	public void reload() {
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		points.add(getFirstPoint());
		points.add(getSecondPoint());
		setPoints(points);
		setWkt();
	}
	
	/**
	 * 判断线条是否相交
	 * @param otherLine
	 * @return
	 */
	public int countIntersectionPoints(XhxkCadLine otherLine) {
	    int count = 0;	    
	    double x1 = this.firstPoint.getX();
	    double y1 = this.firstPoint.getY();
	    double x2 = this.secondPoint.getX();
	    double y2 = this.secondPoint.getY();
	    double x3 = otherLine.getFirstPoint().getX();
	    double y3 = otherLine.getFirstPoint().getY();
	    double x4 = otherLine.getSecondPoint().getX();
	    double y4 = otherLine.getSecondPoint().getY();	    
	    // 判断两条线段是否相交
	    double d = (y4 - y3) * (x2 - x1) - (y2 - y1) * (x4 - x3);
	    if (d != 0) { // 两条线段不平行
	        double ua = ((x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)) / (double)d;
	        double ub = ((x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)) / (double)d;	        
	        // 判断交点是否在两条线段之内
	        if (ua >= 0 && ua <= 1 && ub >= 0 && ub <= 1) {
	            count++;
	        }
	    }	    
	    return count;
	}

}
