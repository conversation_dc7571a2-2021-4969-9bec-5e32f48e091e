/**
 * @Title: SuccessFailEnums.java
 * @Package cn.cmcc.common.enums
 * 
 * <AUTHOR>
 * @date 2021年11月3日
 * @version V1.0
 */
package cn.cmcc.common.enums;

public enum DealStatusEnums {

	SUCCESS("S", "成功"), 
	FAIL("F", "失败"), 
	UNTEATED("U","未处理"),
	PROCESSING("P","处理中"),
	RETRYING("R", "重新处理中");

    private final String code;
    private final String info;

    DealStatusEnums(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    
}
