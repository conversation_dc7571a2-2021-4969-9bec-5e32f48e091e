package cn.cmcc.common.utils.sql;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.google.common.base.Joiner;

import cn.cmcc.common.exception.BaseException;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.utils.StringUtils;

/**
 * sql操作工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SqlUtil {
	/**
	 * 仅支持字母、数字、下划线、空格、逗号、小数点（支持多个字段排序）
	 */
	public static String SQL_PATTERN = "[a-zA-Z0-9_\\ \\,\\.]+";

	/**
	 * 检查字符，防止注入绕过
	 */
	public static String escapeOrderBySql(String value) {
		if (StringUtils.isNotEmpty(value) && !isValidOrderBySql(value)) {
			throw new BaseException("参数不符合规范，不能进行查询");
		}
		return value;
	}

	/**
	 * 验证 order by 语法是否符合规范
	 */
	public static boolean isValidOrderBySql(String value) {
		return value.matches(SQL_PATTERN);
	}

	/**
	 *
	 * 生成创建表SQL
	 *
	 * @Title: createTableSql
	 * @param tableName
	 * @param fieldList
	 * @param isTempTable 是否临时表
	 * @return String
	 * @throws
	 */
	public static String createTableSql(String tableName, List<String> fieldList, boolean isTempTable) {
		if (StringUtils.isEmpty(tableName) || CollectionUtils.isEmpty(fieldList)) {
			throw new CustomException("参数不允许为空");
		}
		String createSql = "CREATE " + (isTempTable ? "TEMPORARY" : "") + " TABLE " + tableName + "(";
		String tableField = fieldList.stream().map(item -> "\"" + item + "\"  varchar(4096)")
				.reduce((item, value) -> item + ",  " + value).orElse(StringUtils.EMPTY);

		if (StringUtils.isEmpty(tableField)) {
			throw new CustomException("生成字段信息异常");
		}
		createSql += tableField + ")";
		if (isTempTable) {
			createSql += " ON COMMIT DROP";
		}
		return createSql;
	}

	/**
	 *
	 * 居于已经存在的表创建表
	 *
	 * @Title: createTableSqlOnExistTable
	 * @param tableName
	 * @param existTable
	 * @param fieldList
	 * @param isTempTable
	 * @return String
	 * @throws
	 */
	public static String createTableSqlOnExistTable(String tableName, String existTable, List<String> fieldList,
			boolean isTempTable) {
		if (StringUtils.isEmpty(tableName) || StringUtils.isEmpty(existTable)) {
			throw new CustomException("参数不允许为空");
		}
		String createSql = "CREATE " + (isTempTable ? "TEMPORARY" : "") + " TABLE " + tableName ;
		if (isTempTable) {
			createSql += " ON COMMIT DROP";
		}
		createSql += " AS SELECT " + (CollectionUtils.isEmpty(fieldList) ? " * "
				: Joiner.on(",").join(fieldList.stream().map(SqlUtil::addSymbol).collect(Collectors.toList())));
		createSql +=" FROM " + existTable + " WHERE 1 = 2";
		return createSql;
	}

	/**
	 * 加上"",否则，数字或下划线开头开头的字段有问题
	 * @param field
	 * @return
	 */
	public static String addSymbol(String field) {
		if(field.contains("\"")) return field;
		return "\"" + field + "\"";
	}

	/**
	 *
	 * 生成创建索引SQL
	 *
	 * @Title: createTableIndex
	 * @param tableName
	 * @param indexField
	 * @return String
	 * @throws
	 */
	public static String createTableIndex(String tableName, String indexField) {
		if (StringUtils.isEmpty(tableName) || StringUtils.isEmpty(indexField)) {
			throw new CustomException("参数不允许为空");
		}
		return "CREATE INDEX idx_" + tableName + "_" + indexField + " ON " + tableName + " USING btree (" + indexField
				+ " ASC NULLS LAST )";
	}

	/**
	 *
	 * 生成删表SQL
	 *
	 * @Title: createDropSql
	 * @param tableName
	 * @return String
	 * @throws
	 */
	public static String createDropSql(String tableName) {
		return "DROP TABLE IF EXISTS " + tableName;
	}

	/**
	 *
	 * 查询字段SQL
	 *
	 * @Title: createQueryTableFieldSql
	 * @param schema
	 * @param tableName
	 * @return String
	 * @throws
	 */
	public static String createQueryTableFieldSql(String schema, String tableName) {
		return "select column_name from information_schema.columns where table_schema='" + schema
				+ "' and table_name ='" + tableName + "'";
	}

	/**
	 * 替换字符串中的占位符
	 *
	 * @param strsql
	 * @param dataMap
	 * @return
	 * @version v1.0
	 * @date 2023年4月24日上午10:05:29
	 */
	public static String getRunSql(String str,Map<String, Object> dataMap) {
		Matcher matcher = Pattern.compile("\\$\\{(.+?)\\}").matcher(str);
		while(matcher.find()) {
			String replace = matcher.group(0);
			String key = replace.replace("${", StringUtils.EMPTY).replace("}", StringUtils.EMPTY).trim();
			str = str.replace(replace, MapUtils.getString(dataMap, key, StringUtils.EMPTY));
		}
		return str;
	}



}
