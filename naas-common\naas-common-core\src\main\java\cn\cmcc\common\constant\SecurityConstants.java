package cn.cmcc.common.constant;

/**
 * 权限相关通用常量
 * 
 * <AUTHOR>
 */
public class SecurityConstants
{
    /**
     * 用户字段
     */
    public static final String USER_ID = "user_id",ACCOUNT = "account";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";
    
    /**
     * 回话标识
     */
    public static final String TOKEN_UUID = "token_uuid";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";

    /**
     * 登录用户
     */
    public static final String LOGIN_USER = "login_user";
    
    /**
     * 令牌
     */
    public static final String TOKEN = "token";
    
    /**
     * 失效
     */
    public static final String EXPIRES = "expires";
    
    /**
     * account
     */
    public static final String USERNAME = "userName";
    
    /**
     * 令牌自定义标识
     */
    public static final String AUTHENTICATION = "Authorization";
    
    /**
     * cookie
     */
    public static final String COOKIE_TOKEN_KEY = "Naas-Msa-Token";

    /**
     * 令牌前缀
     */
    public static final String PREFIX = "Bearer ";

    /**
     * 令牌秘钥
     */
    public final static String SKEY = "bjxhxkcmccnaas5tgb6tfc9uytrdvb";

    /**
     * dubbo用户信息传递
     */
    public final static String DUBBO_USER_KEY = "DUBBO_USER_KEY";

    /**
     * 用户名密码错误
     */
    public final static String AUTHENTICATION_FAILED = "用户认证失败";
    
    /**
     * 最大错误次数
     */
    public final static Integer MAX_PASSWD_ERROR_COUNT = 5;
}
