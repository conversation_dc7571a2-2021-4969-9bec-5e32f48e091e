package cn.cmcc.common.datasource.mapper;

import cn.cmcc.common.datasource.handler.MapResultHandler;
import cn.cmcc.common.datasource.pojo.bo.TCommonQueryFieldDto;
import cn.cmcc.common.datasource.pojo.bo.TableColumn;
import cn.cmcc.common.datasource.pojo.bo.TableColumnsInfoBo;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface CommonMapper {

    @Insert("<script>" + "${sqlStr}" + "</script>")
    int runInsertSql(@Param("sqlStr") String sqlStr);

    @Select("<script>" + "${sqlStr}" + "</script>")
    List<Map<String, Object>> selectList(@Param("sqlStr") String sqlStr);

    @Select("<script>" + "${sqlStr}" + "</script>")
    Map<String, Object> selectMap(@Param("sqlStr") String sqlStr);

    @Select("<script>" + "${sqlStr}" + "</script>")
    @ResultType(Map.class)
    void selectMap(@Param("sqlStr") String sqlStr, MapResultHandler<String, Object> resultHandler);

    @Update("<script>" + "${sqlStr}" + "</script>")
    int update(@Param("sqlStr") String sqlStr);

    @Select("<script>" + "${sqlStr}" + "</script>")
    @ResultType(LinkedHashMap.class)
    List<LinkedHashMap<Object, Object>> selectLinkedList(@Param("sqlStr") String sqlStr);

    @Select("select count(1) from pg_class where relname = #{tableName}")
    int checkTableExist(String tableName);

    @Select("select count(1) from information_schema.tables where table_schema = #{schema} and table_name = #{tableName}")
    int checkTableExistBySchema(String schema,String tableName);

    @Select("select count(1) from ${tableName} limit 1")
    int checkTableExistData(String tableName);

    Map<String, String> selectFieldTitle(String nspname, String tableName, String fieldName);


    @Insert("insert into ${targetTableName} select * from ${sourceTableName} ")
    int copyTableData( String sourceTableName, String targetTableName);

    /**
     * @throws
     * @Title: checkFunctionExist
     * @Description: 检查方法是否存在
     * @param: @param functionName
     * @param: @return
     * @return: int
     */
    int checkFunctionExist(String functionName);

    void dropTable(String tableName);

    void createFindattname();

    /**
     * @throws
     * @Title: createShowcreatetable
     * @Description:创建createShowcreatetable方法
     * @param:
     * @return: void
     */
    void createShowcreatetable();

    /**
     * 获取表结构信息
     *
     * @param tableName  表名，用于指定需要获取结构信息的表
     * @param schemeName 模式名，用于指定表所属的数据库模式
     * @return 返回一个包含TableColumn对象的列表，每个对象代表表结构中的一个列
     */
    List<TableColumn> getTableStructure(@Param("tableName") String tableName, @Param("schemeName") String schemeName);
    /**
      * <AUTHOR> @date 2025/7/14 14:33
      * @description 创建临时表-text字段
      * @param tmpTable 临时表名
     * @param fieldList 字段名
     * @param quote 是否添加引号
      */
    int createTextTempTable(String tmpTable, Collection<String> fieldList,Boolean quote);
    /**
      * <AUTHOR> @date 2025/7/14 14:37
      * @description 创建临时表-varchar
      * @param tmpTable
     * @param fieldList
      */
    int createTextTable(String tmpTable, Collection<String> fieldList);

    @MapKey("table_name")
    Map<String, Map> getTableNameMap(Set<String> relatedTables);

    /**
     * <AUTHOR>  2025/6/6 15:18
     * @description:  根据查询码查询通用字段
     * @param code
     * @return List<TCommonQueryFieldDto>
     */
    List<TCommonQueryFieldDto> findCommonQryFieldByQryCode(String code);
    /**
     * <AUTHOR>  2025/6/9 10:38
     * @description:  根据表名查询表的列信息
     * @param
     * @return List
     */
    List<TableColumnsInfoBo> findTableInfoByTableName(@Param("tableName") String tableName);

    /**
     * 获取数据库当前模式
     * @return
     */
    String getDBCurrentSchema();

    Integer alterAddColumn(@Param("tableName") String tableName, @Param("colName") String colName);

    Integer alterDropColumn(@Param("tableName") String tableName, @Param("colName") String colName);
}
