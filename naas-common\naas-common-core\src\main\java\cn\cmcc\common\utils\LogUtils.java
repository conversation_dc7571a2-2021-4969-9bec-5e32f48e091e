package cn.cmcc.common.utils;

import org.slf4j.Logger;

/**
 * 处理并记录日志文件
 * 
 * <AUTHOR>
 */
public class LogUtils
{
	private long startTimeMs;
	private long lastStartTimeMs;
	private Logger logger;
	
	public LogUtils(Logger log) {
		this.logger = log;
		startTimeMs = System.currentTimeMillis();
		lastStartTimeMs = startTimeMs;
	}
	
	public void printTime(String desc) {
		long currentMs = System.currentTimeMillis();
		logger.info("{}, takes {} ms.", desc, currentMs - lastStartTimeMs);
		lastStartTimeMs = System.currentTimeMillis();
	}
	
	public void printEnd(String desc) {
		long currentMs = System.currentTimeMillis();
		logger.info("{},total takes {} ms.", desc, currentMs - startTimeMs);
	}
	
    public static String getBlock(Object msg)
    {
        if (msg == null) msg = StringUtils.EMPTY;
        return "[" + msg.toString() + "]";
    }
    
    /**
     * 打印耗时日志
     *
     * @param log     日志
     * @param desc    描述
     * @param startMs 开始时间
     * @return 当前时间
     */
    public static long takeLog(Logger log, String desc, long startMs) {
        long currentMs = System.currentTimeMillis();
        log.info("{}, takes {} ms.", desc, currentMs - startMs);
        return currentMs;
    }
    
}
