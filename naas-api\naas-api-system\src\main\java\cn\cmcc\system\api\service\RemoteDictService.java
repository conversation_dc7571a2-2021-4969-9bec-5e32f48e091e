/**
 * @Title: RemoteDictService.java
 * @Package cn.cmcc.system.api.service
 *
 * <AUTHOR>
 * @date 2022年4月12日
 * @version V1.0
 */
package cn.cmcc.system.api.service;

import cn.cmcc.system.api.pojo.bo.DictDataBO;
import cn.cmcc.system.api.pojo.dto.SysDictDataDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RemoteDictService {

	 /**
	  *
	  * @Title: selectDictDataByCode
	  * @Description: 通过字典编码查询字典
	  * @param: @param code
	  * @param: @return
	  * @return: SysDictData
	  * @throws
	  */
	 public SysDictDataDto selectDictDataByCode(String dictTypeCode,String code);

	 /**
	  *
	  * @Title: selectDictDataById
	  * @Description: 通过字典编码查询字典
	  * @param: @param code
	  * @param: @return
	  * @return: SysDictData
	  * @throws
	  */
	 public SysDictDataDto selectDictDataById(Long id);

	 /**
	  *
	  * @Title: selectDictDataListByTypeCode
	  * @Description: 通用字典类型查询字典列表
	  * @param: @param typeCode
	  * @param: @return
	  * @return: List<SysDictData>
	  * @throws
	  */
	 public List<SysDictDataDto> selectDictDataListByTypeCode(String typeCode);

	/**
	 *
	  * 刷新缓存
	  *
	 * @Title: loadDictCache
	  * @param type
	  * @param key
	  * @return Void
	  * @throws
	  */
	 public void loadDictCache(String type,String key);


	/**
	     * 查询字典列表
	     *
	     * @param type            类型
	     * @param currentProvince 当前省份
	     * @return 字典列表
	 */
	 public List<DictDataBO> selectDictList(String type);
	 
	 /**
	  * 删除数据字典
	  * 
	  * @param typeCode
	  * @param code 
	  * void
	  */
	 public void deleteDictData(String typeCode,String code);
}
