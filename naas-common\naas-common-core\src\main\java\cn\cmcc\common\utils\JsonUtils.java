/**
 *
 *
 * @Title: JsonUtils.java
 * <AUTHOR>
 * @date 2022年9月16日
 * @version V1.0
 */
package cn.cmcc.common.utils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class JsonUtils {
    public static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper()
                .setSerializationInclusion(JsonInclude.Include.ALWAYS) // 保留 null 字段
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        SimpleModule module = new SimpleModule();
        module.addSerializer(Date.class, new JsonSerializer<>() {
            @Override
            public void serialize(Date date, JsonGenerator generator, SerializerProvider provider) throws IOException {
                if (date != null) {
                    generator.writeString(DateUtils.dateTime(date));
                }
            }
        }).addSerializer(String.class, new JsonSerializer<>() {
            @Override
            public void serialize(String data, JsonGenerator generator, SerializerProvider provider) throws IOException {
                if (StringUtils.isEmpty(data)) {
                    generator.writeNull();
                } else {
                    generator.writeString(data);
                }
            }
        });
        module.addDeserializer(Date.class, new JsonDeserializer<>() {
            @SneakyThrows
            @Nullable
            @Override
            public Date deserialize(JsonParser parser, DeserializationContext context) {
                String source = parser.getText();
                if (StringUtils.isEmpty(source)) {
                    return null;
                }
                return DateUtils.parseDate(source.trim());
            }
        });
        objectMapper.registerModule(module);
    }


    public static String toJsonString(Object object) {
        if (ObjectUtil.isNull(object)) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String text, Class<T> clazz) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        try {
            return objectMapper.readValue(text, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T convertValue(Object data,Class<T> clazz){
        if (ObjectUtil.isEmpty(data)) {
            return null;
        }
        return objectMapper.convertValue(data, clazz);
    }

    public static <T> T parseObject(byte[] bytes, Class<T> clazz) {
        if (ArrayUtil.isEmpty(bytes)) {
            return null;
        }
        try {
            return objectMapper.readValue(bytes, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String text, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        try {
            return objectMapper.readValue(text, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static <T> T parseObject(Object object, Class<T> clazz) {
        if (ObjectUtil.isEmpty(object)) {
            return null;
        }
        try {
            return objectMapper.readValue(toJsonString(object), clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static Dict parseMap(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        try {
            return objectMapper.readValue(text, objectMapper.getTypeFactory().constructType(Dict.class));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static List<Dict> parseArrayMap(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        try {
            return objectMapper.readValue(text, objectMapper.getTypeFactory().constructCollectionType(List.class, Dict.class));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> parseArray(String text, Class<T> clazz) {
        if (StringUtils.isEmpty(text)) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(text, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    //暴露readTree()方法
    public static ObjectNode readTree(String json) {
        try {
            return (ObjectNode) objectMapper.readTree(json);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /***
	 * <AUTHOR> <<EMAIL>>
	 * @Version V1.0.0
	 * @Since
	 * @Date 2021/5/8 4:12 下午
	 * @Description 仅限 json与实体简单对应 （Date 类型字段不适用，待扩展。。。）
	 * @Param [cls, param, fieldConfigs]
	 * @return T
	 */
	public static <T> T formatJsonToT(Class<T> cls, JSONObject param, Map<String, String> fieldMap) {
		T result;
		try {
			result = cls.newInstance();
		} catch (InstantiationException | IllegalAccessException e) {
			log.error(e.getMessage(),e);
			return null;
		}
		Arrays.stream(cls.getDeclaredFields()).parallel().forEach(field -> {
			field.setAccessible(true);
			Object value = param.get(fieldMap.get(field.getName()));
			try {
				if (value == null) {
					field.set(result, null);
				} else {
					if (field.getType().equals(BigDecimal.class)) {
						if (value instanceof Integer) {
							value = new BigDecimal((Integer) value);
						} else if (value instanceof String) {
							value = new BigDecimal((String) value);
						} else if (value instanceof Long) {
							value = new BigDecimal((Long) value);
						} else if (value instanceof Double) {
							value = BigDecimal.valueOf((Double) value);
						}
					} else if (field.getType().equals(String.class)) {
						value = value.toString();
					}
					field.set(result, value);
				}
			} catch (IllegalAccessException e) {
				log.error(e.getMessage(),e);
			}
		});
		return result;
	}

    /***
     * <AUTHOR> jsckson实现
     * @Description 仅限 json与实体简单对应 （Date 类型字段不适用，待扩展。。。）
     * @Param [cls, param, fieldMap]
     * @return T
     */
    public static <T> T formatJsonToTNew(Class<T> cls, ObjectNode param, Map<String, String> fieldMap) {
        // 创建一个新的对象实例
        T result;
        try {
            result = cls.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            log.error(e.getMessage(), e);
            return null;
        }

        // 创建新的 ObjectNode，来存储映射后的字段
        ObjectNode mappedNode = objectMapper.createObjectNode();

        // 遍历 fieldMap，进行字段映射
        fieldMap.forEach((entityField, jsonField) -> {
            JsonNode jsonValue = param.get(jsonField);

            // 根据字段类型进行必要的类型转换
            if (jsonValue != null) {
                try {
                    if (cls.getDeclaredField(entityField).getType().equals(BigDecimal.class)) {
                        // BigDecimal 类型特殊处理
                        if (jsonValue.isInt() || jsonValue.isLong()) {
                            mappedNode.put(entityField, new BigDecimal(jsonValue.asLong()));
                        } else if (jsonValue.isDouble()) {
                            mappedNode.put(entityField, BigDecimal.valueOf(jsonValue.asDouble()));
                        } else if (jsonValue.isTextual()) {
                            mappedNode.put(entityField, new BigDecimal(jsonValue.asText()));
                        }
                    } else {
                        // 直接拷贝其他类型的数据
                        mappedNode.set(entityField, jsonValue);
                    }
                } catch (NoSuchFieldException e) {
                    log.error("Field mapping error: " + entityField, e);
                }
            }
        });

        // 将映射后的 ObjectNode 转换为指定的对象类型
        try {
            result = objectMapper.treeToValue(mappedNode, cls);
        } catch (Exception e) {
            log.error("Failed to map JSON to object", e);
            return null;
        }

        return result;
    }



}
