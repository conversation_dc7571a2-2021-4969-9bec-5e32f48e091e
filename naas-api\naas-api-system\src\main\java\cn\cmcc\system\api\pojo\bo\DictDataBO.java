package cn.cmcc.system.api.pojo.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 字典 BO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Data
@NoArgsConstructor
public class DictDataBO implements Serializable{

    /**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 字典 ID
     */
    private Long dictId;

    /**
     * 父 ID
     */
    private Long parentId;

    /**
     * 代码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 父
     */
    private String parent;

    /**
     * 综资编码名称
     */
    private String resourceName;
    /**
     * 综资编码
     */
    private String resourceCode;

    /**
     * 孩子
     */
    private List<DictDataBO> children;

}
