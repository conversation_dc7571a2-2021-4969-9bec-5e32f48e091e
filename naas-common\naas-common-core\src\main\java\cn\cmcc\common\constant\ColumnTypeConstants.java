/**
 * @Title: ColumnTypeConstants.java
 * @Package cn.cmcc.common.constant
 * 
 * <AUTHOR>
 * @date 2022年5月31日
 * @version V1.0
 */
package cn.cmcc.common.constant;

public interface ColumnTypeConstants {

    /** 数据库字符串类型 */
    public static final String[] COLUMNTYPE_STR = { "char", "varchar", "nvarchar", "varchar2" };

    /** 数据库文本类型 */
    public static final String[] COLUMNTYPE_TEXT = { "tinytext", "text", "mediumtext", "longtext" };

    /** 数据库时间类型 */
    public static final String[] COLUMNTYPE_TIME = { "datetime", "time", "date", "timestamp" };

    /** 数据库数字类型 */
    public static final String[] COLUMNTYPE_NUMBER = { "tinyint", "smallint", "mediumint", "int" ,"int2" ,"int4", "int8",
    		"number", "integer", "numeric", "decimal", "float4", "float8", "bit", "bigint", "float", "double"};

    /** geometry */
    public static final String GEOMETRY = "geometry";

}
