package cn.cmcc.common.utils.file;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * @author: <PERSON><PERSON> huafei
 * @date: 2021-08-17 17:35
 */

@Slf4j
public class CsvFileParser {

	private static CsvFileParser instance;

	private CsvFileParser() {}

	public static CsvFileParser getInstance() {
		if (instance == null) {
			instance = new CsvFileParser();
		}
		return instance;
	}

	public <T> List<T> getCsvData(File file, Class<T> clazz, String charserName) {
		return getCsvData(file,clazz,"$",charserName);
	}
	
	public <T> List<T> getCsvData(File file, Class<T> clazz,String separator, String charserName) {
		CsvToBean<T> csvToBean = null;
		try {
			csvToBean = new CsvToBeanBuilder<T>(new InputStreamReader(new FileInputStream(file), charserName))
					.withSeparator(separator.toCharArray()[0])
					.withType(clazz)
					.build();
		} catch (UnsupportedEncodingException | FileNotFoundException e) {
			log.error(e.getMessage(),e);
		}
		assert csvToBean != null;
		return csvToBean.parse();
	}

	/**
     * 
     *
     * @param file csv文件
     * @return 数组
	 * @throws FileNotFoundException 
	 * @throws UnsupportedEncodingException 
     */
    public static List<String> header(File file,String charserName) throws UnsupportedEncodingException, FileNotFoundException {
        CSVReader csvReader = new CSVReaderBuilder(
                new BufferedReader(
                        new InputStreamReader(new FileInputStream(file), charserName))).build();
        Iterator<String[]> iterator = csvReader.iterator();
        if(iterator.hasNext()) {
            return Arrays.asList(iterator.next());
        }
        return null;
    }

}
