package cn.cmcc.auth.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.WebUtils;
import cn.cmcc.common.utils.encryption.AESECBUtil;
import cn.cmcc.common.utils.http.HttpClientUtils;
import cn.cmcc.common.utils.http.bo.HttpReqParam;
import cn.cmcc.common.web.controller.BaseController;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.core.util.XmlUtil;

/** 
 * <AUTHOR>
 * @date 2021/03/21 9:42
 * 福建网优系统登录接口
*/

@Controller
@RequestMapping("/sso")
public class FJLoginController extends BaseController{

	private final Logger logger = LoggerFactory.getLogger(FJLoginController.class);

	/**
     * 福建登录url
     */
    @Value("${cmcc.fjLogin:}")
    private String fjLoginUrl;
    
    /**
     * URL
     */
    @Value("${cas.serverUrl:..}")
    private String serverUrl;
    
    @CrossOrigin
    @GetMapping("/login")
    public void checkLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String key = "sde@5f98H*^hsff%dfs$r344&df8543*er";
        String username = request.getParameter("user");
        String ticket = request.getParameter("ticket");
        String ivUser = request.getHeader("iv-user");
        logger.info("url：" + fjLoginUrl); 
        logger.info("获取到的参数-iv-user：" + ivUser);
        logger.info("获取到的参数-user：" + username);
        logger.info("获取到的参数-ticket：" + ticket);
        if (!StringUtils.isEmpty(ivUser)){
        	 logger.info("iv-users参数为:"+ivUser+",根据iv-users自动登录!!");
        	 WebUtils.redirect(response,serverUrl + "/naas_fj/#/ssoLogin?username=" + AESECBUtil.encrypt(ivUser, key) + "&key=tl+PSzVVdMP3ooewECJYrg==");
        }else if(!StringUtils.isEmpty(username)) {
        	logger.info("request:" + request.toString());
            HashMap<String, Object> xmlMap = new HashMap<>();
            xmlMap.put("user_name", username);
            xmlMap.put("ticket", ticket);
            //Map转换XML 请求转化
            String postXml ="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><operation_in><user_name>"+username+"</user_name><ticket>"+ticket+"</ticket></operation_in>";
            logger.info("postXml:" + postXml);
            AjaxResult ajr = HttpClientUtils.send(HttpReqParam.builder(fjLoginUrl, postXml, HttpMethod.POST).mediaType(MediaType.APPLICATION_XML).build());
            String xmlBack = ajr.getData().toString();
            logger.info("xmlBack：" + xmlBack);
            //去网优网优系统权限系统验证
            Map<String, Object> map =XmlUtil.xmlToMap(xmlBack);
            Object resultCode = map.get("resultCode");
            logger.info("状态码（resultCode）：" + resultCode);
            logger.info("状态码描述：" + map.get("description"));
            logger.info("验证码校验失败后跳到登录界面url：" + map.get("loginUrl"));
            String successCode = "200";
            if (resultCode==null||successCode.equals(resultCode)) {
                //自动登录
                logger.info("响应成功，正在进行自动登录！");
                WebUtils.redirect(response,serverUrl + "/naas_fj/#/ssoLogin?username=" + AESECBUtil.encrypt(username, key) + "&key=tl+PSzVVdMP3ooewECJYrg==");
            } else {
                //跳转登录页
                logger.info("响应失败，跳转登录页");
                WebUtils.redirect(response,map.get("loginUrl").toString());
            }
        } else {
            //跳转登录页
            logger.info("三个参数为空，跳转默认登录平台 ");
            WebUtils.redirect(response,serverUrl + "/naas_fj/");
        }
    }
}
