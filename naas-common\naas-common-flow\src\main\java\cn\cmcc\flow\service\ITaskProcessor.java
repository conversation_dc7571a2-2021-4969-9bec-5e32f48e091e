package cn.cmcc.flow.service;

import cn.cmcc.flow.common.enums.ProcDefKeyEnum;

/**
 * 
 * 
 * @version v1.0
 * <AUTHOR>
 * @date 2023年5月29日上午9:36:41
 */
public interface ITaskProcessor {

	ProcDefKeyEnum[] procDefKeys();

	/**
	 * 包含流程
	 *
	 * @param defKey 流程键
	 * @return 是否包含
	 */
	default boolean contains(ProcDefKeyEnum defKey) {
		ProcDefKeyEnum[] keys = procDefKeys();
		if (keys == null)
			return false;

		for (ProcDefKeyEnum key : keys) {
			if (key == defKey)
				return true;
		}
		return false;
	}

	/**
	 * 包含流程
	 *
	 * @param defKey 流程键
	 * @return 是否包含
	 */
	default boolean contains(String defKey) {
		ProcDefKeyEnum[] keys = procDefKeys();
		if (keys == null)
			return false;

		for (ProcDefKeyEnum key : keys) {
			if (key.getCode().equals(defKey))
				return true;
		}
		return false;
	}

}
