package cn.cmcc.common.datasource.config;

import cn.cmcc.common.enums.DBTypeEnums;
import cn.cmcc.common.nassert.Assert;
import cn.cmcc.common.utils.StringUtils;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.stat.DruidDataSourceStatManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 动态数据源
 *
 * <AUTHOR>
 */
public class DynamicDataSource extends AbstractRoutingDataSource
{
    private static AtomicReference<DynamicDataSource> referenceInsantance = new AtomicReference<DynamicDataSource>();

	public static Map<Object, Object> dynamicTargetDataSources;

	private final Logger log = LoggerFactory.getLogger(DynamicDataSource.class);

    public static DynamicDataSource getInstance() {
        for(;;) {
            DynamicDataSource instance = referenceInsantance.get();
            if(instance != null ) {
                return instance;
            }
            instance = new DynamicDataSource();
            if(referenceInsantance.compareAndSet(null, instance)) {
                return instance;
            }
        }
    }

    @Override
    public void setTargetDataSources(Map<Object, Object> targetDataSources) {
        super.setTargetDataSources(targetDataSources);
        dynamicTargetDataSources = targetDataSources;
    }

    /**
     * 批量添加数据源
     * @param ddsMap
     */
	public void addTargetDataSources(Map<String, DruidDataSource> ddsMap) {
		if (ddsMap == null) return;
		Map<Object, Object> dynamicTargetDataSources2 = dynamicTargetDataSources;
		for (Map.Entry<String, DruidDataSource> dds : ddsMap.entrySet()) {
			dynamicTargetDataSources2.put(dds.getKey(), dds.getValue());
		}
		this.setTargetDataSources(dynamicTargetDataSources2);
		super.afterPropertiesSet();

        dynamicTargetDataSources = dynamicTargetDataSources2;
	}

    /**
     *
     * @param druidDataSource
     * @return
     */
    public DruidDataSource createDataSource(DruidDataSource druidDataSource) {
        try {
            if (!linkDatasource(druidDataSource.getDriverClassName(),druidDataSource.getDriverClassName(),
                    druidDataSource.getUrl(), druidDataSource.getUsername(), druidDataSource.getPassword(),
                    druidDataSource.getDriverClassLoader())) {
                Assert.ce("无法连接数据库，请检查配置信息。");
            }

            druidDataSource.setFilters("stat");
            druidDataSource.init();
        	Map<Object, Object> dynamicTargetDataSources2 = dynamicTargetDataSources;
            // 加入map
            dynamicTargetDataSources2.put(druidDataSource.getName(), druidDataSource);
            // 将map赋值给父类的TargetDataSources
            this.setTargetDataSources(dynamicTargetDataSources2);
            // 将TargetDataSources中的连接信息放入resolvedDataSources管理
            super.afterPropertiesSet();

            dynamicTargetDataSources = dynamicTargetDataSources2;

            log.info(druidDataSource.getName()+"数据源初始化成功");
            return druidDataSource;
        } catch (Exception e) {
        	log.error("createDataSource error:",e);
            return null;
        }
    }

    /**
     * 删除数据源
     * @param dataSourceName
     * @return
     */
    public boolean delDatasources(String dataSourceName) {
        Map<Object, Object> dynamicTargetDataSources2 = DynamicDataSource.dynamicTargetDataSources;
        if (dynamicTargetDataSources2.containsKey(dataSourceName)) {
            Set<DruidDataSource> druidDataSourceInstances = DruidDataSourceStatManager.getDruidDataSourceInstances();
            for (DruidDataSource druidDataSource : druidDataSourceInstances) {
                if (dataSourceName.equals(druidDataSource.getName())) {
                    dynamicTargetDataSources2.remove(dataSourceName);
                    DruidDataSourceStatManager.removeDataSource(druidDataSource);
                    setTargetDataSources(dynamicTargetDataSources2);// 将map赋值给父类的TargetDataSources
                    super.afterPropertiesSet();// 将TargetDataSources中的连接信息放入resolvedDataSources管理
                    return true;
                }
            }
            return false;
        } else {
            return false;
        }
    }


    /**
     * 判断数据库是否能连接
     *
     * @param driveClass 连接驱动
     * @param url        连接地址
     * @param username   用户名
     * @param password   密码
     * @return 连接是否成功
     */
    private boolean linkDatasource(String type,String driveClass, String url,
                                   String username, String password,ClassLoader cusClassLoader) {
        log.info("尝试连接数据库,类型:{},地址:{},用户名:{},密码:{}", type, url, username, password);
        Connection connection = null;
        try {
            if(DBTypeEnums.PostgreSQL.name().equals(type)) {
                Class.forName(driveClass);
                //
                DriverManager.setLoginTimeout(1);
                // 相当于连接数据库
                connection = DriverManager.getConnection(url, username, password);
            }else {
                Driver driverClass = (Driver) cusClassLoader.loadClass(driveClass).newInstance();
                ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
                try {
                    Thread.currentThread().setContextClassLoader(cusClassLoader);
                    Properties props = new Properties();
                    props.setProperty("user", username);
                    props.setProperty("password", password);
                    connection = driverClass.connect(url, props);
                } finally {
                    Thread.currentThread().setContextClassLoader(classLoader);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return true;
    }


    @Override
    protected Object determineCurrentLookupKey()
    {
    	String datasource =  DynamicDataSourceContextHolder.getDataSource();
    	if (!StringUtils.isEmpty(datasource)) {
            Map<Object, Object> dynamicTargetDataSources2 = DynamicDataSource.dynamicTargetDataSources;
            if (dynamicTargetDataSources2.containsKey(datasource)) {
                log.info("---当前数据源：" + datasource + "---");
            } else {
                log.info("不存在的数据源：");
                return null;
            }
        }
        return datasource;

    }
}
