package cn.cmcc.common.enums;

import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.ftp.Ftp;
import cn.cmcc.common.utils.ftp.FtpUtil;
import cn.cmcc.common.utils.ftp.SftpUtil;

/**
 * 
 * 文件来源类型
 * 
 * @version v1.0
 * <AUTHOR>
 * @date 2023年7月6日下午5:22:48
 */
public enum FileSourceTypeEnum {

	LOCAL("LOCAL","本地") {
		@Override
		public Ftp newFtp(String address, int port, String username, String password,String privateKey) {
			throw new CustomException("LOCAL不需要创建FTP");
		}
	},
	FTP("FTP","ftp") {
		@Override
		public Ftp newFtp(String ip, int port, String username, String password,String privateKey) {
			return new FtpUtil(ip,port,username,password);
		}
	},
	SFTP("SFTP","sftp") {
		@Override
		public Ftp newFtp(String ip, int port, String username, String password,String privateKey) {
			return new SftpUtil(username,password,ip,port,privateKey);
		}
	};
	
	private final String code;
	private final String info;
	
	public abstract Ftp newFtp(String ip, int port, String username, String password,String privateKey);

	FileSourceTypeEnum(String code, String info) {
		this.code = code;
		this.info = info;
	}

	public String getCode() {
		return code;
	}

	public String getInfo() {
		return info;
	}
	
	public static FileSourceTypeEnum getByValue(String value) {
		if(StringUtils.isEmpty(value)) return LOCAL;
        for (FileSourceTypeEnum e : values()) {
            if (e.getCode().equals(value)) {
                return e;
            }
        }
        return LOCAL;
    }
}
