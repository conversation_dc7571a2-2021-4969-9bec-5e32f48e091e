package cn.cmcc.auth.service.impl;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.cmcc.auth.config.properties.CaptchaProperties;
import cn.cmcc.auth.service.CapchaCodeService;
import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.cache.service.CacheService;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.pojo.LoginBody;
import cn.cmcc.common.security.enums.LoginSourceEnums;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.uuid.IdUtils;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;

/**
 * 验证码实现处理
 *
 * <AUTHOR>
 */
@Service
public class CapchaCodeServiceImpl implements CapchaCodeService {
	
    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;
    
	@Autowired
	private CacheService cacheService;

	@Autowired
	private CaptchaProperties captchaProperties;

	/**
	 * 生成验证码
	 */
	@Override
	public AjaxResult createCapcha() throws IOException, CustomException {
		Map<String, Object> dataMap = new HashMap<String, Object>();

		boolean captchaOnOff = captchaProperties.getEnabled();
		if (!captchaOnOff) {
			dataMap.put("open", false);
			return AjaxResult.success(dataMap);
		}

		dataMap.put("open", true);

		// 保存验证码信息
		String uuid = IdUtils.simpleUUID();
		String verifyKey = CacheConstants.CacheKey.CAPTCHA_CODE_KEY + uuid;

		LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(150, 40, 4, 4);
		lineCaptcha.createCode();
		cacheService.set(verifyKey, lineCaptcha.getCode(), CAPTCHA_EXPIRATION, TimeUnit.MINUTES,
				CachesEnums.CACHE_DEFAULT.name());

		dataMap.put("uuid", uuid);
		dataMap.put("img", lineCaptcha.getImageBase64());
		AjaxResult ajax = AjaxResult.success(dataMap);

		return ajax;
	}

	/**
	 * 校验验证码
	 */
	@Override
	public AjaxResult checkCapcha(LoginBody form) throws CustomException {
		if(LoginSourceEnums.APP.getCode().equals(form.getSource())) return AjaxResult.success();
		
		if (StringUtils.isEmpty(form.getCode())) {
			return AjaxResult.warn("验证码不能为空");
		}
		if (StringUtils.isEmpty(form.getUuid())) {
			return AjaxResult.warn("验证码已失效");
		}
		String verifyKey = CacheConstants.CacheKey.CAPTCHA_CODE_KEY + form.getUuid();
		Object captcha = cacheService.get(verifyKey,CachesEnums.CACHE_DEFAULT.name());
		cacheService.del(verifyKey,CachesEnums.CACHE_DEFAULT.name());
		if (captcha == null) {
			return AjaxResult.warn("验证码已失效");
		}
		if (!form.getCode().equalsIgnoreCase(captcha.toString())) {
			return AjaxResult.warn("验证码错误");
		}
		
		return AjaxResult.success();
	}
}
