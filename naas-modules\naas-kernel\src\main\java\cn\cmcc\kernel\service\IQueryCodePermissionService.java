package cn.cmcc.kernel.service;

import cn.cmcc.kernel.domain.TQueryCodePermission;
import cn.cmcc.kernel.domain.TRoleQueryCodePermission;

import java.util.List;

/**
 * 通用查询编码权限服务接口
 *
 * <AUTHOR> 4.0 sonnet
 */
public interface IQueryCodePermissionService {

    /**
     * 检查用户是否有权限访问指定的查询编码
     *
     * @param username  用户名
     * @param queryCode 查询编码
     * @param userId
     * @return true-有权限，false-无权限
     */
    boolean hasPermission(String username, String queryCode, Long userId);

    /**
     * 清除用户权限缓存
     *
     * @param username 用户名
     */
    void clearUserPermissionCache(String username);

    /**
     * 清除查询编码权限缓存
     *
     * @param queryCode 查询编码
     */
    void clearQueryCodePermissionCache(String queryCode);

    // ==================== 用户权限管理方法 ====================

    /**
     * 授予用户权限
     *
     * @param username       用户名
     * @param queryCode      查询编码
     * @param permissionType 权限类型
     */
    void grantUserPermission(String username, String queryCode, String permissionType);

    /**
     * 撤销用户权限
     *
     * @param username  用户名
     * @param queryCode 查询编码
     */
    void revokeUserPermission(String username, String queryCode);

    /**
     * 获取用户权限列表
     *
     * @param username 用户名
     * @return 权限列表
     */
    List<TQueryCodePermission> getUserPermissions(String username);

    /**
     * 批量授予用户权限
     *
     * @param username       用户名
     * @param queryCodes     查询编码列表
     * @param permissionType 权限类型
     */
    void batchGrantUserPermissions(String username, List<String> queryCodes, String permissionType);

    /**
     * 批量撤销用户权限
     *
     * @param username   用户名
     * @param queryCodes 查询编码列表
     */
    void batchRevokeUserPermissions(String username, List<String> queryCodes);

    // ==================== 角色权限管理方法 ====================

    /**
     * 授予角色权限
     *
     * @param roleKey        角色标识
     * @param queryCode      查询编码
     * @param permissionType 权限类型
     */
    void grantRolePermission(String roleKey, String queryCode, String permissionType);

    /**
     * 撤销角色权限
     *
     * @param roleKey   角色标识
     * @param queryCode 查询编码
     */
    void revokeRolePermission(String roleKey, String queryCode);

    /**
     * 获取角色权限列表
     *
     * @param roleKey 角色标识
     * @return 权限列表
     */
    List<TRoleQueryCodePermission> getRolePermissions(String roleKey);

    /**
     * 批量授予角色权限
     *
     * @param roleKey        角色标识
     * @param queryCodes     查询编码列表
     * @param permissionType 权限类型
     */
    void batchGrantRolePermissions(String roleKey, List<String> queryCodes, String permissionType);

    /**
     * 批量撤销角色权限
     *
     * @param roleKey    角色标识
     * @param queryCodes 查询编码列表
     */
    void batchRevokeRolePermissions(String roleKey, List<String> queryCodes);

    // ==================== 查询方法 ====================

    /**
     * 获取用户可访问的所有查询编码
     *
     * @param username 用户名
     * @return 查询编码列表
     */
    List<String> getUserAccessibleCodes(String username);

    /**
     * 获取有权限访问指定编码的用户列表
     *
     * @param queryCode 查询编码
     * @return 用户名列表
     */
    List<String> getUsersWithCodeAccess(String queryCode);

    /**
     * 获取有权限访问指定编码的角色列表
     *
     * @param queryCode 查询编码
     * @return 角色标识列表
     */
    List<String> getRolesWithCodeAccess(String queryCode);
}
