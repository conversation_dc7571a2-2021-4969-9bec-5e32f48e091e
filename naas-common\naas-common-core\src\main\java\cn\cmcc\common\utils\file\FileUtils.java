package cn.cmcc.common.utils.file;

import cn.cmcc.common.config.NaasConfig;
import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.uuid.IdUtils;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;


/**
 * 文件处理工具类
 *
 * <AUTHOR>
 */

@Slf4j
public class FileUtils extends org.apache.commons.io.FileUtils {
    public static String FILENAME_PATTERN = "[a-zA-Z0-9_\\-\\|\\.\\u4e00-\\u9fa5]+";

    /**
     * 输出指定文件的byte数组
     *
     * @param filePath 文件路径
     * @param os       输出流
     * @return
     */
    public static void writeBytes(String filePath, OutputStream os) throws IOException {
        File file = FileUtils.getFile(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException(filePath);
        }

        // 使用 try-with-resources 确保文件输入流正确关闭
        // 注意：不要在这里关闭 OutputStream，因为它可能是 HttpServletResponse 的输出流
        try (FileInputStream fis = FileUtils.openInputStream(file)) {
            byte[] buffer = new byte[8192]; // 增大缓冲区提高性能
            int length;
            while ((length = fis.read(buffer)) > 0) {
                os.write(buffer, 0, length);
            }
            // 确保数据写入
            os.flush();
        } catch (IOException e) {
            // 重新抛出 IOException，让调用者处理
            throw e;
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        boolean flag = false;
        File file = FileUtils.getFile(filePath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists() && file.delete()) {
            flag = true;
        }
        return flag;
    }

    /**
     * 批量删除文件
     *
     * @param filePaths 文件
     * @return
     */
    public static boolean deleteFiles(List<String> filePaths) {
        if (CollectionUtils.isEmpty(filePaths)) return false;

        filePaths.forEach(f -> FileUtils.deleteFile(f));
        return true;
    }

    /**
     * 文件名称验证
     *
     * @param filename 文件名称
     * @return true 正常 false 非法
     */
    public static boolean isValidFilename(String filename) {
        return filename.matches(FILENAME_PATTERN);
    }

    /**
     * 检查文件是否可下载
     *
     * @param resource 需要下载的文件
     * @return true 正常 false 非法
     */
    public static boolean checkAllowDownload(String resource) {
        // 禁止目录上跳级别
        if (StringUtils.contains(resource, "..")) {
            return false;
        }

        // 检查允许下载的文件规则
        if (ArrayUtils.contains(MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, FileTypeUtils.getFileType(resource))) {
            return true;
        }

        // 不在允许下载的文件规则
        return false;
    }

    /**
     * 下载文件名重新编码
     *
     * @param request  请求对象
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    public static String setFileDownloadHeader(HttpServletRequest request, String fileName) throws UnsupportedEncodingException {
        final String agent = request.getHeader("USER-AGENT");
        String filename = fileName;
        if (agent.contains("MSIE")) {
            // IE浏览器
            filename = URLEncoder.encode(filename, "utf-8");
            filename = filename.replace("+", " ");
        } else if (agent.contains("Firefox")) {
            // 火狐浏览器
            filename = new String(fileName.getBytes(), "ISO8859-1");
        } else if (agent.contains("Chrome")) {
            // google浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        }
        return filename;
    }

    /**
     * 下载文件名重新编码
     *
     * @param response     响应对象
     * @param realFileName 真实文件名
     * @return
     */
    public static void setAttachmentResponseHeader(HttpServletResponse response, String realFileName) throws UnsupportedEncodingException {
        String percentEncodedFileName = percentEncode(realFileName);

        StringBuilder contentDispositionValue = new StringBuilder();
        contentDispositionValue.append("attachment; filename=")
                .append(percentEncodedFileName)
                .append(";")
                .append("filename*=")
                .append("utf-8''")
                .append(percentEncodedFileName);

        response.setHeader("Content-disposition", contentDispositionValue.toString());
    }

    /**
     * 百分号编码工具方法
     *
     * @param s 需要百分号编码的字符串
     * @return 百分号编码后的字符串
     */
    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
        return encode.replaceAll("\\+", "%20");
    }

    public static void copyFileToCSV(String sourceFile, String targetFile, String task_info) {
        try (FileInputStream fr = FileUtils.openInputStream(FileUtils.getFile(sourceFile)); BufferedReader br = new BufferedReader(new InputStreamReader(fr, "UTF-8"))) {
            StringBuffer sBuffer = new StringBuffer();
            String line = br.readLine();
            int index = 0;
            while (line != null) {
                if (index > 0) {
                    if (!line.trim().equals("")) {
                        sBuffer.append((line + task_info) + "\n");
                    }
                }
                index++;
                line = br.readLine();
            }
            writeFile(targetFile, sBuffer.toString());
        } catch (Exception e) {
            log.error("copyFileToCSV error:", e);
        }
    }

    public static void copyFile(String sourceFile, String targetFile) {
        try {
            copyFile(FileUtils.getFile(sourceFile), FileUtils.getFile(targetFile));
        } catch (Exception e) {
            log.error("copyFile error:", e);
        }
    }

    public static void copyFile(File sourceFile, File targetFile) throws IOException {
        try (FileInputStream input = FileUtils.openInputStream(sourceFile); BufferedInputStream inBuff = new BufferedInputStream(input); FileOutputStream output = FileUtils.openOutputStream(FileUtils.getFile(targetFile)); BufferedOutputStream outBuff = new BufferedOutputStream(output)) {
            byte[] b = new byte['?'];
            int len;
            while ((len = inBuff.read(b)) != -1) {

                outBuff.write(b, 0, len);
            }
            outBuff.flush();
        }
    }

    public static void copyDirectiory(String sourceDir, String targetDir) throws IOException {
        FileUtils.getFile(targetDir).mkdirs();

        File[] file = FileUtils.getFile(sourceDir).listFiles();
        for (int i = 0; i < file.length; i++) {
            if (file[i].isFile()) {
                File sourceFile = file[i];

                File targetFile = FileUtils.getFile(FileUtils.getFile(targetDir).getAbsolutePath() + File.separator + file[i].getName());
                copyFile(sourceFile, targetFile);
            }
            if (file[i].isDirectory()) {
                String dir1 = sourceDir + "/" + file[i].getName();

                String dir2 = targetDir + "/" + file[i].getName();
                copyDirectiory(dir1, dir2);
            }
        }
    }

    public static void deleteFile(File file) {
        if (file.exists()) {
            if (file.isFile() && file.delete()) {
            } else if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (int i = 0; i < files.length; i++) {
                    deleteFile(files[i]);
                }
                if (!file.delete()) {

                }
            }
        }
    }

    public static void deleteFile(File file, boolean deleteSelf) {
        if (file.exists()) {
            if ((file.isFile()) && (deleteSelf) && file.delete()) {
            } else if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (int i = 0; i < files.length; i++) {
                    deleteFile(files[i], true);
                }
                if (deleteSelf && file.delete()) {
                }
            }
        }
    }


    public static void deleteFolder(String folderPath) {
        File folder = FileUtils.getFile(folderPath);

        if (folder.exists() && folder.isDirectory()) {
            try {
                deleteDirectory(folder);
                log.info("文件夹删除成功！");
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        } else {
            log.info("文件夹不存在或不是一个有效的文件夹！");
        }
    }

    /**
     * 删除 days 的文件
     *
     * @param dirPath
     * @param days
     * @throws IOException
     * @version v1.0
     * @date 2023年7月7日下午12:46:55
     */
    public static void deleteFilesOlderThanNDays(String dirPath, int days) throws IOException {
        long cutOff = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L);
        try (Stream<Path> spath = Files.list(FileUtils.getFile(dirPath).toPath())) {
            spath.filter(path -> {
                try {
                    return Files.isRegularFile(path) && Files.getLastModifiedTime(path).to(TimeUnit.MILLISECONDS) < cutOff;
                } catch (IOException ex) {
                    return false;
                }
            }).forEach(path -> {
                try {
                    Files.delete(path);
                } catch (IOException ex) {
                }
            });
        }
    }

    public static boolean isEdit(String ext) {
        String editExtLiString = ".html,.htm,.jsp,.ftl,.xml,.xhtml,.txt,.css,.js";
        if (editExtLiString.indexOf(ext) != -1) {
            return true;
        }
        return false;
    }

    public static boolean writeFile(String filePathAndName, String fileContent) {
        return writeFile(filePathAndName, fileContent, "UTF-8");
    }

    public static boolean writeFile(String filePathAndName, String fileContent, String chartset) {
        try {
            File f = FileUtils.getFile(filePathAndName);
            File file = FileUtils.getFile(filePathAndName.substring(0, filePathAndName.lastIndexOf("/")));
            if (!file.exists()) {
                file.mkdirs();
            }
            if (!f.exists() && f.createNewFile()) {
            }
            try (OutputStreamWriter write = new OutputStreamWriter(FileUtils.openOutputStream(f), chartset); BufferedWriter writer = new BufferedWriter(write)) {
                writer.write(fileContent);

            }
            return true;
        } catch (Exception e) {
            log.error("writeFile error:", e);
        }
        return false;
    }

    public static List<String> readFile(String file) {
        try {
            List<String> list = new ArrayList<>();
            try (FileInputStream fr = FileUtils.openInputStream(FileUtils.getFile(file)); BufferedReader br = new BufferedReader(new InputStreamReader(fr, "UTF-8"))) {
                String line = br.readLine();
                while (line != null) {
                    if (!line.trim().equals("")) {
                        list.add(line);
                    }
                    line = br.readLine();
                }
            }
            return list;
        } catch (IOException e) {
            log.error("readFile error:", e);
        }
        return null;
    }

    public static String readText(String textname, boolean isbr) {
        return readText(textname, isbr, "UTF-8");
    }

    public static String readText(String textname, boolean isbr, String chartset) {
        try {
            try (FileInputStream fr = FileUtils.openInputStream(FileUtils.getFile(textname)); BufferedReader br = new BufferedReader(new InputStreamReader(fr, chartset))) {
                StringBuffer sb = new StringBuffer();
                String line = br.readLine();
                while (line != null) {
                    if (isbr) {
                        sb.append(line + "\n");
                    } else {
                        sb.append(line);
                    }
                    line = br.readLine();
                }
                return sb.toString();
            }
        } catch (IOException e) {
            log.error("readText error:", e);
        }
        return null;
    }

    public static StringBuffer readTextToBuffer(String textname, boolean isbr, String chartset) {
        try (FileInputStream fr = FileUtils.openInputStream(FileUtils.getFile(textname)); BufferedReader br = new BufferedReader(new InputStreamReader(fr, chartset))) {
            StringBuffer sb = new StringBuffer();
            String line = br.readLine();
            while (line != null) {
                if (isbr) {
                    sb.append(line + "\n");
                } else {
                    sb.append(line);
                }
                line = br.readLine();
            }
            return sb;
        } catch (IOException e) {
            log.error("readTextToBuffer error:", e);
        }
        return null;
    }

    public static String readText(String textname) {
        return readText(textname, true);
    }

    public static void writeFile(String filePathAndName, String fileContent, boolean reset) {
        try {
            String dirString = filePathAndName.substring(0, filePathAndName.lastIndexOf("/")) + "/";

            File dir = FileUtils.getFile(dirString);
            if (!dir.isDirectory() && dir.mkdirs()) {
            }
            File f = FileUtils.getFile(filePathAndName);
            if (!f.exists() && f.createNewFile()) {
            }
            try (FileWriter writer = new FileWriter(f, reset)) {
                writer.write(fileContent);
            }
        } catch (Exception e) {
            log.error("write file error!", e);
        }
    }

    /***
     * <AUTHOR>
     * @param inputStream 输入流
     * @return 返回字节流数组
     * @throws IOException
     */
    public static byte[] readInputStream(final InputStream inputStream) throws IOException {
        final byte[] buffer = new byte[1024];
        int len = 0;
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            while ((len = inputStream.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toByteArray();
        }
    }

    /**
     * @throws
     * @Title: readBytes
     * @Description: TODO(描述这个方法的作用)
     * @param: @param filePath
     * @param: @param maxReadLenth
     * @param: @return
     * @param: @throws IORuntimeException
     * @return: byte[]
     */
    public static byte[] readBytes(String filePath, Integer maxReadLenth) throws IORuntimeException {
        File file = FileUtils.getFile(filePath);
        long len = file.length();

        byte[] bytes = new byte[(int) len];
        int readLength;
        try (FileInputStream in = FileUtils.openInputStream(file)) {
            readLength = in.read(bytes);
            if (readLength < len) {
                throw new IOException(StrUtil.format("File length is [{}] but read [{}]!", len, readLength));
            }
        } catch (Exception e) {
            throw new IORuntimeException(e);
        }

        return bytes;
    }

    /***
     * <AUTHOR>
     * @param urlStr 文件地址
     * @param fileName 文件名
     * @param savePath 保存路径
     * @throws IOException 抛出异常
     */
    public static void downLoadFromUrl(final String url, final String fileName, final String savePath) throws IOException {
        final File saveDir = FileUtils.getFile(savePath);
        if (!saveDir.exists() && saveDir.mkdir()) {
        }
        HttpUtil.downloadFile(url, saveDir + File.separator + fileName);
    }

    /**
     * @param zipFile 压缩文件
     * @param descDir 目的路径
     * @throws IOException
     */
    public static void unZipFiles(final File zipFile, final String descDir) throws IOException {
        final File pathFile = FileUtils.getFile(descDir);
        if (!pathFile.exists()) {
            FileUtil.mkdir(descDir);
        }
        try (ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"))) {
            final Enumeration entries = zip.entries();
            while (entries.hasMoreElements()) {
                final ZipEntry entry = (ZipEntry) entries.nextElement();
                final String zipEntryName = entry.getName();
                final String outPath = (descDir + "/" + zipEntryName).replaceAll("\\*", "/");
                final File file = FileUtils.getFile(FilenameUtils.getFullPathNoEndSeparator(outPath));
                if (!file.exists()) {
                    file.mkdirs();
                }

                final File outFile = FileUtils.getFile(outPath);
                if (entry.isDirectory() && !outFile.exists()) {
                    outFile.mkdirs();
                }
                if (outFile.isDirectory()) {
                    continue;
                }

                try (InputStream in = zip.getInputStream(entry); OutputStream out = FileUtils.openOutputStream(outFile)) {
                    final byte[] buf1 = new byte[1024];
                    int len;
                    while ((len = in.read(buf1)) > 0) {
                        out.write(buf1, 0, len);
                    }
                }
            }
            log.info("******************解压完毕********************");
        }

    }

    /**
     * @param files       要压缩的文件列表
     * @param zipFilePath 目的路径   压缩后文件的绝对路径 (/data/xxx/xxx/xxx.zip)
     */
    public static void doZip(List<File> files, String zipFilePath) {
        try {
            File zipFile = FileUtils.getFile(zipFilePath);
            if (!zipFile.getParentFile().exists()) {
                zipFile.getParentFile().mkdirs();
            }
            if (!zipFile.exists() && zipFile.createNewFile()) {
            }
            try (ZipOutputStream zipOutputStream = new ZipOutputStream(FileUtils.openOutputStream(FileUtils.getFile(zipFile)), Charset.defaultCharset())) {
                for (File file : files) {
                    byte[] buf = new byte[2 * 1024];
                    zipOutputStream.putNextEntry(new ZipEntry(file.getName()));
                    int len;
                    try (FileInputStream in = FileUtils.openInputStream(file)) {
                        while ((len = in.read(buf)) != -1) {
                            zipOutputStream.write(buf, 0, len);
                        }
                        zipOutputStream.flush();
                        zipOutputStream.closeEntry();
                    }
                }
                log.info("========================压缩结束=====================");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * @param zipFile 解压文件
     * @param descDir 目的路径
     * @throws IOException
     */
    public static void doUnZip(final File zipFile, final String descDir) throws IOException {
        final File pathFile = FileUtils.getFile(descDir);
        if (!pathFile.exists() && pathFile.mkdirs()) {
        }
        try (ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"))) {
            final Enumeration entries = zip.entries();
            while (entries.hasMoreElements()) {
                final ZipEntry entry = (ZipEntry) entries.nextElement();
                final String zipEntryName = entry.getName();
                try (InputStream in = zip.getInputStream(entry)) {
                    final String outPath = (descDir + "/" + zipEntryName).replaceAll("\\*", "/");
                    final File file = FileUtils.getFile(outPath.substring(0, outPath.lastIndexOf(File.separator)));
                    if (!file.exists() && file.mkdirs()) {
                    }
                    if (FileUtils.getFile(outPath).isDirectory()) {
                        continue;
                    }
                    try (OutputStream out = FileUtils.openOutputStream(FileUtils.getFile(outPath))) {
                        final byte[] buf1 = new byte[1024];
                        int len;
                        while ((len = in.read(buf1)) > 0) {
                            out.write(buf1, 0, len);
                        }
                    }
                }

            }
            log.info("******************解压完毕********************");
        }

    }

    /**
     * 文件交换（文件名保持不变）
     *
     * @param aFile
     * @param bFile
     * @version v1.0
     * @date 2023年4月21日下午2:15:45
     */
    public static void fileNameExchange(String aFileName, String bFileName) {
        if (aFileName.equals(bFileName)) {
            return;
        }
        File aFile = FileUtils.getFile(aFileName);
        File aFileBak = FileUtils.getFile(aFileName + "_bak");
        if (aFile.renameTo(aFileBak)) {
            File bFile = FileUtils.getFile(bFileName);
            if (bFile.renameTo(aFile) && aFileBak.renameTo(bFile)) {
                log.debug("文件：{}和文件:{},文件名字交换成功", aFileName, bFileName);
            }

        }

    }

    /**
     * 文件重命名
     *
     * @param sourceFileName
     * @param descFileName
     * @version v1.0
     * @date 2023年4月21日下午4:03:20
     */
    public static void fileNameRename(String sourceFileName, String descFileName) {
        if (sourceFileName.equals(descFileName)) {
            return;
        }

        File sourceFile = FileUtils.getFile(sourceFileName);
        File descsourceFile = FileUtils.getFile(descFileName);
        if (!sourceFile.renameTo(descsourceFile)) {
            log.debug("文件：{},重命名失败", sourceFileName);
        }
    }

    /**
     * 获取新后缀名称
     *
     * @param filePathName
     * @return
     * @version v1.0
     * @date 2023年7月6日下午6:25:54
     */
    public static String getFilePathNameNewPrefix(String filePathName, String prefix) {
        return FilenameUtils.getFullPath(filePathName)
                + FilenameUtils.getBaseName(filePathName)
                + FilenameUtils.EXTENSION_SEPARATOR + prefix;
    }

    /**
     * @param first
     * @param more
     * @return String
     */
    public static String getPath(String first, String... more) {
        return FileSystems.getDefault().getPath(first, more).toString();
    }

    /**
     * @param response
     * @param file
     * @param fileName
     * @return
     * <AUTHOR>  2025/4/25 17:24
     * @description: 下载文件
     */
    public static void downloadFile(HttpServletResponse response, File file, String fileName) throws Exception {
        // 清空response
        response.reset();
        // 设置response的Header
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ";");
        response.addHeader("Content-Length", "" + file.length());
        response.setContentType("application/octet-stream");
        OutputStream toClient = null;
        InputStream fis = null;
        //打开文件输入流 和 servlet输出流
        try {
            toClient = new BufferedOutputStream(response.getOutputStream());
            fis = new BufferedInputStream(new FileInputStream(file));
            //通过ioutil 对接输入输出流，实现文件下载
            IOUtils.copy(fis, toClient);
            toClient.flush();
        } catch (Exception e) {
            log.error("【文件下载失败】", e);
            throw new RuntimeException("文件下载失败");
        } finally {
            //关闭流
            IOUtils.closeQuietly(fis);
            IOUtils.closeQuietly(toClient);
        }
    }

    /**
     * @param filePath 路径
     * @return 排序后的文件名【但不包含文件夹】更改时间越近越靠前
     * <AUTHOR>  2025/4/2 16:04
     * @description: 根据文件修改日期排序
     */
    public static List<File> orderByFileUpdateDate(String filePath) {
        File file = new File(filePath);// NOSONAR
        File[] files = file.listFiles();
        if (files == null || file.length() == 0) {
            return null;
        }
        List<File> fileList = new ArrayList<>();
        for (int i = 0; i < files.length; i++) {
            if (files[i].isFile()) {
                fileList.add(files[i]);
            }
        }
        Collections.sort(fileList, new Comparator<File>() {
            public int compare(File f1, File f2) {
                long diff = f1.lastModified() - f2.lastModified();
                if (diff > 0) return -1;
                if (diff == 0) return 0;
                return 1;//如果 if 中修改为 返回-1 同时此处修改为返回 1  排序就会是递减
            }
        });
        return fileList;
    }


    @SneakyThrows
    public static File saveFileToLocal(MultipartFile uploadFile) {
        if (uploadFile == null) {
            return null;
        }

        String date = DateUtil.format(new Date(), "yyyyMMdd");
        String catalogPath = NaasConfig.getProfile() + Constants.IMPORT_PATH_PREFIX + date;
        File catalog = FileUtils.getFile(catalogPath);
        // 目录不存在，则生成
        log.info("catalogPath:{}", catalogPath);
        if (!catalog.exists()) {
            log.info("mkdir: {}", catalogPath);
            catalog.mkdirs();
        }
        String filePath = catalogPath + "/" + String.format("%s_%s", IdUtils.fastSimpleUUID(), uploadFile.getOriginalFilename());
        File destFile = FileUtils.getFile(filePath);
        uploadFile.transferTo(destFile);
        return destFile;
    }

    public static AjaxResult sendFileToHttpInterface(Map<String, File> fileMap, String remoteUrl, Map<String, String> params) throws Exception {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        AjaxResult var13;
        try {
            HttpPost httpPost = new HttpPost(remoteUrl);
            MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
            entityBuilder.setCharset(Charset.forName("UTF-8"));
            for (String key : fileMap.keySet()) {
                entityBuilder.addPart(key, new FileBody((File) fileMap.get(key), ContentType.create("application/octet-stream", "UTF-8")));
            }
            for (String key : params.keySet()) {
                entityBuilder.addTextBody(key, (String) params.get(key), ContentType.create("application/x-www-form-urlencoded", "UTF-8"));
            }
            HttpEntity entity = entityBuilder.build();
            httpPost.setEntity(entity);
            log.info("开始发送文件到http接口,接口名:{},数据参数:{}，文件参数:{}", new Object[]{remoteUrl, JSON.toJSONString(params), JSON.toJSONString(fileMap.keySet())});
            HttpResponse response = httpclient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            String result = null;
            if (responseEntity != null) {
                result = EntityUtils.toString(responseEntity, "UTF-8");
            }
            log.info("发送文件到http接口结束,请求结果:{}", result);
            if (response.getStatusLine().getStatusCode() != 200) {
                AjaxResult var32 = AjaxResult.error(result);
                return var32;
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            int statusCode = jsonObject.getInteger("code");
            if (statusCode != 200) {
                var13 = AjaxResult.error(jsonObject.getString("msg"));
                return var13;
            }
            var13 = AjaxResult.success(jsonObject.get("data"));
        } catch (Exception e) {
            log.info("发送文件到http接口失败,{}", e.getMessage(), e);
            AjaxResult entityBuilder = AjaxResult.error(e.getMessage());
            return entityBuilder;
        } finally {
            try {
                httpclient.close();
            } catch (Exception e) {
                log.info("{}", e.getMessage(), e);
            }
        }
        return var13;
    }
}
