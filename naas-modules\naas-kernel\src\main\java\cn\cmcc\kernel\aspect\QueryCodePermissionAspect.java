package cn.cmcc.kernel.aspect;

import cn.cmcc.common.annotation.CheckQueryCodePerms;
import cn.cmcc.common.context.SecurityUtils;
import cn.cmcc.common.exception.QueryCodePermissionException;
import cn.cmcc.kernel.service.IQueryCodePermissionService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 通用查询编码权限验证切面
 *
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Aspect
@Component
@Order(2) // 在CheckPerms之后执行
public class QueryCodePermissionAspect {

    @Autowired
    private IQueryCodePermissionService queryCodePermissionService;

    @Before("@annotation(checkQueryCodePerms)")
    public void checkQueryCodePermission(JoinPoint joinPoint, CheckQueryCodePerms checkQueryCodePerms) {
        try {
            // 获取当前用户
            String username = SecurityUtils.getUserName();
            Long userId = SecurityUtils.getUserId();
            if (username == null) {
                throw new QueryCodePermissionException("", "", "用户未登录");
            }

            // 从方法参数中提取查询编码
            String queryCode = extractQueryCode(joinPoint, checkQueryCodePerms.codeParam());
            if (queryCode == null || queryCode.trim().isEmpty()) {
                throw new QueryCodePermissionException("", username, "查询编码参数为空");
            }

            // 执行权限验证
            boolean hasPermission = queryCodePermissionService.hasPermission(username, queryCode, userId);
            if (!hasPermission) {
                log.warn("用户 [{}] 尝试访问无权限的查询编码 [{}]", username, queryCode);
                throw new QueryCodePermissionException(queryCode, username, checkQueryCodePerms.message());
            }

            log.debug("用户 [{}] 成功通过查询编码 [{}] 权限验证", username, queryCode);

        } catch (QueryCodePermissionException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询编码权限验证过程中发生异常", e);
            throw new QueryCodePermissionException("", "", "权限验证异常: " + e.getMessage());
        }
    }

    /**
     * 从方法参数中提取查询编码
     */
    private String extractQueryCode(JoinPoint joinPoint, String codeParam) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];

            // 检查是否有@PathVariable注解且参数名匹配
            PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
            if (pathVariable != null) {
                String paramName = pathVariable.value().isEmpty() ?
                        pathVariable.name().isEmpty() ? parameter.getName() : pathVariable.name()
                        : pathVariable.value();

                if (codeParam.equals(paramName) && args[i] != null) {
                    return args[i].toString();
                }
            }
        }

        log.warn("未能从方法参数中找到查询编码参数: {}", codeParam);
        return null;
    }
}
