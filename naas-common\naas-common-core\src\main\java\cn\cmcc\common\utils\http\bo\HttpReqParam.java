/**
 *
 *
 * @Title: RequestParam.java
 * <AUTHOR>
 * @date 2022年10月12日
 * @version V1.0
 */
package cn.cmcc.common.utils.http.bo;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import cn.cmcc.common.utils.StringUtils;
import lombok.Data;

@Data
public class HttpReqParam {

	/**
	 * 请求地址
	 */
	private String url;

	/**
	 * 请求参数
	 */
	private String params;

	/**
	 * 请求方式
	 */
	private HttpMethod method;

	/**
	 * 请求头
	 */
	private HttpHeaders headers;

	/**
	 * 连接超时
	 */
	private Integer connectTimeout;

	/**
	 * 读超时
	 */
	private Integer readTimeout;

	private HttpReqParam(Builder builder) {
		this.url = builder.getUrl();
		this.params = builder.getParams();
		this.method = builder.getMethod();
		this.connectTimeout = builder.getConnectTimeout();
		this.readTimeout = builder.getReadTimeout();

		headers = new HttpHeaders();
		if(null == builder.getMediaType()) {
        	headers.setContentType(MediaType.APPLICATION_JSON);
        }else {
        	headers.setContentType(builder.getMediaType());
        }

        if(MapUtils.isNotEmpty(builder.getHeaderParam())) {
        	builder.getHeaderParam().forEach((k,v)->{
				if(StringUtils.isNotEmpty(k) && StringUtils.isNotEmpty(v)) {
					headers.add(k, v);
				}
			});
        }
	}

	public static Builder builder(String url,HttpMethod method) {
	    return new Builder(url,method);
	}

	public static Builder builder(String url,String params,HttpMethod method) {
	    return new Builder(url,params,method);
	}

	public static Builder builder(String url,String params,HttpMethod method,Integer connectTimeout,Integer readTimeout) {
		return new Builder(url,params,method);
	}

	public static Builder builder(String url,String params,HttpMethod method,Map<String, String> headerParam) {
	    return new Builder(url,params,method,headerParam);
	}

	@Data
	public static class Builder{
		private String url;
		private String params;
		private Map<String, String> headerParam;
		private HttpMethod method;
		private MediaType  mediaType;
		private Integer connectTimeout;
		private Integer readTimeout;

		public Builder(String url,HttpMethod method) {
			this.url = url;
			this.params = StringUtils.EMPTY;
			this.method = method;
			this.headerParam = new HashMap<String, String>();
		}

		public Builder(String url,String params,HttpMethod method) {
			this.url = url;
			this.params = params;
			this.method = method;
			this.headerParam = new HashMap<String, String>();
		}

		public Builder(String url,String params,HttpMethod method,Map<String, String> headerParam) {
			this.url = url;
			this.params = params;
			this.method = method;
			this.headerParam = headerParam;
		}

		public Builder params(String params) {
			this.params = params;
			return this;
		}

		public Builder headerParam(Map<String, String> headerParam) {
			this.headerParam = headerParam;
			return this;
		}

		public Builder addHeaderParam(String key,String value) {
			this.headerParam.put(key, value);
			return this;
		}

		public Builder mediaType(MediaType  mediaType) {
			this.mediaType = mediaType;
			return this;
		}

		public Builder connectTimeout(Integer connectTimeout) {
			this.connectTimeout = connectTimeout;
			return this;
		}

		public Builder readTimeout(Integer readTimeout) {
			this.readTimeout = readTimeout;
			return this;
		}

		public HttpReqParam build(){
            return new HttpReqParam(this);
        }
	}
}
