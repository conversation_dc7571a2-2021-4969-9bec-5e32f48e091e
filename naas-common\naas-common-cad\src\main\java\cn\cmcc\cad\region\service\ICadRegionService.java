package cn.cmcc.cad.region.service;

import java.util.List;

import cn.cmcc.cad.region.entity.CadRegion;
import cn.cmcc.common.web.domain.AjaxResult;

public interface ICadRegionService {

	/**
	 * 获取Cad图纸的图框数据,实现原理:<br/>
	 * 没有包含在其他矩形的矩形(少量图纸中有特大矩形的情况无法获取)
	 * @param dwgFile - 图纸路径
	 * @return
	 */
	public List<CadRegion> getCadRegions(String dwgFile);
	
	/**
	 * 根据CadRegion图框配置，生成图框图片
	 * @param region - 图框配置
	 * @param clearText - 是否清除文字
	 * @param lineStroke - 线条画笔大小，默认值为1，所有线条特殊处理 画笔大小，不按原cad图纸，主要方便后续训练时提取边界
	 * @param imageFile - 生成图片地址
	 * @return
	 */
	public AjaxResult cadRegionToImage(CadRegion region,boolean clearText,int lineStroke,String imageFile);
	
}
