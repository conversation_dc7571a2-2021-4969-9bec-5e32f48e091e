package cn.cmcc.kernel.annotation;

import java.lang.annotation.*;

/**
 * 通用查询编码权限验证注解
 * 自动从方法的 @PathVariable String code 参数中提取编码进行权限验证
 *
 * <AUTHOR> 4.0 sonnet
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CheckQueryCodePerms {

    /**
     * 编码参数名，默认为 "code"
     */
    String codeParam() default "code";

    /**
     * 是否启用权限缓存，默认启用
     */
    boolean enableCache() default true;

    /**
     * 权限验证失败时的提示信息
     */
    String message() default "无权限访问该查询编码";
}
