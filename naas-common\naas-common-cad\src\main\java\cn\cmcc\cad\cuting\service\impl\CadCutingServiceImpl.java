package cn.cmcc.cad.cuting.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.cad.constant.CadConstants;
import cn.cmcc.cad.cuting.entity.CadCutingBoundaryConfig;
import cn.cmcc.cad.cuting.entity.CadCutingConfig;
import cn.cmcc.cad.cuting.service.ICadCutingService;
import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadBlockEntity;
import cn.cmcc.cad.entity.XhxkCadImage;
import cn.cmcc.cad.entity.XhxkCadInsertObject;
import cn.cmcc.cad.entity.XhxkCadPoint;
import cn.cmcc.cad.entity.XhxkCadText;
import cn.cmcc.cad.service.ICadService;
import cn.cmcc.common.cache.context.SysConfigContext;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.core.lang.UUID;

@Service
public class CadCutingServiceImpl implements ICadCutingService {
	@Autowired
	private ICadService cadService;
	private static Logger logger = LoggerFactory.getLogger(CadCutingServiceImpl.class);
	

	public List<CadCutingConfig> getCAdAutoCutConfigs(XhxkCadImage image) {
		Map<String, CadCutingBoundaryConfig> boundaryConfig = boundaryConfig();
		List<XhxkCadBlockEntity> configBlockEntities = getCadBlockConfigList(image.deepCopy(), boundaryConfig);
		Map<String, List<XhxkCadBaseEntity>> cadBlockBaseEntityMap = getCadBlockEntities(image);
		List<CadCutingConfig> cadAutoCutConfigs = getCutBoundary(image, configBlockEntities, cadBlockBaseEntityMap);
		return cadAutoCutConfigs;
	}

	@Override
	public List<XhxkCadBlockEntity> getCadBlockConfigList(XhxkCadImage cadImage, Map<String, CadCutingBoundaryConfig> boundaryConfig) {
		long start = System.currentTimeMillis();
		List<XhxkCadBlockEntity> configBlockEntities = new LinkedList<XhxkCadBlockEntity>();
		List<XhxkCadBlockEntity> cadBlockEntities = cadImage.getBlockEntities();
		for (XhxkCadBlockEntity blockEntity : cadBlockEntities) {
			String key = blockEntity.getBlockName();
			for (String configKey : boundaryConfig.keySet()) {
				CadCutingBoundaryConfig config = boundaryConfig.get(configKey);
				if (key.contains(config.getName())) {
					blockEntity.setBoundaryConfig(config);
					configBlockEntities.add(blockEntity);
					continue;
				}
			}
		}
		long end = System.currentTimeMillis();
		logger.info("getCadBlockConfigList time:" + (end - start) + "ms");
		return configBlockEntities;
	}

	public Map<String, List<XhxkCadBaseEntity>> getCadBlockEntities(XhxkCadImage cadImage) {
		long start = System.currentTimeMillis();
		Map<String, List<XhxkCadBaseEntity>> resultMap = new HashMap<String, List<XhxkCadBaseEntity>>();
		List<XhxkCadBaseEntity> cadBaseEntities = cadImage.getEntities();
		for (XhxkCadBaseEntity baseEntity : cadBaseEntities) {
			if (baseEntity instanceof XhxkCadInsertObject) {
				XhxkCadInsertObject cadInsertObject = (XhxkCadInsertObject) baseEntity;
				String blockName = cadInsertObject.getBlock();
				if (!resultMap.containsKey(blockName)) {
					resultMap.put(blockName, new LinkedList<XhxkCadBaseEntity>());
				}
				List<XhxkCadBaseEntity> entities = resultMap.get(blockName);
				entities.add(baseEntity);
				resultMap.put(blockName, entities);
			}
		}
		long end = System.currentTimeMillis();
		logger.info("getCadBlockEntities time:" + (end - start) + "ms");
		return resultMap;
	}

	@Override
	public List<CadCutingConfig> getCutBoundary(XhxkCadImage cadImage, List<XhxkCadBlockEntity> cadBlockEntities, Map<String, List<XhxkCadBaseEntity>> cadBaseEntityMap) {
		long start = System.currentTimeMillis();
		int keyIndex = 0;   
		StringBuffer csvBox = new StringBuffer("name,type,wkt\n");
		List<CadCutingConfig> cadAutoCutConfigs = new LinkedList<CadCutingConfig>();
		for (XhxkCadBlockEntity blockEntity : cadBlockEntities) {
			CadCutingBoundaryConfig boundaryConfig = blockEntity.getBoundaryConfig();
			String key = boundaryConfig.getCode();
			long start2 = System.currentTimeMillis();
			CadCutingConfig cutConfig = new CadCutingConfig(key, blockEntity);
			long end2 = System.currentTimeMillis();

			List<XhxkCadBaseEntity> list = cadBaseEntityMap.get(blockEntity.getBlockName());
			logger.info("BlockName:\t"+blockEntity.getBlockName()+"\t"+(list==null?0:list.size()));
			if((list!=null)&&!list.isEmpty())
			for (XhxkCadBaseEntity baseEntity : list) {
				XhxkCadInsertObject cadInsertObject = (XhxkCadInsertObject) baseEntity;
				XhxkCadPoint insertionPoint = cadInsertObject.getInsertionPoint();
				double xMin = cutConfig.getXMin() * cadInsertObject.getScaleX() + insertionPoint.getX();
				double yMin = cutConfig.getYMin() * cadInsertObject.getScaleY() + insertionPoint.getY();
				double xMax = cutConfig.getXMax() * cadInsertObject.getScaleX() + insertionPoint.getX();
				double yMax = cutConfig.getYMax() * cadInsertObject.getScaleY() + insertionPoint.getY();

				JSONObject keysJsonObject = boundaryConfig.getKeys();
				String type = null;
				String typeValue = null;
				logger.info("getFullText:\t"+cadInsertObject.getFullText());
				List<XhxkCadBaseEntity> cadBaseEntities = cadImage.getEntities();
				//云南图纸大量系统图、平面图的关键字不在图框属性内，只能读取整个图框区域文本
				List<XhxkCadBaseEntity> cadTexts = cadBaseEntities.stream().filter(rec -> {
					if(rec.getPosition()==null) {
						return false;
					}
					double x = rec.getPosition().getX();
					double y = rec.getPosition().getY();
					if((rec.getText()!=null)&&rec.getText().contains("平面图")) {
						System.out.println(rec.getText());
						System.out.println((int)x+"\t"+(int)xMin+"\t"+(int)xMax+"\t"+(int)insertionPoint.getX());
						System.out.println((int)y+"\t"+(int)yMin+"\t"+(int)yMax+"\t"+(int)insertionPoint.getY());
					}
					return (rec instanceof XhxkCadText)&&(x >= xMin) && (x <= xMax) && (y >= yMin) && (y <= yMax);
				}).collect(Collectors.toList());
				
				// 判断图框类型
				String title= null;
				for (String configKey : keysJsonObject.keySet()) {
					if (cadInsertObject.getFullText().contains(configKey)) {
						typeValue = configKey;
						type = keysJsonObject.getString(configKey);
						title= cadInsertObject.getRegText(typeValue);
						break;
					}
				}
				if (StringUtils.isBlank(type)) {
					for (String configKey : keysJsonObject.keySet()) {
						for(XhxkCadBaseEntity entity:cadTexts) {
							if((entity instanceof XhxkCadText)&&(((XhxkCadText)entity).getText().contains(configKey))){
								typeValue = configKey;
								type = keysJsonObject.getString(configKey);
								title = ((XhxkCadText)entity).getText();
								break;
							}
						}
						if (cadInsertObject.getFullText().contains(configKey)) {
							typeValue = configKey;
							type = keysJsonObject.getString(configKey);
							break;
						}
					}
				}
				
				if (StringUtils.isNotBlank(type)) {
					String configId = key + "_" + keyIndex++;
					CadCutingConfig resultCAdAutoCutConfig = new CadCutingConfig(key, xMin, yMin, xMax, yMax);
					resultCAdAutoCutConfig.setId(configId);

					// 返回的器件数据，处理过减去最小值的逻辑，需要恢复
					List<XhxkCadBaseEntity> cadDevices = cadBaseEntities.stream().filter(rec -> {
						if(rec.getPosition()==null) {
							return false;
						}
						XhxkCadPoint point = rec.getPosition();//ant-
						return rec.isDevice()&&StringUtils.isNotBlank(rec.getDeviceCode())
								&&rec.getDeviceCode().startsWith("ant-")
								&&(point.getX() >= xMin) && (point.getX() <= xMax) 
								&& (point.getY() >= yMin) && (point.getY() <= yMax);
					}).collect(Collectors.toList());
					if ((cadDevices != null) && !cadDevices.isEmpty()) {// 表明图框范围内有器件
						int rectangleSize = cadDevices.size();
						resultCAdAutoCutConfig.setCadDevices(cadDevices);
						resultCAdAutoCutConfig.setRectangleSize(rectangleSize);

						List<XhxkCadBaseEntity> boxBaseEntities = cadBaseEntities.stream().filter(rec -> {
							if(rec.getPosition()==null) {
								return false;
							}
							double x = rec.getPosition().getX();
							double y = rec.getPosition().getY();
							return (x >= xMin) && (x <= xMax) && (y >= yMin) && (y <= yMax);
						}).collect(Collectors.toList());

						resultCAdAutoCutConfig.setCadBaseEntities(boxBaseEntities);
						resultCAdAutoCutConfig.setType(type);
						resultCAdAutoCutConfig.setTypeValue(typeValue);
						resultCAdAutoCutConfig.update();
						resultCAdAutoCutConfig.setTitle(title);
						if (("system".equals(type) || ("plan".equals(type)) && StringUtils.isNotBlank(resultCAdAutoCutConfig.getTitle()))) {
							cadAutoCutConfigs.add(resultCAdAutoCutConfig);
						}
						keyIndex++;
					}
				}
			}

		}
		long end = System.currentTimeMillis();
		logger.info("getCutBoundary time:" + (end - start) + "ms\tsize:" + cadAutoCutConfigs.size());
		return cadAutoCutConfigs;
	}

	@Override
	public List<CadCutingConfig> cutDwgByBoundary(XhxkCadImage image, List<CadCutingConfig> cadAutoCutConfigs) {
		if ((cadAutoCutConfigs != null) && !cadAutoCutConfigs.isEmpty()) {
			int poolSize = cadAutoCutConfigs.size() > 3 ? 3 : cadAutoCutConfigs.size();
			poolSize = 1;
			ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
			List<Future<AjaxResult>> futureList = new ArrayList<Future<AjaxResult>>();
			List<AjaxResult> results = new LinkedList<AjaxResult>();
			List<CadCutingConfig> systemConfigs = cadAutoCutConfigs.stream().filter(config -> {
				return config.getType().equals("system");
			}).collect(Collectors.toList());

			List<CadCutingConfig> planConfigs = cadAutoCutConfigs.stream().filter(config -> {
				return config.getType().equals("plan");
			}).collect(Collectors.toList());
			if ((planConfigs != null) && !planConfigs.isEmpty()) {
				for (CadCutingConfig config : planConfigs) {
					Future<AjaxResult> future = executorService.submit(new Callable<AjaxResult>() {
						@Override
						public AjaxResult call() {
							List<CadCutingConfig> configs = new LinkedList<CadCutingConfig>();
							configs.add(config);
							AjaxResult ajaxResult =  cutDwgByBoundaryItem(image, null, "plan", configs);
							configs = null;
							return ajaxResult;
						}
					});
					futureList.add(future);
				}
			}

			List<CadCutingConfig> resultAutoCutConfigs = new LinkedList<CadCutingConfig>();
			for (Future<AjaxResult> f : futureList) {
				try {
					AjaxResult ajaxResult = f.get();
					if (ajaxResult.isSuccess()) {
						resultAutoCutConfigs.add((CadCutingConfig) ajaxResult.getExtend());
					}
				} catch (InterruptedException e) {
					Thread.currentThread().interrupt();
					logger.error(e.getMessage(),e);
				} catch (Exception e) {
					logger.error(e.getMessage(),e);
				}
			}
			executorService.shutdown();
			return resultAutoCutConfigs;
		} else {
			return null;
		}
	}

	@Override
	public AjaxResult cutDwgByBoundaryItem(XhxkCadImage image, List<XhxkCadBaseEntity> cadBaseEntities, String type, List<CadCutingConfig> cutConfigs) {
		long startAll = System.currentTimeMillis();
		AjaxResult ajaxResult = null;
		XhxkCadImage imageModel = null;
		try {
			long start = System.currentTimeMillis();
			CadCutingConfig config = cutConfigs.get(0);
			imageModel = cutCadByBox(image, cutConfigs);
			
			String id = UUID.randomUUID().toString(true);
			String xhxkFile = imageModel.getFile().replace(".dwg", "_" + type + "_" + id + CadConstants.CAD_SUFFIX);
			long end = System.currentTimeMillis();
			logger.info("getBoundaryObject time:" + (end - start) + "ms\t"+xhxkFile);
			logger.info("imageModel getBlockEntities: " + imageModel.getBlockEntities().size() + "\tCadEntities: "+image.getEntities().size()+ "\tgetEntities: "+imageModel.getEntities().size());

			start = System.currentTimeMillis();
			cadService.saveCadImageToXhxk(imageModel, xhxkFile);
			config.setXhxkFile(xhxkFile);
			config.setCadBaseEntities(null);
			config.setCadDevices(null);
			config.setEntities(null);
			ajaxResult = AjaxResult.success("切割成功!", xhxkFile, config);
			end = System.currentTimeMillis();
			logger.info("dwgToXhxkImage time:" + (end - start) + "ms\t");

		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			ajaxResult = AjaxResult.error("切割异常!", e.getMessage());
		}finally {
			imageModel=null;
			cutConfigs=null;
			System.gc();
		}
		long endAll = System.currentTimeMillis();
		logger.info("cutDwgByBoundary time:" + (endAll - startAll) + "ms");
		logger.info("\n\n\n");
		return ajaxResult;
	}

	private XhxkCadImage cutCadByBox(XhxkCadImage image, List<CadCutingConfig> cutConfigs) {
		logger.info("cutCadByBox start... ");
		for (CadCutingConfig config : cutConfigs) {
			logger.info("cutCadByBox CAdAutoCutConfig ["+config.getXMin()+" "+config.getXMax()+"] ["+config.getYMin()+" "+config.getYMax()+"]");
		}
		List<XhxkCadBaseEntity> cadBaseEntities = image.getEntities();
		long start = System.currentTimeMillis();
		List<XhxkCadBaseEntity> boxBaseEntities = cadBaseEntities.stream().filter(rec -> {
			if(rec.getPosition()==null) {
				return false;
			}
			double x = rec.getPosition().getX();
			double y = rec.getPosition().getY();
			boolean status = false;
			for (CadCutingConfig config : cutConfigs) {
				double xMin = config.getXMin();
				double yMin = config.getYMin();
				double xMax = config.getXMax();
				double yMax = config.getYMax();
				//边界收缩 上、左、右收缩4%，下收缩20%
				xMin = xMin + (xMax-xMin)*(4.0 / 100.0);
				xMax = xMax - (xMax-xMin)*(4.0 / 100.0);
				yMin = yMin + (yMax-yMin)*(20.0 / 100.0);
				yMax = yMax - (yMax-yMin)*(4.0 / 100.0);
				if (
					(x >= xMin) && (x <= xMax) && 
				    (y >= yMin) && (y <= yMax)
				   ) {
					status = true;
					break;
				}
			}
			return status;
		}).collect(Collectors.toList());
		XhxkCadImage boxCadImage = image.deepCopy();
		boxCadImage.setEntities(boxBaseEntities);
		long end = System.currentTimeMillis();
		logger.info("cutCadByBox done time:" + (end - start) + "ms\tsize:" + boxBaseEntities.size());
		//cadBaseEntities.clear();
		cadBaseEntities=null;
		//boxBaseEntities.clear();
		boxBaseEntities=null;
		return boxCadImage;
	}

	@Override
	public Map<String, CadCutingBoundaryConfig> boundaryConfig() {
		String INDOOR_Boundary_Config = SysConfigContext.getConfigValue("INDOOR_Boundary_Config");
		JSONArray nameKeyArray = JSONArray.parseArray(INDOOR_Boundary_Config);
		Map<String, CadCutingBoundaryConfig> boundaryConfigMap = new HashMap<String, CadCutingBoundaryConfig>();
		for (Object object : nameKeyArray) {
			JSONObject jsonObject = (JSONObject) object;
			boundaryConfigMap.put(jsonObject.getString("id"), new CadCutingBoundaryConfig(jsonObject.getString("code"), jsonObject.getString("name"), jsonObject.getString("id"), jsonObject.getJSONObject("keys")));
		}
		return boundaryConfigMap;
	}

}
