package cn.cmcc.auth.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2023-10-23 02:23:00
 */

@Slf4j
public class JwtUtil {

	 /**
     * 获取token中的信息无需secret解密也能获取
     *
     * @param token 密钥
     * @return  token中包含的用户名
     */
    public static String  getJwtString(String token,String key){
        try {
            DecodedJWT jwt= JWT.decode(token);
            return  jwt.getClaim(key).asString();
        }catch (JWTDecodeException ex){
            return null;
        }
    }
 
    public static void main(String[] args) {
        String token = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        log.info("====:" + JwtUtil.getJwtString(token,"sub"));
    }
}
