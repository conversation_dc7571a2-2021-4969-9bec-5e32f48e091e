/**
 * @Title: IntervalUtil.java
 * @Package cn.cmcc.common.utils
 * 
 * <AUTHOR>
 * @date 2022年6月6日
 * @version V1.0
 */
package cn.cmcc.common.utils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

public class IntervalUtil {
	
	// 连接符 OR  
	public static final String CONNECTOR_OR = "||";
	
	// 连接符 AND  
	public static final String CONNECTOR_AND = " && ";
	
	// 校验值占位符
	public static final String PLACEHOLDER= "#d#";
	
	public static ScriptEngine jse = new ScriptEngineManager().getEngineByName("JavaScript");
	
	/**
	 * 判断dataValue是否在interval区间范围内
	 * 
	 * @param dataValue 数值类型的
	 * @param formula 表达式  (#d# >= 7.0 && #d# <= 8.0)
	 * @return true：表示dataValue在区间interval范围内，false：表示dataValue不在区间interval范围内
	 */
	public static boolean isInTheInterval(String dataValue, String formula) {
		try {
			formula = formula.replaceAll(PLACEHOLDER, dataValue);
			System.out.println(formula);
			// 计算表达式
			return (Boolean) jse.eval(formula);
		} catch (Exception t) {
			return false;
		}
	}

	/**
	 * 将所有阀值区间转化为公式：如 
	 * [75,80) => #d# < 80 && #d# >= 75
	 * (125%,135%)U(70%,80%) =》 (#d# < 1.35 && #d# > 1.25) || (#d# < 0.8 && #d# > 0.7)
	 * 
	 * @param interval   形式如：(125%,135%)U(70%,80%)
	 */
	public static String getFormulaByAllInterval(String interval) {
		StringBuffer buff = new StringBuffer();
		for (String limit : interval.split("U")) {// 如：（125%,135%）U (70%,80%)
			buff.append("(").append(getFormulaByInterval(limit, CONNECTOR_AND)).append(")").append(CONNECTOR_OR);
		}
		String allLimitInvel = buff.toString();
		int index = allLimitInvel.lastIndexOf(CONNECTOR_OR);
		allLimitInvel = allLimitInvel.substring(0, index);
		return allLimitInvel;
	}

	/**
	 * 将整个阀值区间转化为公式：如 145) =》 #d# < 145 [75,80) =》 #d# < 80 &&
	 * 
	 * @param interval   形式如：145)、[75,80)
	 * @param connector  连接符 如：&&
	 */
	public static String getFormulaByInterval(String interval, String connector) {
		StringBuffer buff = new StringBuffer();
		for (String halfInterval : interval.split(",")) {// 如：[75,80)、≥80
			buff.append(getFormulaByHalfInterval(halfInterval)).append(connector);
		}
		String limitInvel = buff.toString();
		int index = limitInvel.lastIndexOf(connector);
		limitInvel = limitInvel.substring(0, index);
		return limitInvel;
	}

	/**
	 * 将半个阀值区间转化为公式：如 145) =》 dataValue < 145 ≥80% =》 dataValue >= 0.8 [130 =》
	 * dataValue >= 130 <80% =》 dataValue < 0.8
	 * 
	 * @param halfInterval 形式如：145)、≥80%、[130、<80%
	 * @return dataValue < 145
	 */
	public static String getFormulaByHalfInterval(String halfInterval) {
		halfInterval = halfInterval.trim();
		if (halfInterval.contains("∞")) {// 包含无穷大则不需要公式
			return "1 == 1";
		}
		StringBuffer formula = new StringBuffer();
		String data = "";
		String opera = "";
		if (halfInterval.matches("^([<>≤≥\\[\\(]{1}(-?\\d+.?\\d*\\%?))$")) {// 表示判断方向（如>）在前面 如：≥80%
			opera = halfInterval.substring(0, 1);
			data = halfInterval.substring(1);
		} else {// [130、145)
			opera = halfInterval.substring(halfInterval.length() - 1);
			data = halfInterval.substring(0, halfInterval.length() - 1);
		}
		double value = dealPercent(data);
		formula.append(PLACEHOLDER).append(" ").append(opera).append(" ").append(value);
		String a = formula.toString();
		// 转化特定字符
		return a.replace("[", ">=").replace("(", ">").replace("]", "<=").replace(")", "<").replace("≤", "<=")
				.replace("≥", ">=");
	}

	/**
	 * 去除百分号，转为小数
	 * 
	 * @param str 可能含百分号的数字
	 * @return
	 */
	public static double dealPercent(String str) {
		double d = 0.0;
		if (str.contains("%")) {
			str = str.substring(0, str.length() - 1);
			d = Double.parseDouble(str) / 100;
		} else {
			d = Double.parseDouble(str);
		}
		return d;
	}

	public static void main(String[] args) {
		System.out.println(IntervalUtil.isInTheInterval("7", IntervalUtil.getFormulaByAllInterval("[7,8]U(5,8)")));
	}
}
