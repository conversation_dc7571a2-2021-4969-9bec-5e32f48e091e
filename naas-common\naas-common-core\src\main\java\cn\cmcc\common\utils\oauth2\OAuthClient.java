package cn.cmcc.common.utils.oauth2;


import java.io.IOException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.http.HttpClientUtils;
import cn.cmcc.common.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;

/**  
 * oauth2 认证客户端
 * 
 * <AUTHOR>
 * @date 2024-04-18 10:42:40
*/

@Slf4j
public class OAuthClient {

	  public static String nKOauth2Code = null;
	  public static Map<String, String> tokenMap = new HashMap<>();
	  public static Map<String, Long> tokenExpiresMap = new HashMap<>();
	  public static String ENCODING_UTF8 = "UTF-8";
	  public static String CONTENT_TYPE_JSON = "application/json;charset=UTF-8";
	  
	  public static String getAccessTokenByOauth2(OAuth2Properties oAuth2Properties)throws Exception
	  {
	    return getAccessTokenByOauth2(oAuth2Properties,false);
	  }
	  
	  /**
	   * 获取 ACCESS_TOKEN
	   * 
	   * @param appId
	   * @param isForceNew
	   * @return
	   * @throws IOException 
	   * String
	   */
	  public static String getAccessTokenByOauth2(OAuth2Properties oAuth2Properties, boolean isForceNew) throws Exception
	  {
	    if ((!isForceNew) && (tokenMap.containsKey(oAuth2Properties.getAppId())) 
	    		&& (((String)tokenMap.get(oAuth2Properties.getAppId())).length() > 0) 
	    		&& (tokenExpiresMap.containsKey(oAuth2Properties.getAppId())) 
	    		&& (System.currentTimeMillis() - ((Long)tokenExpiresMap.get(oAuth2Properties.getAppId())).longValue() < 82800000L)) {
	      return (String)tokenMap.get(oAuth2Properties.getAppId());
	    }

	    String codeUrl = getAuthorizeCodeUrl(oAuth2Properties.getUrl(),oAuth2Properties.getRedirectUri(), 
	    		oAuth2Properties.getAppId(), oAuth2Properties.getPublicKey());
	    
	    log.info("codeUrl:" + codeUrl);
	    String codeRedirectUrl = HttpClientUtils.getRedirectUrl(codeUrl);
	    log.info("codeRedirectUrl:" + codeRedirectUrl);
	    String queryUrl = new URL(codeRedirectUrl).getQuery();
	    
	    Map<String, String> codeResult = StringUtils.parseQueryString(queryUrl);
	    String code = (String)codeResult.get("code");
	    if (StringUtils.isEmpty(code)){
	      return null;
	    }
	    
	    log.info("code:" + code);
	    String tokenUrl = getAccessTokenUrl(oAuth2Properties.getUrl(),oAuth2Properties.getAppId(), code, 
	    		oAuth2Properties.getAppSecret(), oAuth2Properties.getRedirectUri());

	    JSONObject reqObject = new JSONObject();
	    AjaxResult tokenResultAjr = HttpClientUtils.sendPost(tokenUrl, reqObject.toJSONString());
	    if(!tokenResultAjr.isSuccess()) {
	    	return null;
	    }
	    JSONObject jsonObject = JSONObject.parseObject(tokenResultAjr.getData().toString());
	    String res_code = jsonObject.getString("res_code");
	    String res_message = jsonObject.getString("res_message");
	    if (("13006".equalsIgnoreCase(res_code)) || ((res_message != null) 
	    		&& (res_message.indexOf("authorize_code") != -1))){
	    	nKOauth2Code = null;
	    	return null;
	    }
	    String accessToken = jsonObject.getString("access_token");
	    if (StringUtils.isEmpty(accessToken)) {
	      return null;
	    }
	    tokenMap.put(oAuth2Properties.getAppId(), accessToken);
	    tokenExpiresMap.put(oAuth2Properties.getAppId(), Long.valueOf(System.currentTimeMillis()));
	    return accessToken;
	  }
	  
	  private static String getAuthorizeCodeUrl(String url,String redirectUri, String appId, String publicKey)
	  {
		  return url + "/api/oauth2/authorize?app_id=" + appId + "&response_type=code&redirect_uri=" + redirectUri;
	  }
	  
	  private static String getAccessTokenUrl(String url,String appId, String authorizeCode, String appSecret, String accessTokenRedirectUri)
	  {
		  return url + "/api/oauth2/access_token?app_id=" + appId + "&app_secret=" + appSecret + "&grant_type=authorization_code&redirect_uri=" + accessTokenRedirectUri + "&code=" + authorizeCode;
	  }
}
