package cn.cmcc.common.exception;

import lombok.Getter;

/**
 * 通用查询编码权限异常
 *
 * <AUTHOR> 4.0 sonnet
 */
@Getter
public class QueryCodePermissionException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private String code;
    private String username;

    public QueryCodePermissionException(String code, String username) {
        super(String.format("用户 [%s] 无权限访问查询编码 [%s]", username, code));
        this.code = code;
        this.username = username;
    }

    public QueryCodePermissionException(String code, String username, String message) {
        super(message);
        this.code = code;
        this.username = username;
    }

}
