package cn.cmcc.common.utils.bean;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Converter;
import cn.hutool.core.lang.SimpleCache;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.cglib.beans.BeanCopier;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 *
 * @version v1.0
 * <AUTHOR>
 * @date 2023年3月23日下午5:05:03
 */
public class BeanCopyUtils {


    /**
     * 单对象基于对象创建拷贝
     *
     * @param source 数据来源实体
     * @param desc   转换后的对象
     * @return desc
     */
    public static <T, V> V copy(T source, V desc) {
        if (ObjectUtil.isNull(source)) {
            return null;
        }
        if (ObjectUtil.isNull(desc)) {
            return null;
        }
        BeanCopier beanCopier = BeanCopierCache.INSTANCE.get(source.getClass(), desc.getClass(), null);
        beanCopier.copy(source, desc, null);
        return desc;
    }


    /**
     * 单对象基于class创建拷贝
     *
     * @param source 数据来源实体
     * @param desc   描述对象 转换后的对象
     * @return desc
     */
    public static <T, V> V copy(T source, Class<V> desc) {
        if (ObjectUtil.isNull(source)) {
            return null;
        }
        if (ObjectUtil.isNull(desc)) {
            return null;
        }
        final V target = ReflectUtil.newInstanceIfPossible(desc);
        return copy(source, target);
    }

    /**
     * 列表对象基于class创建拷贝
     *
     * @param sourceList 数据来源实体列表
     * @param desc       描述对象 转换后的对象
     * @return desc
     */
    public static <T, V> List<V> copyList(List<T> sourceList, Class<V> desc) {
        if (ObjectUtil.isNull(sourceList)) {
            return new ArrayList<>();
        }
        if (CollUtil.isEmpty(sourceList)) {
            return CollUtil.newArrayList();
        }
        return sourceList.stream()
                .map(source -> {
                    V target = ReflectUtil.newInstanceIfPossible(desc);
                    copy(source, target);
                    return target;
                }).collect(Collectors.toList());
    }


    public enum BeanCopierCache {

        INSTANCE;

        private final SimpleCache<String, BeanCopier> cache = new SimpleCache<>();

        public BeanCopier get(Class<?> sourceClass, Class<?> targetClass, Converter converter) {
            final String key = genKey(sourceClass, targetClass, converter);
            return cache.get(key, () -> BeanCopier.create(sourceClass, targetClass, converter != null));
        }


        private String genKey(Class<?> sourceClass, Class<?> targetClass, Converter converter) {
            StringBuilder key = StrUtil.builder()
                    .append(sourceClass.getName())
                    .append('#')
                    .append(targetClass.getName());
            Optional.ofNullable(converter)
                    .ifPresent(c -> key.append(c.getClass().getName()));
            return key.toString();
        }
    }
}
