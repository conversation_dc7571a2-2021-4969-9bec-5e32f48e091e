package cn.cmcc.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * URL有效性校验工具类
 * 用于校验URL地址是否可访问和文件是否可下载
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
public class UrlValidationUtils {
    
    /**
     * 默认连接超时时间（毫秒）
     */
    private static final int DEFAULT_CONNECT_TIMEOUT = 10000;
    
    /**
     * 默认读取超时时间（毫秒）
     */
    private static final int DEFAULT_READ_TIMEOUT = 15000;
    
    /**
     * 异步校验超时时间（秒）
     */
    private static final int ASYNC_TIMEOUT_SECONDS = 30;
    
    /**
     * 校验URL是否有效（同步方式）
     * 
     * @param urlString URL地址
     * @return 校验结果
     */
    public static UrlValidationResult validateUrl(String urlString) {
        return validateUrl(urlString, DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }
    
    /**
     * 校验URL是否有效（同步方式，自定义超时时间）
     * 
     * @param urlString URL地址
     * @param connectTimeout 连接超时时间（毫秒）
     * @param readTimeout 读取超时时间（毫秒）
     * @return 校验结果
     */
    public static UrlValidationResult validateUrl(String urlString, int connectTimeout, int readTimeout) {
        if (StringUtils.isBlank(urlString)) {
            return UrlValidationResult.invalid("URL地址为空");
        }
        
        try {
            URL url = new URL(urlString.trim());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法为HEAD，只获取响应头，不下载文件内容
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(connectTimeout);
            connection.setReadTimeout(readTimeout);
            
            // 设置User-Agent，避免某些服务器拒绝请求
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // 不自动重定向，手动处理
            connection.setInstanceFollowRedirects(false);
            
            int responseCode = connection.getResponseCode();
            
            // 处理重定向
            if (responseCode >= 300 && responseCode < 400) {
                String redirectUrl = connection.getHeaderField("Location");
                connection.disconnect();
                
                if (StringUtils.isNotBlank(redirectUrl)) {
                    log.debug("URL重定向: {} -> {}", urlString, redirectUrl);
                    return validateUrl(redirectUrl, connectTimeout, readTimeout);
                } else {
                    return UrlValidationResult.invalid("重定向地址为空");
                }
            }
            
            // 检查响应码
            if (responseCode >= 200 && responseCode < 300) {
                long contentLength = connection.getContentLengthLong();
                String contentType = connection.getContentType();
                
                connection.disconnect();
                
                return UrlValidationResult.valid(contentLength, contentType);
            } else {
                connection.disconnect();
                return UrlValidationResult.invalid("HTTP响应码: " + responseCode);
            }
            
        } catch (MalformedURLException e) {
            log.warn("URL格式错误: {}", urlString, e);
            return UrlValidationResult.invalid("URL格式错误: " + e.getMessage());
        } catch (IOException e) {
            log.warn("URL连接失败: {}", urlString, e);
            return UrlValidationResult.invalid("连接失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("URL校验异常: {}", urlString, e);
            return UrlValidationResult.invalid("校验异常: " + e.getMessage());
        }
    }
    
    /**
     * 异步校验URL是否有效
     * 
     * @param urlString URL地址
     * @return CompletableFuture包装的校验结果
     */
    public static CompletableFuture<UrlValidationResult> validateUrlAsync(String urlString) {
        return CompletableFuture
            .supplyAsync(() -> validateUrl(urlString))
            .orTimeout(ASYNC_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .exceptionally(throwable -> {
                log.error("异步URL校验失败: {}", urlString, throwable);
                return UrlValidationResult.invalid("异步校验超时或异常: " + throwable.getMessage());
            });
    }
    
    /**
     * 批量校验URL（并行处理）
     * 
     * @param urls URL列表
     * @return 校验结果数组，顺序与输入URL顺序一致
     */
    public static UrlValidationResult[] validateUrlsBatch(String... urls) {
        if (urls == null || urls.length == 0) {
            return new UrlValidationResult[0];
        }
        
        CompletableFuture<UrlValidationResult>[] futures = new CompletableFuture[urls.length];
        
        for (int i = 0; i < urls.length; i++) {
            futures[i] = validateUrlAsync(urls[i]);
        }
        
        UrlValidationResult[] results = new UrlValidationResult[urls.length];
        for (int i = 0; i < futures.length; i++) {
            try {
                results[i] = futures[i].get(ASYNC_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("批量URL校验失败: {}", urls[i], e);
                results[i] = UrlValidationResult.invalid("批量校验异常: " + e.getMessage());
            }
        }
        
        return results;
    }
    
    /**
     * URL校验结果类
     */
    public static class UrlValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final long contentLength;
        private final String contentType;
        
        private UrlValidationResult(boolean valid, String errorMessage, long contentLength, String contentType) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.contentLength = contentLength;
            this.contentType = contentType;
        }
        
        public static UrlValidationResult valid(long contentLength, String contentType) {
            return new UrlValidationResult(true, null, contentLength, contentType);
        }
        
        public static UrlValidationResult invalid(String errorMessage) {
            return new UrlValidationResult(false, errorMessage, -1, null);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public long getContentLength() {
            return contentLength;
        }
        
        public String getContentType() {
            return contentType;
        }
        
        @Override
        public String toString() {
            if (valid) {
                return String.format("Valid[contentLength=%d, contentType=%s]", contentLength, contentType);
            } else {
                return String.format("Invalid[error=%s]", errorMessage);
            }
        }
    }
}
