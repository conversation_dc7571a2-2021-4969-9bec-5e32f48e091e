package cn.cmcc.common.utils.thread;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

import org.slf4j.MDC;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.utils.uuid.IdUtils;

/**
 * 线程池链路MDC
 *
 * <AUTHOR>
 */
public final class ThreadMdcUtil {

	public static void setTraceIdIfAbsent() {
		if (MDC.get(Constants.TRACE_ID) == null) {
			MDC.put(Constants.TRACE_ID, IdUtils.fastSimpleUUID());
		}
	}

	/**
	 * 用于父线程向线程池中提交任务时，将自身MDC中的数据复制给子线程
	 *
	 * @param callable
	 * @param context
	 * @param <T>
	 * @return
	 */
	public static <T> Callable<T> wrap(final Callable<T> callable, final Map<String, String> context) {
		return () -> {
			if (context == null) {
				MDC.clear();
			} else {
				MDC.setContextMap(context);
			}
			setTraceIdIfAbsent();
			try {
				return callable.call();
			} finally {
				MDC.clear();
			}
		};
	}

	/**
	 * 用于父线程向线程池中提交任务时，将自身MDC中的数据复制给子线程
	 *
	 * @param runnable
	 * @param context
	 * @return
	 */
	public static Runnable wrap(final Runnable runnable, final Map<String, String> context) {
		return () -> {
			if (context == null) {
				MDC.clear();
			} else {
				MDC.setContextMap(context);
			}
			setTraceIdIfAbsent();
			try {
				runnable.run();
			} finally {
				MDC.clear();
			}
		};
	}
	
	/**
	 * 用于父线程向线程池中提交任务时，将自身MDC中的数据复制给子线程
	 *
	 * @param callable
	 * @param <T>
	 * @return
	 */
	public static <T> Callable<T> wrap(final Callable<T> callable){
		return wrap(callable,MDC.getCopyOfContextMap());
	}
	
	/**
	 * 用于父线程向线程池中提交任务时，将自身MDC中的数据复制给子线程
	 *
	 * @param runnable
	 * @return
	 */
	public static Runnable wrap(final Runnable runnable) {
		return wrap(runnable,MDC.getCopyOfContextMap());
	}
	
	/**
	 * 用于父线程向线程池中提交任务时，将自身MDC中的数据复制给子线程
	 *
	 * @param supplier
	 * @return
	 */
	public static <U> Supplier<U> wrapSupplier(Supplier<U> supplier) {
	    Map<String, String> mdc = MDC.getCopyOfContextMap();
	    return (Supplier<U>) () -> {
	    	if (mdc == null) {
				MDC.clear();
			} else {
				MDC.setContextMap(mdc);
			}
			setTraceIdIfAbsent();
	        return supplier.get();
	    };
	}
}
