/**
 * @Title: RedisCaffeineCacheManager.java
 * @Package cn.cmcc.common.core.cache.support
 *
 * <AUTHOR>
 * @date 2021年9月2日
 * @version V1.0
 */
package cn.cmcc.common.cache.support;

import cn.cmcc.common.cache.config.GenericFastJsonRedisSerializer;
import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.cache.enums.CachesEnums;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class RedisCaffeineCacheManager implements CacheManager{
	/**
	 *缓存
	 */
	private ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<String, Cache>();

	/**
	 * redis
	 */
	private RedisTemplate<Object, Object> redisTemplate;

	/**
     * 是否允许动态创建缓存，默认是true
     */
    private boolean dynamic = true;

    /**
     * 缓存值是否允许为NULL
     */
    private boolean allowNullValues = false;

	public RedisCaffeineCacheManager(CachesEnums[] cachesEnums ,RedisTemplate<Object, Object> redisTemplate) {
		this.redisTemplate =  redisTemplate;
		for (CachesEnums cacheType : cachesEnums) {
			 this.cacheMap.put(cacheType.name(), createCache(cacheType));
		 }
	}

	public RedisCaffeineCacheManager(CachesEnums[] cachesEnums ,RedisTemplate<Object, Object> redisTemplate,boolean dynamic,boolean allowNullValues) {
		this.redisTemplate =  redisTemplate;
		this.allowNullValues = allowNullValues;
		this.dynamic = dynamic;
		for (CachesEnums cacheType : cachesEnums) {
			 this.cacheMap.put(cacheType.name(), createCache(cacheType));
		 }
	}

	public void add(RedisCaffeineCache redisCaffeineCache) {
		if(redisCaffeineCache != null) this.cacheMap.put(redisCaffeineCache.getName(), redisCaffeineCache);
	}

	@Override
	public Cache getCache(String name) {
		Cache cache = cacheMap.get(name);
		if (cache == null && this.dynamic) {
            synchronized (this.cacheMap) {
                cache = this.cacheMap.get(name);
                if (cache == null) {
                    cache = createCache(name);
                    this.cacheMap.put(name, cache);
                }
            }
        }
		return cache;
	}

	@Override
	public Collection<String> getCacheNames() {
		return Collections.unmodifiableSet(this.cacheMap.keySet());
	}

    protected Cache createCache(String name) {
		return new RedisCaffeineCache(name, redisTemplate,caffeineCache(), false ,allowNullValues);
    }

    protected Cache createCache(CachesEnums cacheType) {
		return new RedisCaffeineCache(cacheType.name(), redisTemplate,caffeineCache(cacheType),cacheType.isUseCaffeineCache(),allowNullValues);
    }

	/**
	 *
	 * @Title: caffeineCache
	 * @Description: 使用默认值创建
	 * @param: @return
	 * @return: com.github.benmanes.caffeine.cache.Cache<Object, Object>
	 * @throws
	 */
	protected com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache() {
		Caffeine<Object, Object> cacheBuilder = Caffeine.newBuilder();
		cacheBuilder.expireAfterWrite(CacheConstants.EXPIRE_AFTER_WRITE,TimeUnit.SECONDS);
		cacheBuilder.initialCapacity(CacheConstants.INITIAL_CAPACTITY);
		cacheBuilder.maximumSize(CacheConstants.MAX_MUM_SIZE);
		return cacheBuilder.build();
    }

	/**
	 *
	 * @Title: caffeineCache
	 * @Description: 使用默认值创建
	 * @param: @return
	 * @return: com.github.benmanes.caffeine.cache.Cache<Object, Object>
	 * @throws
	 */
	protected com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache(CachesEnums cacheType) {
		Caffeine<Object, Object> cacheBuilder = Caffeine.newBuilder();
		cacheBuilder.expireAfterWrite(cacheType.getExpires(),cacheType.getTimeUnit());
		cacheBuilder.initialCapacity(CacheConstants.INITIAL_CAPACTITY);
		cacheBuilder.maximumSize(cacheType.getMaxSize());
		return cacheBuilder.build();
    }

	/**
	 *
	 * @Title: redisCache
	 * @Description: RedisCache
	 * @param: @param name
	 * @param: @return
	 * @return: RedisCache
	 * @throws
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected RedisCache redisCache(String name) {
		if(redisTemplate != null) {
			RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
			GenericFastJsonRedisSerializer serializer = new GenericFastJsonRedisSerializer();
			RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
	                .entryTtl(Duration.ofSeconds(600))
	                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer((serializer)))
	                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(StringRedisSerializer.UTF_8));
	       return new CustomRedisCache(name,RedisCacheWriter.nonLockingRedisCacheWriter(connectionFactory), config);

		}

		return null;
	}

	/**
	 *
	 * @Title: redisCache
	 * @Description: RedisCache
	 * @param: @param name
	 * @param: @return
	 * @return: RedisCache
	 * @throws
	 */
	protected RedisCache redisCache(CachesEnums cacheType) {
		if(redisTemplate != null) {
			RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
			RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig();
			switch (cacheType.getTimeUnit()) {
			case DAYS:
				config = config.entryTtl(Duration.ofDays(cacheType.getExpires()));
				break;
			case HOURS:
				config = config.entryTtl(Duration.ofHours(cacheType.getExpires()));
				break;
			case MINUTES:
				config = config.entryTtl(Duration.ofMinutes(cacheType.getExpires()));
				break;
			case SECONDS:
				config = config.entryTtl(Duration.ofSeconds(cacheType.getExpires()));
				break;
			case MILLISECONDS:
				config = config.entryTtl(Duration.ofMillis(cacheType.getExpires()));
				break;
			default:
				config = config.entryTtl(Duration.ofSeconds(cacheType.getExpires()));
				break;
			}

			GenericFastJsonRedisSerializer serializer = new GenericFastJsonRedisSerializer();
	        config = config.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer((serializer)))
			.serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()));
	        return new CustomRedisCache(cacheType.name(),RedisCacheWriter.nonLockingRedisCacheWriter(connectionFactory), config);

		}

		return null;
	}
}
