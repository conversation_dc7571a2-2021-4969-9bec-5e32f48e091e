package cn.cmcc.auth.service;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.web.domain.AjaxResult;
import io.jsonwebtoken.Jwts;

/**
 * <p>
 * CAS 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
public interface ICasService {

    /**
     * 登录
     *
     * @param request 请求
     * @return 结果
     */
    AjaxResult login(HttpServletRequest request);

    /**
     * 注销
     *
     * @param request 请求
     */
    default void logout(HttpServletRequest request) {
        //
    }

    /**
     * 注销
     *
     * @param ticket 票据
     */
    default void logout(String ticket) {
        //
    }

    /**
     * TOKEN 是否过期
     *
     * @param token TOKEN
     * @return 是否过期
     */
    default boolean tokenExpired(String token) {
        return false;
    }

    /**
     * 有效的 Token
     *
     * @param request 请求
     * @return 是否有效
     */
    default boolean validToken(HttpServletRequest request) {
        String token = request.getHeader(SecurityConstants.AUTHENTICATION);
        if (StringUtils.isNotEmpty(token) && token.startsWith(SecurityConstants.PREFIX)) {
            token = token.replace(SecurityConstants.PREFIX, StringUtils.EMPTY);
        }
        return !StringUtils.isEmpty(token) && Jwts.parser().setSigningKey(SecurityConstants.SKEY).
                parseClaimsJws(token).getBody().getExpiration().after(new Date());
    }

    /**
     * 获取参数
     *
     * @param request   请求
     * @param paramName 参数名称
     * @return 参数
     */
    default String getParameter(HttpServletRequest request, String paramName) {
        String param = request.getParameter(paramName);
        return "null".equalsIgnoreCase(param) || "undefined".equalsIgnoreCase(param) ?
                null : param;
    }
    
    /**
     * 获取票据
     *
     * @param request         请求
     * @param ticketParamName 票据参数名称
     * @return 票据
     */
    default String getTicket(HttpServletRequest request, String ticketParamName) {
        String ticket = request.getParameter(ticketParamName);
        return "null".equalsIgnoreCase(ticket) || "undefined".equalsIgnoreCase(ticket) ?
                null : ticket;
    }


}
