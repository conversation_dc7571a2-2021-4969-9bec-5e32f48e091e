# 查询编码权限管理 API 接口文档

## 接口概览

本文档详细描述了查询编码权限管理系统的所有 REST API 接口，包括请求参数、响应格式和使用示例。

**基础路径**: `/kernel/queryCodePermission`

**权限要求**: 除权限检查接口外，所有管理接口都需要 `ADMIN` 角色权限。

## 1. 权限检查接口

### 1.1 检查用户权限

**接口地址**: `GET /kernel/queryCodePermission/check/{username}/{queryCode}`

**接口描述**: 检查指定用户是否有权限访问指定的查询编码

**请求参数**:
| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| username | String | Path | 是 | 用户名 |
| queryCode | String | Path | 是 | 查询编码 |

**响应示例**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true
}
```

**使用场景**: 在业务代码中动态检查用户权限

---

## 2. 用户权限管理接口

### 2.1 授予用户权限

**接口地址**: `POST /kernel/queryCodePermission/user/grant`

**接口描述**: 为指定用户授予查询编码的访问权限

**请求体**:
```json
{
    "username": "testuser",
    "queryCode": "qryTestCode",
    "permissionType": "read",
    "remark": "测试权限授予"
}
```

**请求参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | String | 是 | 用户名 |
| queryCode | String | 是 | 查询编码 |
| permissionType | String | 是 | 权限类型（read/write） |
| remark | String | 否 | 备注信息 |

**响应示例**:
```json
{
    "code": 200,
    "msg": "用户权限授予成功",
    "data": null
}
```

### 2.2 撤销用户权限

**接口地址**: `DELETE /kernel/queryCodePermission/user/revoke/{username}/{queryCode}`

**接口描述**: 撤销指定用户对查询编码的访问权限

**请求参数**:
| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| username | String | Path | 是 | 用户名 |
| queryCode | String | Path | 是 | 查询编码 |

**响应示例**:
```json
{
    "code": 200,
    "msg": "用户权限撤销成功",
    "data": null
}
```

### 2.3 获取用户权限列表

**接口地址**: `GET /kernel/queryCodePermission/user/{username}`

**接口描述**: 获取指定用户的所有权限列表

**请求参数**:
| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| username | String | Path | 是 | 用户名 |

**响应示例**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "uuid-1",
            "userId": "123",
            "username": "testuser",
            "queryCode": "qryTestCode1",
            "permissionType": "read",
            "createTime": "2025-01-28 10:00:00",
            "createBy": "admin",
            "remark": "测试权限"
        },
        {
            "id": "uuid-2",
            "userId": "123",
            "username": "testuser",
            "queryCode": "qryTestCode2",
            "permissionType": "read",
            "createTime": "2025-01-28 11:00:00",
            "createBy": "admin",
            "remark": null
        }
    ]
}
```

### 2.4 批量授予用户权限

**接口地址**: `POST /kernel/queryCodePermission/user/batchGrant`

**接口描述**: 批量为用户授予多个查询编码的访问权限

**请求体**:
```json
{
    "username": "testuser",
    "queryCodes": ["qryCode1", "qryCode2", "qryCode3"],
    "permissionType": "read",
    "remark": "批量授权"
}
```

**请求参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | String | 是 | 用户名 |
| queryCodes | String[] | 是 | 查询编码列表 |
| permissionType | String | 是 | 权限类型 |
| remark | String | 否 | 备注信息 |

**响应示例**:
```json
{
    "code": 200,
    "msg": "用户权限批量授予成功",
    "data": null
}
```

### 2.5 批量撤销用户权限

**接口地址**: `POST /kernel/queryCodePermission/user/batchRevoke`

**接口描述**: 批量撤销用户对多个查询编码的访问权限

**请求体**:
```json
{
    "username": "testuser",
    "queryCodes": ["qryCode1", "qryCode2", "qryCode3"]
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "用户权限批量撤销成功",
    "data": null
}
```

---

## 3. 角色权限管理接口

### 3.1 授予角色权限

**接口地址**: `POST /kernel/queryCodePermission/role/grant`

**接口描述**: 为指定角色授予查询编码的访问权限

**请求体**:
```json
{
    "roleKey": "BASE_PRI_ROLE",
    "queryCode": "qryTestCode",
    "permissionType": "read",
    "remark": "角色权限授予"
}
```

**请求参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleKey | String | 是 | 角色标识 |
| queryCode | String | 是 | 查询编码 |
| permissionType | String | 是 | 权限类型 |
| remark | String | 否 | 备注信息 |

### 3.2 撤销角色权限

**接口地址**: `DELETE /kernel/queryCodePermission/role/revoke/{roleKey}/{queryCode}`

**接口描述**: 撤销指定角色对查询编码的访问权限

### 3.3 获取角色权限列表

**接口地址**: `GET /kernel/queryCodePermission/role/{roleKey}`

**接口描述**: 获取指定角色的所有权限列表

**响应示例**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "uuid-1",
            "roleId": "1",
            "roleKey": "BASE_PRI_ROLE",
            "queryCode": "qryTestCode1",
            "permissionType": "read",
            "createTime": "2025-01-28 10:00:00",
            "createBy": "admin"
        }
    ]
}
```

### 3.4 批量授予角色权限

**接口地址**: `POST /kernel/queryCodePermission/role/batchGrant`

**请求体**:
```json
{
    "roleKey": "BASE_PRI_ROLE",
    "queryCodes": ["qryCode1", "qryCode2", "qryCode3"],
    "permissionType": "read"
}
```

### 3.5 批量撤销角色权限

**接口地址**: `POST /kernel/queryCodePermission/role/batchRevoke`

---

## 4. 查询统计接口

### 4.1 获取用户可访问的查询编码

**接口地址**: `GET /kernel/queryCodePermission/accessible/{username}`

**接口描述**: 获取用户通过直接权限和角色权限可以访问的所有查询编码

**响应示例**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "username": "testuser",
        "queryCodes": ["qryCode1", "qryCode2", "qryCode3"],
        "permissionType": "read",
        "totalCount": 3
    }
}
```

### 4.2 获取查询编码的权限统计

**接口地址**: `GET /kernel/queryCodePermission/users/{queryCode}`

**接口描述**: 获取指定查询编码的权限分配统计信息

**响应示例**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "queryCode": "qryTestCode",
        "users": ["user1", "user2", "user3"],
        "roles": ["BASE_PRI_ROLE", "ADMIN"],
        "userCount": 3,
        "roleCount": 2
    }
}
```

---

## 5. 缓存管理接口

### 5.1 清除用户权限缓存

**接口地址**: `POST /kernel/queryCodePermission/clearUserCache/{username}`

**接口描述**: 清除指定用户的权限缓存

### 5.2 清除查询编码权限缓存

**接口地址**: `POST /kernel/queryCodePermission/clearCodeCache/{queryCode}`

**接口描述**: 清除指定查询编码的权限缓存

---

## 6. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权访问 | 检查用户登录状态和权限 |
| 403 | 权限不足 | 确认用户具有 ADMIN 角色权限 |
| 500 | 服务器内部错误 | 查看服务器日志，联系管理员 |

## 7. 使用示例

### JavaScript/Ajax 示例

```javascript
// 检查用户权限
$.get('/kernel/queryCodePermission/check/testuser/qryTestCode', function(result) {
    if (result.code === 200 && result.data === true) {
        console.log('用户有权限');
    } else {
        console.log('用户无权限');
    }
});

// 授予用户权限
$.ajax({
    url: '/kernel/queryCodePermission/user/grant',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
        username: 'testuser',
        queryCode: 'qryTestCode',
        permissionType: 'read'
    }),
    success: function(result) {
        console.log('权限授予成功');
    }
});
```

### Java 示例

```java
// 使用 RestTemplate
@Autowired
private RestTemplate restTemplate;

// 检查权限
Boolean hasPermission = restTemplate.getForObject(
    "/kernel/queryCodePermission/check/{username}/{queryCode}", 
    Boolean.class, 
    "testuser", 
    "qryTestCode"
);

// 授予权限
PermissionGrantRequest request = new PermissionGrantRequest();
request.setUsername("testuser");
request.setQueryCode("qryTestCode");
request.setPermissionType("read");

AjaxResult result = restTemplate.postForObject(
    "/kernel/queryCodePermission/user/grant", 
    request, 
    AjaxResult.class
);
```

---

**文档版本**: v1.0.0  
**最后更新**: 2025-01-28  
**维护者**: Claude 4.0 sonnet
