<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.cmcc.common.datasource.mapper.CommonMapper">

	<select id="checkFunctionExist" parameterType="String" resultType="Integer">
		SELECT
		  count(1)
		FROM
		  pg_proc
		    JOIN pg_type
		   ON (pg_proc.prorettype = pg_type.oid)
		WHERE
		  pg_type.typname != 'void' and pg_proc.proname = #{functionName}
		  AND pronamespace = (SELECT pg_namespace.oid FROM pg_namespace WHERE nspname = 'public')
	</select>

	<update id="createFindattname">
		CREATE OR REPLACE FUNCTION "findattname"("dbinstancename" varchar, "namespace" varchar, "tablename" varchar, "ctype" varchar)
		  RETURNS "pg_catalog"."varchar" AS $BODY$

			declare
				tt oid ;
				aname character varying default '';

				begin
				       tt := oid from pg_class where relname= tablename
				    and relnamespace =(select oid from pg_namespace  where nspname=namespace and nspowner=(select datdba from pg_database where datname=dbinstancename) ) ;
				       aname:=  array_to_string(
				        array(
				               select a.attname  from pg_attribute  a
				                where a.attrelid=tt and  a.attnum   in (
				                select unnest(conkey) from pg_constraint c where contype=ctype
				                and conrelid=tt  and array_to_string(conkey,',') is not null
				            )
				        ),',')
				    ;

				    return aname;
				end
	    $BODY$
	  	LANGUAGE plpgsql VOLATILE
	  	COST 100
	</update>

	<select id="selectFieldTitle" resultType="java.util.Map">
		SELECT
			a.attname AS field_name,
			pd.description AS field_title,
			c.relname AS table_name,
			pd_table.description AS table_title
		FROM pg_attribute a
				 LEFT JOIN pg_class c ON a.attrelid = c.oid
				 LEFT JOIN pg_namespace n ON c.relnamespace = n.oid
				 LEFT JOIN pg_description pd ON a.attrelid = pd.objoid AND a.attnum = pd.objsubid
				 LEFT JOIN pg_description pd_table ON c.oid = pd_table.objoid AND pd_table.objsubid = 0
		WHERE n.nspname = '${nspname}'
		  AND c.relname = '${tableName}'
		  AND a.attnum > 0
		  AND NOT a.attisdropped
		  AND a.attname = '${fieldName}'
		ORDER BY a.attnum;
	</select>

	<update id="createShowcreatetable">
		CREATE OR REPLACE FUNCTION "showcreatetable"("namespace" varchar, "tablename" varchar)
			  RETURNS "pg_catalog"."varchar" AS $BODY$
			declare
			tableScript character varying default '';
			tableNum int2 ;
			 dbinstancename character varying default '';
			begin
			dbinstancename := current_database();
			-- check db extist
			tableNum:= count(*)    from pg_class  where relname=tablename and relnamespace =(
			            select oid from pg_namespace where nspowner=(select datdba from pg_database where datname=dbinstancename) and nspname = namespace
			            );
			IF tableNum=0  then
			return '' ;
			end if;
			-- columns
			tableScript:=tableScript || ' CREATE TABLE IF NOT EXISTS '|| '"'||namespace||'".'||tablename|| ' ( '|| array_to_string(
			  array(
			select concat( c1, c2, c3, c4, c5, c6 ) as column_line
			from (
			  select '"'||column_name||'"' || ' ' || case when data_type='ARRAY' then ltrim(udt_name,'_')||'[]' else data_type end as c1,
			    case when character_maximum_length > 0 then '(' || character_maximum_length || ')' end as c2,
			    case when numeric_precision > 0 and numeric_scale &lt; 1 then null end as c3,
			    case when numeric_precision > 0 and numeric_scale &gt; 0 then null end as c4,
			    case when is_nullable = 'NO' then ' NOT NULL' end as c5,
			    case when column_default is not Null then ' DEFAULT' end || ' ' || replace(column_default, '::character varying', '') as c6
			  from information_schema.columns
			  where table_name = tablename
			    and table_catalog=dbinstancename
			  and table_schema=namespace
			  order by ordinal_position
			) as string_columns
			),' , '  || chr(13) ) ||',';


			-- 约束
			tableScript:= tableScript || array_to_string(
			array(
			    select concat(' CONSTRAINT ','"'||conname||'"' ,c ,u,p,f)   from (
			        select conname,
			        case when contype='c' then  ' CHECK('|| consrc ||')' end  as c  ,
			        case when contype='u' then  ' UNIQUE('|| ( select findattname(dbinstancename,namespace,tablename,'u') ) ||')' end as u ,
			        case when contype='p' then ' PRIMARY KEY ('|| ( select findattname(dbinstancename,namespace,tablename,'p') ) ||')' end  as p  ,
			        case when contype='f' then ' FOREIGN KEY('|| ( select findattname(dbinstancename,namespace,tablename,'u') ) ||') REFERENCES '||
			        (select p.relname from pg_class p where p.oid=c.confrelid )  || '('|| ( select findattname(dbinstancename,namespace,tablename,'u') ) ||')' end as  f
			        from pg_constraint c
			        where contype in('u','c','f','p') and conrelid=(
			            select oid  from pg_class  where relname=tablename and relnamespace =(
			            select oid from pg_namespace where nspowner=(select datdba from pg_database where datname=dbinstancename) and nspname = namespace
			            )
			         )
			    ) as t
			) ,',' ) || ' ); ' || chr(13);

			--
			/** **/
			--- 获取非约束索引 column
			-- CREATE UNIQUE INDEX pg_language_oid_index ON pg_language USING btree (oid); -- table pg_language
			tableScript:= tableScript || array_to_string(
			    array(
			        select 'CREATE '||case when is_unique_index=true then 'UNIQUE INDEX' else 'INDEX' end ||'"' || indexrelname ||'"' || ' ON ' || '"'||namespace||'".'||tablename|| ' USING '||index_type|| '(' || attname || ');' || chr(13) from (
			         SELECT
			            i.relname AS indexrelname ,  x.indkey,
			            ( select array_to_string (
			            array(
			                select a.attname from pg_attribute a where attrelid=c.oid and a.attnum in ( select unnest(x.indkey) )

			                 )
			             ,',' ) )as attname, x.indisunique is_unique_index,am.amname index_type

			           FROM pg_class c
			           JOIN pg_index x ON c.oid = x.indrelid
			           JOIN pg_class i ON i.oid = x.indexrelid
			             join pg_am am on am.oid = i.relam
			           LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
			           WHERE  c.relname=tablename and i.relname not in
			              ( select constraint_name from information_schema.key_column_usage  where  table_name=tablename  )
			                and c.relnamespace =(
			            select oid from pg_namespace where nspowner=(select datdba from pg_database where datname=dbinstancename) and nspname = namespace
			            )
			        )as t
			) ,'' );



			return tableScript;

			end
			$BODY$
			  LANGUAGE plpgsql VOLATILE
			  COST 100
	</update>

	<select id="getTableStructure" parameterType="java.lang.String" resultType="cn.cmcc.common.datasource.pojo.bo.TableColumn">
		SELECT column_name, column_type, length, column_comment, required, pk FROM (
			SELECT
				a.attname AS column_name,
				/**pg_catalog.format_type(a.atttypid, a.atttypmod) AS column_type,**/
				t.typname AS column_type,
				COALESCE(NULLIF(substring(pg_catalog.format_type(a.atttypid, a.atttypmod) FROM '\((.*)\)'), ''), '') AS length,
				col_description(a.attrelid, a.attnum) AS column_comment,
				CASE WHEN a.attnotnull THEN true ELSE false END AS required,
				CASE WHEN pc.contype = 'p' THEN true ELSE false END AS pk,
				a.attnum,
				ROW_NUMBER() OVER (PARTITION BY a.attname ORDER BY a.attnum) AS row_num
			FROM pg_catalog.pg_attribute a
			JOIN pg_catalog.pg_class c ON a.attrelid = c.oid
			JOIN pg_catalog.pg_type t ON a.atttypid = t.oid
			JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
			LEFT JOIN (
				SELECT conrelid,unnest(conkey) AS conkey,contype FROM pg_catalog.pg_constraint WHERE contype = 'p'
			) pc ON pc.conrelid = c.oid AND pc.conkey = a.attnum
			WHERE c.relname = #{tableName}
			<if test="schemeName != null and schemeName != ''">
				AND n.nspname = #{schemeName} /*-- 指定模式名称*/
			</if>
			AND a.attnum > 0 AND NOT a.attisdropped
		) AS subquery
		WHERE row_num = 1
		ORDER BY attnum
	</select>

	<insert id="createTextTempTable">
		CREATE TEMPORARY TABLE ${tmpTable} (
		<foreach collection="fieldList" item="column" open="" separator="," close="">
			<choose>
				<when test="quote != null and quote !='' and quote==true">
					"${column}" text
				</when>
				<otherwise>
					${column} text
				</otherwise>
			</choose>
		</foreach>
		)
	</insert>

	<insert id="createTextTable">
		CREATE TABLE ${tmpTable} (
		<foreach collection="fieldList" item="column" open="" separator="," close="">
			${column} varchar
		</foreach>
		)
	</insert>

	<select id="getTableNameMap" resultType="java.util.Map">
		SELECT
			c.relname AS table_name,
			obj_description(c.oid, 'pg_class') AS table_comment
		FROM
			pg_class c
		WHERE
			c.relkind = 'r' -- 只选择常规表（非索引、视图等）
		  AND c.relnamespace IN (
			SELECT oid FROM pg_namespace WHERE nspname NOT IN ('pg_toast', 'information_schema')
		) -- 排除系统表空间
		  and c.relname in
		<foreach collection="relatedTables" item="item" open="(" separator=", " close=")">
			#{item}
		</foreach>
		ORDER BY
			c.relname;
	</select>
	
	<update id="dropTable">
		drop table IF EXISTS ${tableName}
	</update>

	<select id="findCommonQryFieldByQryCode"  parameterType="java.lang.String" resultType="cn.cmcc.common.datasource.pojo.bo.TCommonQueryFieldDto">
 		select id,sort,name,title,has_import,java_type,drop_down_source_code,drop_down_source_type from t_common_query_field where common_query_code=#{code} order by sort asc;
	</select>

	<!--根据表名查询表信息-->
	<select id="findTableInfoByTableName"  parameterType="java.lang.String" resultType="cn.cmcc.common.datasource.pojo.bo.TableColumnsInfoBo">
 		SELECT column_name,data_type FROM information_schema.columns where table_name =#{tableName}
	</select>

	<select id="getDBCurrentSchema"   resultType="java.lang.String">
		SELECT current_schema();
	</select>

	<update id="alterAddColumn">
		alter table ${tableName} add column ${colName} varchar(20);
	</update>
	<update id="alterDropColumn">
		alter table ${tableName} drop column ${colName};
	</update>
</mapper>
