<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.cmcc.common.datasource.mapper.CommonCurdMapper">

	<select id="uniqueCheck" resultType="Integer">
		select count(1) from ${tableName} where 1 = 1
		<foreach item="item" collection="params" separator="" open="" close="">
			<if test="item.value != null and item.type == 'add'.toString() ">
				and ${item.name} = #{item.value}
			</if>
			<if test="item.value != null and item.type == 'edit'.toString() ">
				and ${item.name} != #{item.value}
			</if>
		</foreach>
	</select>

	<insert id="save">
		 insert into ${tableName}
        <foreach collection="param.keys" item="key" open="(" close=")"
                 separator=",">
            ${key}
        </foreach>
        values
        <foreach collection="param.keys" item="key" open="(" close=")"
                 separator=",">
			<choose>
				<when test="key == 'geom'">
					#{param[${key}],jdbcType=OTHER,typeHandler=cn.cmcc.common.datasource.handler.GeometryTypeHandler}
				</when>
				<otherwise>
					#{param[${key}]}
				</otherwise>
			</choose>

        </foreach>
	</insert>

	<update id="update">
        update ${tableName}
        <set>
            <foreach collection="param.entrySet()" index="key" item="value"
                     separator=",">
                <choose>
                	<when test="key == 'geom' or key == 'cover_geom'">
						${key} =#{value,jdbcType=OTHER,typeHandler=cn.cmcc.common.datasource.handler.GeometryTypeHandler}
                	</when>
                	<otherwise>
                		<if test="key != 'id'.toString()">
		                    ${key} = #{value}
		                </if>
                	</otherwise>
                </choose>
            </foreach>
        </set>
        WHERE
        ${privimaryKey} =#{privimaryKeyValue}
    </update>

    <update id="updateByMultiField">
		update ${tableName} set
    	<foreach collection="param.entrySet()" item="value" index="key" separator=",">
			${key} = #{value}
        </foreach>
		WHERE 1 = 1
        <foreach collection="whereParam.entrySet()" item="value" index="key" separator="">
           and ${key} = #{value}
        </foreach>
    </update>

    <delete id="delete">
		delete from ${tableName} where ${privimaryKey} in
    	<foreach item="item" collection="list" separator="," open="(" close=")">
			#{item}
		</foreach>
    </delete>

    <select id="detail" resultType="map">
    	select * from  ${tableName} where ${privimaryKey} = #{privimaryKeyValue}
    </select>

    <insert id="saveBatchType">
		 insert into ${tableName}
        <foreach collection="fields.keys" item="key" open="(" close=")"
                 separator=",">
            ${key}
        </foreach>
        values
        <foreach collection="dataList" item="dataMap" separator=",">
            (
             <foreach collection="dataMap.entrySet()" item="value" index="key" separator=",">
	            <choose>
            			<when test="value.dataType == 'geometry'">
            				#{value.fieldValue,jdbcType=OTHER,typeHandler=cn.cmcc.common.core.db.GeometryTypeHandler}
            			</when>
            			<otherwise>
            				#{value.fieldValue}
            			</otherwise>
            		</choose>

	        </foreach>
            )
        </foreach>
	</insert>

	<insert id="insertDynamicFieldsAndValues">
		 insert into ${tableName}
        <foreach collection="fields.keys" item="key" open="(" close=")"
                 separator=",">
            ${key}
        </foreach>
        values
        <foreach collection="dataList" item="dataMap" separator=",">
            (
             <foreach collection="dataMap.entrySet()" item="value" index="key" separator=",">
	            #{value}
	        </foreach>
            )
        </foreach>
	</insert>

	<insert id="insertStaticFieldsFromObjects">
		INSERT INTO ${tableName}
		<foreach collection="fields" item="field" open="(" close=")" separator=",">
			${field}
		</foreach>
		values
		<foreach collection="dataList" item="data" separator=",">
			<foreach collection="fields" item="field" open="(" close=")" separator=",">
				#{data.${field}}
			</foreach>
		</foreach>
	</insert>

	<delete id="deleteBatch">
		delete from ${tableName} where 1 = 1
		<foreach collection="dataList.entrySet()" item="value" index="key" open="" close=""
                 separator="">
			and ${key} in
            <foreach collection="value" item="data" open="(" close=")"
                 separator=",">
				#{data}
            </foreach>
        </foreach>
	</delete>

	<delete id="deleteBatchByField">
		delete from ${tableName} where ${fieldName} in
		<foreach collection="dataList" item="data" open="(" close=")"
                 separator=",">
			#{data}
        </foreach>
	</delete>

	<delete id="deleteByMultiField">
		delete from ${tableName} where 1 = 1
		<foreach collection="fieldValueMap.entrySet()" item="value" index="key" separator="">
           and ${key} = #{value}
        </foreach>
	</delete>

	<delete id="deleteBatchByTempTable">
		delete from ${tableName} a where 1 = 1
		and EXISTS (select 1 from ${tmpTable} b where 1 = 1
		<foreach collection="uniqueKeys" item="item" open="" separator=" " close="">
			AND a.${item} = b.${item}
		</foreach>
		)
	</delete>

	<select id="qryRepeatList" resultType="map">
		select ${primaryKey},${orderNo},
		<foreach collection="dataList.keys" item="key"
                 separator=",">
            ${key}
        </foreach>
		from ${tableName} where 1 =1
		<foreach collection="dataList.entrySet()" item="value" index="key" open="" close=""
				 separator="">
			and ${key} in
			<foreach collection="value" item="data" open="(" close=")"
					 separator=",">
				#{data}
			</foreach>
		</foreach>
	</select>

	<delete id="clearTable">
		TRUNCATE TABLE ${tableName}
	</delete>

	<delete id="removeRequiredIsNull">

	</delete>

	<delete id="deleteRepeData">
		DELETE FROM ${tempTableName}
		WHERE EXISTS (
		SELECT 1
		FROM ${originalTableName}
		WHERE 1 = 1
		<foreach collection="uniKeyColumns" item="column" separator="" close="" >
			<if test="column.data_type != 'varchar' and column.data_type != 'text'">
				AND (${tempTableName}.${column.column_name} ~ <![CDATA['${column.match}']]> OR ${tempTableName}.${column.column_name} IS NULL)
			</if>
			AND ${originalTableName}.${column.column_name} = ${tempTableName}.${column.column_name}::${column.data_type}
		</foreach>
		);
	</delete>

	<delete id="deleteTempTable">
		DROP TABLE IF EXISTS ${tempTableName};
	</delete>
	<delete id="deleteByNotEqColumnValue">
		<if test="fieldName != null and fieldName != '' and columnValue != '' and columnValue != null">
			delete from ${tableName} where ${fieldName} &lt;&gt; #{columnValue} or ${fieldName} is null;
		</if>
	</delete>

	<insert id="createTempTable">
		CREATE TEMPORARY TABLE ${tmpTable}
		ON COMMIT DROP AS
		SELECT
		<choose>
			<when test="fieldList != null">
				<foreach collection="fieldList" item="item" open="" separator=", " close="">
					${item}
				</foreach>
			</when>
			<otherwise>
				*
			</otherwise>
		</choose>

		FROM ${table} WHERE 1 = 2
	</insert>

	<update id="updateByFieldNameFromTempTable">
		UPDATE ${tableName} a SET
		<foreach collection="updateFields" item="item" open="" separator="," close="">
			${item.left} = ${item.right}
		</foreach>
		FROM ${tmpTable} b
		WHERE 1 = 1
		<foreach collection="uniqueKeys" item="item" open="" separator=" " close="">
			AND a.${item} = b.${item}
		</foreach>
	</update>

	<select id="selectRepeatFromTempTable" resultType="java.lang.String">
		SELECT
		<foreach collection="uniqueKeys" item="item" open="" separator="|| '_' ||" close="">
			${item}
		</foreach>
		FROM
		${tmpTable} a
		WHERE
		EXISTS (
		SELECT
		1
		FROM
		${table} b
		WHERE 1 = 1
		<foreach collection="uniqueKeys" item="item" open="" separator=" " close="">
			AND a.${item} = b.${item}
		</foreach>
		)
	</select>

	<select id="selectColumnsType" resultType="java.util.Map">
		SELECT COLUMN_NAME,	UDT_NAME AS data_type	FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '${tableScheme}'  AND TABLE_NAME = '${tableName}';
	</select>

	<select id="selectInsertFailedDataFromTempTable" resultType="java.util.Map">
		SELECT
		<if test="columns != null">
			<foreach collection="columns" item="column" separator=",">
				b.${column.column_name},
				(CASE WHEN ('${column.data_type}' = 'varchar' OR '${column.data_type}' = 'text' OR
				b.${column.column_name} ~ <![CDATA['${column.match}']]> OR b.${column.column_name} IS NULL)
				THEN ''
				ELSE '字段【${column.column_name_zh}】发生错误: 数据类型为[${column.data_type_zh}${column.data_type}]，而你填的是“'||b.${column.column_name}||'”；'
				END ) AS error_${column.column_name}_info
			</foreach>
		</if>
		<if test="columns == null">
			b.*
		</if>
		FROM ${tempTableName} b
		WHERE NOT EXISTS (
		SELECT 1 FROM ${originalTableName} a WHERE 1 = 1
		<foreach collection="uniKeyColumns" item="item" separator="" close="" >
			<if test="item.data_type != 'varchar' and item.data_type != 'text'">
				AND (b.${item.column_name} ~ <![CDATA['${item.match}']]> OR b.${item.column_name} IS NULL)
			</if>
			AND a.${item.column_name} = b.${item.column_name}::${item.data_type}
		</foreach>
		)
	</select>

	<insert id="insertTableFromTempTable">
		INSERT INTO ${table}
		<if test="fields != null and fields != '' ">
			(${fields}) SELECT ${fields}
		</if>
		<if test="fields == null or fields == ''.toString() ">
			SELECT *
		</if>
		FROM
		${tmpTable} b
		<if test="uniqueKeys != null">
			WHERE NOT EXISTS ( SELECT 1 FROM ${table} a WHERE 1 = 1
			<foreach collection="uniqueKeys" item="item" open="" separator=" " close="">
				AND b.${item} = a.${item}
			</foreach>
			)
		</if>
    </insert>

	<insert id="insertOriginalTableFromTextTempTable">
		INSERT INTO ${originalTableName}
		<if test="columns != null">
			<foreach collection="columns" item="column" open="(" separator="," close=")">
				<choose>
					<when test="quote != null and quote !='' and quote==true">
		    			"${column.column_name}"
		    		</when>
					<otherwise>
						${column.column_name}
					</otherwise>
				</choose>
			</foreach>
		</if>
		SELECT
		<if test="columns != null">
			<foreach collection="columns" item="column" open="" separator="," close="">
				<choose>
					<when test="quote != null and quote !='' and quote==true">
						"${column.column_name}"::${column.data_type}
					</when>
					<otherwise>
						${column.column_name}::${column.data_type}
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="columns == null">
			*
		</if>
		FROM ${tempTableName} b
		WHERE
		1 = 1
		<if test="columns != null">
			<foreach collection="columns" item="column" separator="" close="">
				<if test="column.data_type != 'varchar' and column.data_type != 'text' and column.ingore_match != 'true' ">
					AND (b.${column.column_name} ~ <![CDATA['${column.match}']]> OR b.${column.column_name} IS NULL)
				</if>
			</foreach>
		</if>
	</insert>

	<update id="batching">
		<choose>
			<when test="insert">
				INSERT INTO ${table}
				<foreach collection="fieldAttributeType" index="index" item="item" open="(" separator="," close=")">
					${item.left}
				</foreach>
				VALUES
				<foreach collection="list" item="data" separator=",">
					<foreach collection="fieldAttributeType" index="index" item="item" open="(" close=")" separator=",">
						<choose>
							<when test="item.right == 91">
								TO_DATE(#{data.${item.middle}}, 'yyyy-MM-dd hh24:mi:ss')
							</when>
							<when test="item.right == 93">
								TO_TIMESTAMP(#{data.${item.middle}}, 'yyyy-MM-dd hh24:mi:ss')
							</when>
							<otherwise>
								#{data.${item.middle}}
							</otherwise>
						</choose>
					</foreach>
				</foreach>
			</when>
			<otherwise>
				UPDATE ${table} o SET
				<foreach collection="fieldAttributeType" index="index" item="item" open="" separator="," close="">
					<if test="key != item.left">
						${item.left} = COALESCE(n.${item.left}, o.${item.left})
					</if>
				</foreach>
				FROM (VALUES
				<foreach collection="list" item="data" separator=",">
					<foreach collection="fieldAttributeType" index="index" item="item" open="(" close=")" separator=",">
						<choose>
							<when test="item.right == 91">
								TO_DATE(#{data.${item.middle}}, 'yyyy-MM-dd hh24:mi:ss')
							</when>
							<when test="item.right == 93">
								TO_TIMESTAMP(#{data.${item.middle}}, 'yyyy-MM-dd hh24:mi:ss')
							</when>
							<otherwise>
								#{data.${item.middle}}
							</otherwise>
						</choose>
					</foreach>
				</foreach>
				) AS n
				<foreach collection="fieldAttributeType" index="index" item="item" open="(" separator="," close=")">
					${item.left}
				</foreach>
				WHERE o.${key} = n.${key}
			</otherwise>
		</choose>
	</update>

	<select id="selectRepetitionFromTempTable" resultType="java.util.List">
		SELECT
		<if test="columns != null">
			<foreach collection="columns" item="column" separator=",">
				b.${column.column_name} AS "${column.column_name_zh}"
			</foreach>
		</if>
		<if test="columns == null">
			b.*
		</if>
		FROM ${tempTableName} b
		<if test="uniKeyColumns != null">
			WHERE EXISTS (
			SELECT 1
			FROM ${originalTableName} a
			WHERE 1 = 1
			<foreach collection="uniKeyColumns" item="item" separator="" close="" >
				<if test="item.data_type != 'varchar' and item.data_type != 'text'">
					AND (b.${item.column_name} ~ <![CDATA['${item.match}']]> OR b.${item.column_name} IS NULL)
				</if>
				AND a.${item.column_name} = b.${item.column_name}::${item.data_type}
			</foreach>
			)
		</if>
	</select>

	<delete id="deleteNotUserCityData">
		<!-- 仅限云南使用  -->
		delete from ${tableName}
		where ${key} not in
		(
			SELECT c.name from sys_user_mgr_city a
								   join t_city_list c on a.city_id = c.id and a.user_id = #{userId}
			union
			SELECT c.alias from sys_user_mgr_city a
									join t_city_list c on a.city_id = c.id and a.user_id = #{userId}
		)
	</delete>

	<select id="findCountByKey" resultType="Integer">
		SELECT count(*) from ${tableName} where ${idField} = #{idValue}
	</select>
</mapper>
