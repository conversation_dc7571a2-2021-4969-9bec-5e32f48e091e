package cn.cmcc.cad.entity;

import java.io.Serializable;

import cn.cmcc.cad.service.ICadService;
import lombok.Data;

@Data
public class XhxkCadEllipse extends XhxkCadArc implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private XhxkCadPoint majorEndPoint;
	private XhxkCadPoint minorEndPoint;
	private XhxkCadPoint extrusion;
	private int angleStep = 4;

	public XhxkCadEllipse(String id, String type, String block,String layer, XhxkCadPoint centerPoint, double radius, double startAngle, double endAngle, XhxkCadPoint majorEndPoint, XhxkCadPoint minorEndPoint, XhxkCadPoint extrusion) {
		super(id, type, block,layer, centerPoint, radius, startAngle, endAngle);
		this.majorEndPoint = majorEndPoint;
		this.minorEndPoint = minorEndPoint;
		this.extrusion = extrusion;
	}

	@Override
	public void transfer(ICadService cadService,Double scale, XhxkCadPoint minPoint) {
		// TODO Auto-generated method stub
		super.transfer(cadService,scale, minPoint);
	}

}
