package cn.cmcc.common.cache.enums;

import java.util.concurrent.TimeUnit;

public enum CachesEnums {
	/**
     * 定义cache名称、超时时长（秒）、最大容量
     * 28800s: 8小时
     * 86400s: 24小时
     * false:使用caffeine缓存，false:不使用caffeine缓存
     * 注:若为false，但redis没有开启，默认还是使用caffeine缓存
     * 	  若为false，但开启了redis缓存，则直接使用redis缓存，不使用caffeine缓存
     * 	  若为false,但redis没有开启，则只使用caffeine缓存
     *    若为false,但开启redis缓存，则为二级缓存，
     */

	CACHE_DEFAULT(TimeUnit.SECONDS,86400,2000),
	CACHE_LOGIN(TimeUnit.DAYS,1,800),
	CACHE_CAS_LOGIN(TimeUnit.DAYS, 1, 800),
	/**
	 * 字典
	 */
	CACHE_DICT(TimeUnit.DAYS,360,2000),
	CACHE_REPEAT(TimeUnit.SECONDS,10,2000),
	/**
	 * 通用查询
	 */
    CACHE_REPORT(TimeUnit.DAYS,1,2000),
    /**
     * 工单处理锁
     */
	CACHE_ORDER_LOCK(TimeUnit.SECONDS,30,1500),

	/**
     * 工单信息变更
     */
	CACHE_ORDER_INFO_WATCH(TimeUnit.HOURS,8,1500),

	/**
	 * 同一工单多用户保存记录
	 */
	CACHE_ORDER_SAVE_LOCK(TimeUnit.SECONDS,300,800),

	/**
	 * 系统变量
	 */
	CACHE_SYS_CONFIG(TimeUnit.DAYS,360,500),

	/**
	 * 系统变量
	 */
	CACHE_SYS_CONFIG_FRONT(TimeUnit.DAYS,360,500),

	/**
	 *地市区县
	 */
	CACHE_REGION_VALUE(TimeUnit.DAYS,360,500),
	CACHE_REGION_NAME(TimeUnit.DAYS,360,500),

	/**
	 * 分公司
	 */
	CACHE_COMPANY_VALUE(TimeUnit.DAYS,360,500),
	CACHE_COMPANY_NAME(TimeUnit.DAYS,360,500),

	/**
	 * 导入进度
	 */
	CACHE_IMPORT_PROGRESS(TimeUnit.HOURS,1,100);

	CachesEnums(TimeUnit timeUnit,int expires, int maxSize) {
		this.timeUnit = timeUnit;
        this.expires = expires;
        this.maxSize = maxSize;
        this.useCaffeineCache = false;
    }

	CachesEnums(TimeUnit timeUnit,int expires, int maxSize,boolean useCaffeineCache) {
		this.timeUnit = timeUnit;
        this.expires = expires;
        this.maxSize = maxSize;
        this.useCaffeineCache = useCaffeineCache;
    }

	private int maxSize;
    private int expires;
    private boolean useCaffeineCache;
    private TimeUnit timeUnit;

	public int getMaxSize() {
        return maxSize;
    }
    public int getExpires() {
        return expires;
    }
	public boolean isUseCaffeineCache() {
		return useCaffeineCache;
	}
	public void setUseCaffeineCache(boolean useCaffeineCache) {
		this.useCaffeineCache = useCaffeineCache;
	}
	public TimeUnit getTimeUnit() {
		return timeUnit;
	}
	public void setTimeUnit(TimeUnit timeUnit) {
		this.timeUnit = timeUnit;
	}
	public void setMaxSize(int maxSize) {
		this.maxSize = maxSize;
	}
	public void setExpires(int expires) {
		this.expires = expires;
	}


}
