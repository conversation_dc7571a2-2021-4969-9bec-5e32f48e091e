/**
 * @Title: ExcelExportUtil.java
 * @Package cn.cmcc.base.utils
 *
 * <AUTHOR>
 * @date 2022年2月23日
 * @version V1.0
 */
package cn.cmcc.common.utils.file.excel;

import cn.cmcc.common.config.NaasConfig;
import cn.cmcc.common.nassert.Assert;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.file.FileUtils;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * HSSF 导出行数太少，不够用
 * XSSF 导出行数多，但内存消耗大
 * 使用多线程操作poi时，需注意的事项。
 * sheet.creaRow() 方法是非线程安全的 ，需要进行控制
 * poi 若需要对每个单元格分别设置样式，则不要每次的创建（cell.createCellStyle()）,否则很可能会出现样式混乱情况，正确做法应该复用相同单元格的样式，即使用 cellStyle.cloneStyleFrom(srcCellStyle)方法
 *
 */
@Slf4j
public class ExcelExportUtil {
    private SXSSFWorkbook wb;

    private String filePath;

    private boolean explicitHide;

    /**
     * 使用现有的模板，为true，不需要生成表头
     */
    private boolean withTemplate = false;

    /**
     * 最大行数
     */
    private final Integer maxRow = 1048575;

    /**
     * 缓存到内存总条数，超过该条数缓存到硬盘
     */
    private int CACHE_MEMORY_MAX = 10000;

    /**
     *  Sheet map
     */
    private Map<String,SXSSFSheetInfo> sheetMap;

    /**
     * 必填样式
     */
    private CellStyle cellStyle;

    /**
     * 解锁样式
     */
    private CellStyle unlockedCellStyle;

    /**
     * 锁定样式
     */
    private CellStyle lockedCellStyle;

	/**
	 * 水印配置
	 */
	@Setter
	private ExcelWatermarkUtil.WatermarkConfig watermarkConfig;

	@Data
	public class SXSSFSheetInfo{
		private SXSSFSheet sheet;
		private String sheetName;
		private String protectPasswd;
		private List<ExcelCellField> fieldList;
		private Map<Integer, String[]> explicitListConstraintMap = new HashMap<>();

		public SXSSFSheetInfo(String sheetName, String protectPasswd, List<ExcelCellField> fieldList) {
			if(withTemplate) {
				sheet = "WITH_SHEET0".equals(sheetName) ? wb.getSheetAt(0) : wb.getSheet(sheetName);
				Assert.notNull(sheet, String.format("导出导入模板文件中，sheet:%s 不存在，请检查配置，或修改sheet名称",sheetName));
			}else {
				sheet = wb.createSheet(sheetName);
			}
			this.protectPasswd = protectPasswd;

			CellStyle lockstyle = wb.createCellStyle();
			lockstyle.setHidden(true);

			this.fieldList = fieldList;
			if(explicitHide) {
				hideColumns();
				setCellExplicit();
			}
			if(StringUtils.isNotEmpty(protectPasswd)) {
				sheet.protectSheet(protectPasswd);
			}

		}

		/**
		 * 设置隐藏列
		 */
		private void hideColumns() {
			if (CollectionUtils.isNotEmpty(fieldList)) {
				for(int index = 0;index < fieldList.size();++ index) {
					ExcelCellField excelCellField = fieldList.get(index);
					if(excelCellField.isExportHide()) {
						this.sheet.setColumnHidden(index, true);
					}

					if(excelCellField.getDropDownValues() != null) {
						explicitListConstraintMap.put(index, excelCellField.getDropDownValues());
					}
				}
			}
		}

		/**
		 * 设置单元格下拉
		 */
		private void setCellExplicit() {
			DataValidationHelper helper = this.sheet.getDataValidationHelper();

			// 按列序号排序处理，确保顺序一致性
			explicitListConstraintMap.entrySet().stream()
					.sorted(Map.Entry.comparingByKey())
					.forEach(entry -> {
						Integer k = entry.getKey();
						String[] v = entry.getValue();

						// 为每个下拉列表创建唯一的sheet名称
						String sheetName = String.format("hideSheet_%d_%d", sheet.getWorkbook().getNumberOfSheets(), k);

						// 创建隐藏sheet
						Sheet hideSheet = wb.createSheet(sheetName);
						wb.setSheetHidden(wb.getSheetIndex(sheetName), true);

						// 写入下拉数据
						for (int i = 0; i < v.length; i++) {
							Row row = hideSheet.createRow(i);
							Cell cell = row.createCell(0);
							cell.setCellValue(v[i]);
						}

						// 创建名称引用
						Name categoryName = wb.createName();
						categoryName.setNameName(sheetName);
						categoryName.setRefersToFormula(sheetName + "!$A$1:$A$" + v.length);

						// 设置数据有效性验证范围
						CellRangeAddressList addressList = new CellRangeAddressList(1, maxRow, k, k);
						DataValidationConstraint constraint = helper.createFormulaListConstraint(sheetName);
						DataValidation validation = helper.createValidation(constraint, addressList);

						// 设置下拉框样式
						validation.setSuppressDropDownArrow(validation instanceof XSSFDataValidation);

						validation.setShowErrorBox(true);
						validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
						validation.createErrorBox("提示", "此值与单元格定义格式不一致");

						// 添加验证到sheet
						sheet.addValidationData(validation);
					});
		}


	}
    /**
     * 实例化excel工具类
     *
     * @return
     * @throws Exception
     */
    public static ExcelExportUtil getInstance() throws Exception {
        return new ExcelExportUtil();
    }

    /**
     * 实例化excel工具类
     *
     * @param filePath 文件路径
     * @return
     * @throws Exception
     */
    public static ExcelExportUtil getInstance(String filePath) throws Exception {
        return new ExcelExportUtil(filePath,true);
    }

	/**
     * 居于现有文件创建
	 *
     * @param filePath
     * @param fis
     * @return
     * @throws Exception
     */
    public static ExcelExportUtil getInstance(String filePath,InputStream fis)  throws Exception {
    	return new ExcelExportUtil(filePath,true,fis);
    }

	/**
     * 实例化excel工具类
     *
     * @param filePath 文件路径
     * @param explicitHide 文件名称
     * @return
     * @throws Exception
     */
    public static ExcelExportUtil getInstance(String filePath,boolean explicitHide) throws Exception {
        return new ExcelExportUtil(filePath,explicitHide);
    }

	private ExcelExportUtil() throws Exception{
    	this.sheetMap = new ConcurrentHashMap<String,SXSSFSheetInfo>();
        this.wb = new SXSSFWorkbook(CACHE_MEMORY_MAX);

		this.createDefaultCellStyle();
    }

	private ExcelExportUtil(String filePath,boolean explicitHide) throws Exception{
    	this.filePath = filePath;
    	this.explicitHide = explicitHide;
    	this.sheetMap = new ConcurrentHashMap<String,SXSSFSheetInfo>();
        this.wb = new SXSSFWorkbook(CACHE_MEMORY_MAX);

		this.createDefaultCellStyle();
    }

	private ExcelExportUtil(String filePath,boolean explicitHide,InputStream fis) throws Exception{
    	this.withTemplate = true;
    	this.filePath = filePath;
    	this.explicitHide = explicitHide;
    	this.sheetMap = new ConcurrentHashMap<String,SXSSFSheetInfo>();
        this.wb = new SXSSFWorkbook(new XSSFWorkbook(fis),CACHE_MEMORY_MAX);

		this.createDefaultCellStyle();
    }

	/**
     * 默认样式
     */
    private void createDefaultCellStyle() {
    	//必填样式，cell单元格标红
    	this.cellStyle = wb.createCellStyle();
    	this.cellStyle.setAlignment(HorizontalAlignment.CENTER); //左右居中
    	this.cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
    	this.cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    	this.cellStyle.setFillForegroundColor(IndexedColors.RED.getIndex()); // 背景色-红色

		this.unlockedCellStyle  = wb.createCellStyle();
		this.unlockedCellStyle.setLocked(false);

		this.lockedCellStyle  = wb.createCellStyle();
		this.lockedCellStyle.setLocked(true);
    }

	/**
	 *
     * @param cellStyle
     */
    public void setRquiredCellStyle(CellStyle cellStyle) {
    	this.cellStyle = cellStyle;
	}

	/**
	 * 设置水印文本
	 *
	 * @param watermarkText 水印文本
	 */
	public void setWatermarkText(String watermarkText) {
		if (watermarkText != null && !watermarkText.trim().isEmpty()) {
			this.watermarkConfig = ExcelWatermarkUtil.createDefaultConfig(watermarkText);
		}
	}

    /**
     * 写入Excel
     *
     * @param recordIndex 写入第几行数据
     * @param strs 需要写入每行数据
     * @param sheetName 页签名称
     * @throws IOException
     */
    public void writeExcelRecords(int recordIndex, List<String> strs, String sheetName){
    	SXSSFSheetInfo  sheetInfo = getSheetInfo(sheetName,null,null);
    	SXSSFRow hssfrow = sheetInfo.getSheet().createRow(recordIndex);

		for (int i = 0; i < strs.size(); i++) {
			String val = strs.get(i);

			SXSSFCell hssfcell = hssfrow.createCell(i);
			hssfcell.setCellValue(val==null||val.equals("null")?"":strs.get(i) + "");
		}
	}

	/**
     * 写入Excel，超过最大行，重新开一个sheet
     *
     * @param recordIndex 写入第几行数据
     * @param rowData 需要写入每行数据
     * @param sheetName 页签名称
     * @param fieldList 字典列表
     * @throws IOException
     */
    public void writeExcelRecords(int recordIndex, Map<String,Object> rowData, String sheetName,List<ExcelCellField> fieldList){

		int sheetIndex = recordIndex / maxRow;
    	if(sheetIndex > 0 ) sheetName = sheetName + sheetIndex;

		SXSSFSheetInfo  sheetInfo = getSheetInfo(sheetName,null,fieldList);
    	SXSSFRow hssfrow = sheetInfo.getSheet().createRow(recordIndex % maxRow);

		for(int index = 0;index < fieldList.size();++ index) {
    		ExcelCellField cellField = fieldList.get(index);
    		SXSSFCell hssfcell = hssfrow.createCell(index);
    		hssfcell.setCellStyle(this.unlockedCellStyle);
			if(cellField.isExportReadOnly() && StringUtils.isNotEmpty(sheetInfo.getProtectPasswd())) {
				hssfcell.setCellStyle(lockedCellStyle);
			}
			hssfcell.setCellValue(MapUtils.getString(rowData, cellField.getName(), ""));

    	}
	}

	public void writeExcelRecords( List<Map<String,String>> dataList, String sheetName,List<String> fieldList){
		SXSSFSheetInfo  sheetInfo = getSheetInfo(sheetName);
		for(int index = 0 ; index < dataList.size() ; ++ index){
			SXSSFRow hssfrow = sheetInfo.getSheet().createRow(index + 1);
			for(int cellIndex = 0;cellIndex < fieldList.size();++ cellIndex) {
				fieldList.get(cellIndex);
				SXSSFCell hssfcell = hssfrow.createCell(cellIndex);
				hssfcell.setCellValue(MapUtils.getString(dataList.get(index), fieldList.get(cellIndex), ""));
			}
		}
	}

	/**
	 * <AUTHOR>  2025/4/21 11:39
	 * @description: 写入excel记录
	 * @param dataList 将写入的数据
	 * @param sheetName sheet名称
	 * @param fieldMap 字段map，key为第几列，从0开始，value为第几列的key，解决不能写入中文表头顺序问题
	 */
	public void writeExcelRecords( List<Map<String,Object>> dataList, String sheetName,Map<Integer,String> fieldMap){
		SXSSFSheetInfo  sheetInfo = getSheetInfo(sheetName);
		for(int index = 0 ; index < dataList.size() ; ++ index){
			SXSSFRow hssfrow = sheetInfo.getSheet().createRow(index + 1);
			for(int cellIndex = 0;cellIndex < fieldMap.size();++ cellIndex) {
				SXSSFCell hssfcell = hssfrow.createCell(cellIndex);
				hssfcell.setCellValue(MapUtils.getString(dataList.get(index), fieldMap.get(cellIndex), ""));
			}
		}
	}

	/**
     * 写入Excel，超过最大行，重新开一个sheet
     *
     * @param recordIndex 写入第几行数据
     * @param rowData 需要写入每行数据
     * @param sheetName 页签名称
     * @throws IOException
     */
    public void writeExcelRecords(int recordIndex, Collection<Object> rowData, String sheetName){

		int sheetIndex = recordIndex / maxRow;
    	if(sheetIndex > 0 ) sheetName = sheetName + sheetIndex;

		SXSSFSheetInfo  sheetInfo = getSheetInfo(sheetName);
    	SXSSFRow row = sheetInfo.getSheet().createRow(recordIndex % maxRow);
		int i = 0;
    	for (Object value : rowData) {
			SXSSFCell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
			i++;
			if (value != null) {
				if (value instanceof String) {
					cell.setCellValue((String) value);
				} else if (value instanceof Long) {
					cell.setCellValue((Long) value);
				} else if (value instanceof Double) {
					cell.setCellValue((Double) value);
				} else if (value instanceof Float) {
					cell.setCellValue((Float) value);
				} else if (value instanceof BigDecimal) {
					cell.setCellValue(((BigDecimal) value).doubleValue());
				} else {
					cell.setCellValue(value.toString());
				}
			} else {
				cell.setCellValue("");
			}
		}
	}

	/**
     * 写入Excel表头
     *
     * @param fieldList 需要写入每行数据
     * @param sheetName 页签名称
     * @throws IOException
     */
    public void writeExcelHead(List<ExcelCellField> fieldList,String sheetName,String protectPasswd){
    	SXSSFSheetInfo  sheetInfo = getSheetInfo(sheetName,protectPasswd,fieldList);
        SXSSFRow hssfrow = sheetInfo.getSheet().createRow(0);
        int index = 0;

		for (ExcelCellField tqf : sheetInfo.getFieldList()) {
        	String header = tqf.getTitle() == null ? "" : tqf.getTitle();
        	boolean required = tqf.isRequired();

			SXSSFCell hssfcell = hssfrow.createCell(index);

			if (required) {
            	hssfcell.setCellStyle(this.cellStyle);
			}

			CellStyle cellStyle = hssfcell.getCellStyle();
            cellStyle.setLocked(true);

			if(tqf.isExportHide() && StringUtils.isNotEmpty(sheetInfo.getProtectPasswd())) {
            	cellStyle.setLocked(false);
			}
        	hssfcell.setCellStyle(cellStyle);
            hssfcell.setCellValue(header);
            index ++;
        }
    }

	public void writeExcelHead(List<String> fieldList,String sheetName){
		SXSSFSheetInfo  sheetInfo = getSheetInfo(sheetName);
		SXSSFRow hssfrow = sheetInfo.getSheet().createRow(0);
		int index = 0;

		for (String filed : fieldList) {
			SXSSFCell hssfcell = hssfrow.createCell(index);
			hssfcell.setCellValue(filed);
			index ++;
		}
	}

	private synchronized SXSSFSheetInfo getSheetInfo(String sheetName,String protectPasswd,List<ExcelCellField> fieldList) {
    	if(StringUtils.isEmpty(sheetName)) {
    		sheetName = "sheet1";
    	}
    	SXSSFSheetInfo  sheetInfo = this.sheetMap.get(sheetName);
    	if(sheetInfo == null) {
    		sheetInfo = new SXSSFSheetInfo(sheetName,protectPasswd,fieldList);
    		this.sheetMap.put(sheetName, sheetInfo);
    	}
    	return sheetInfo;
    }

	private synchronized SXSSFSheetInfo getSheetInfo(String sheetName) {
    	SXSSFSheetInfo  sheetInfo = this.sheetMap.get(sheetName);
    	if(sheetInfo == null) {
    		sheetInfo = new SXSSFSheetInfo(sheetName,null,null);
    		this.sheetMap.put(sheetName, sheetInfo);
    	}
    	return sheetInfo;
    }

	/**
	 *
     * sheet排序
     *
	 * @Title: setSheetNo
     * @param sheetName
     * @param sheetNo void
     * @throws
     */
    public void setSheetNo(String sheetName,Integer sheetNo) {
    	try {
    		if(this.sheetMap.containsKey(sheetName)) {
            	this.wb.setSheetOrder(sheetName, sheetNo);
    		}
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
    }


	/**
	 * @return the withTemplate
	 */
	public boolean isWithTemplate() {
		return withTemplate;
	}

	/**
     * 写入自定义流
     *
     * @param out
     * @throws IOException
     */
    public void writeToOutputStream(OutputStream out) throws IOException {
        if(out==null){
            throw new NullPointerException("参数不能为空！");
		}
		try {
			// 在写入前应用水印
			if (watermarkConfig != null) {
				ExcelWatermarkUtil.addWatermark(this.wb, watermarkConfig);
			}

            this.wb.write(out);
       	   /**
       	    * 清理Excel时产生的poi-sxssf-sheet*.xml临时文件
       	    */
       	   this.wb.dispose();
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
    }


	/**
	 *
	 * @Title: write
     * @Description: 写入文件
	 * @param: @throws IOException
	 * @return: void
     * @throws
     */
    public long write() throws IOException {
        OutputStream outputStream = null;
        try {
     	   File file = FileUtils.getFile(NaasConfig.getProfile() + this.filePath);
            if (!file.getParentFile().exists()) {
         	   file.getParentFile().mkdirs();
     	   }
     	   if (!file.exists()) {
			   boolean newFile = file.createNewFile();
			   if (!newFile) {
				   log.error("文件创建失败：" + file.getPath());
			   }
		   }

			outputStream = FileUtils.openOutputStream(file);

			// 在写入前应用水印
			if (watermarkConfig != null) {
				ExcelWatermarkUtil.addWatermark(this.wb, watermarkConfig);
			}

           this.wb.write(outputStream);
           return file.length();
        } finally {
        	try {
          	   /**
          	    * 清理Excel时产生的poi-sxssf-sheet*.xml临时文件
          	    */
          	   	this.wb.dispose();
			} catch (Exception e) {
				log.error(e.getMessage(),e);
			}
     	   if(outputStream != null) {
     	       outputStream.close();
     	   }
        }
    }
	/**
	 * <AUTHOR>  2025/4/28 12:04
	 * @description:  excel表格最大允许列字符为32767，重新设置最大字符为int的最大值
	 * @param
	 * @return
	 */
	public static void excelMaxTextReset(){
		SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
		if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
			Field field;
			try {
				// SpreadsheetVersion.EXCEL2007的_maxTextLength变量
				field = excel2007.getClass().getDeclaredField("_maxTextLength");
				// 关闭反射机制的安全检查，可以提高性能
				field.setAccessible(true);
				// 重新设置这个变量属性值
				field.set(excel2007,Integer.MAX_VALUE);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}

	@Data
    public static class ExcelCellField {
    	 /** 英文字段名 */
        private String name;

        /** 中文字段名 */
        private String title;

		/**
         * 是否必填，用于查询导出之后，将必填项单元格标红
         */
        private boolean required = false;

		/**
         * 隐藏列
         */
        private boolean exportHide = false;

		/**
         * 导出只读
         */
        private boolean exportReadOnly = false;

		/**
         * 导出下拉值
         */
        private String [] dropDownValues;
    }
}
