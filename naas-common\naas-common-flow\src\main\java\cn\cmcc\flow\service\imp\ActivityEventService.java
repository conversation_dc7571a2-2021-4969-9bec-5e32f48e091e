package cn.cmcc.flow.service.imp;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.cmcc.flow.common.enums.RuleTypeEnum;
import cn.cmcc.flow.domain.TFlowInstance;
import cn.cmcc.flow.service.IFlowInstanceService;
import cn.cmcc.flow.service.ITFlowRuleService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.event.ActivitiEvent;
import org.activiti.engine.delegate.event.ActivitiEventType;
import org.activiti.engine.delegate.event.impl.ActivitiActivityEventImpl;
import org.activiti.engine.delegate.event.impl.ActivitiEntityEventImpl;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.ExecutionEntityManager;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.activiti.engine.impl.persistence.entity.VariableInstanceEntity;
import org.activiti.engine.task.IdentityLinkType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.google.common.base.Splitter;

import cn.cmcc.common.context.SecurityUtils;
import cn.cmcc.common.datasource.mapper.CommonMapper;
import cn.cmcc.common.nassert.Assert;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.spring.SpringUtils;
import cn.cmcc.flow.common.constants.BpmnConstant;
import cn.cmcc.flow.common.constants.FlowConstants;
import cn.cmcc.flow.common.enums.CountersignEnum;
import cn.cmcc.flow.common.enums.FlowSequenceEnum;
import cn.cmcc.flow.common.event.TaskCreatedDispatchEvent;
import cn.cmcc.flow.domain.TFlowDefInfo;
import cn.cmcc.flow.pojo.bo.DynamicCandidateConditionBO;
import cn.cmcc.flow.pojo.bo.FlowSeqContext;
import cn.cmcc.flow.pojo.bo.ThreadFlowNodeSeq;
import cn.cmcc.flow.pojo.bo.ThreadFlowNodeSeq.ActivitiEventNotify;
import cn.cmcc.flow.pojo.bo.ThreadFlowNodeSeq.NextTask;
import cn.cmcc.flow.pojo.dto.CompleteTaskInfo;
import cn.cmcc.flow.pojo.dto.NotifyMessageDto;
import cn.cmcc.flow.service.IFlowDefInfoService;
import cn.cmcc.kernel.api.dto.CommonQueryDto;
import cn.cmcc.kernel.api.service.RemoteDbQueryService;

import static cn.cmcc.common.utils.StringUtils.EMPTY;
/**
 * activity 事件处理
 * <AUTHOR>
 *
 */

@Component
@Slf4j
public class ActivityEventService {


    @DubboReference
    private RemoteDbQueryService remoteDbQueryService;

    @Autowired
	private CommonMapper commonMapper;

	@Autowired
	private ApplicationEventPublisher applicationEventPublisher;

	@Autowired
	private ITFlowRuleService tFlowRuleService;

	/**
     * 节点开始执行
     * @param event
     */
    public void activityStartedDeal(ActivitiEvent event) {
    	RuntimeService runtimeService = SpringUtils.getBean(RuntimeService.class);
    	IFlowDefInfoService flowDefInfoService = SpringUtils.getBean(IFlowDefInfoService.class);
		IFlowInstanceService flowInstanceService = SpringUtils.getBean(IFlowInstanceService.class);

    	if (event instanceof ActivitiActivityEventImpl
                && ((ActivitiActivityEventImpl) event).getActivityType().equals(BpmnConstant.NODE_USER_TASK)) {
			log.info("一个任务开始执行,info:{},ExecutionId:{},type:{},BehaviorClass:{}", ((ActivitiActivityEventImpl) event).getActivityName(), (event).getExecutionId(), ((ActivitiActivityEventImpl) event).getActivityType(), ((ActivitiActivityEventImpl) event).getBehaviorClass());
            ActivitiActivityEventImpl activity = (ActivitiActivityEventImpl) event;
            String nodeId = activity.getActivityId();

            /**
             * 会签只需要进入一次即可
             */
			if (FlowSeqContext.get().isCountersign()
            		|| nodeId.equals(FlowSeqContext.get().getNodeId())) {
            	return;
            }

            String procInstId = activity.getProcessInstanceId();
            String executionId = activity.getExecutionId();

            TFlowDefInfo flowDefInfo = flowDefInfoService.getFlowDefInfoByNodeIdProcKey(nodeId,getProcDefKey(activity.getProcessDefinitionId()));
            Assert.notNull(flowDefInfo, "未查询到节点配置信息");
			boolean isDynamicCandidate = flowDefInfo.getDynamicCandidate() != null && flowDefInfo.getDynamicCandidate();
        	String multiIns = flowDefInfo.getMultiInstance();
        	String fixedOptions = flowDefInfo.getFixedOptions();
        	if (!isDynamicCandidate) {
    			FlowSeqContext.setCountersign(CountersignEnum.isCountersignOpen(multiIns));
        		if (FlowSeqContext.get().isCountersign()) {
					//会签，限定只按组会签
					log.info("=======flowDefInfo.getMultiCollectionAssignees()============" + flowDefInfo.getMultiCollectionAssignees());
                    if (StringUtils.isNotEmpty(flowDefInfo.getMultiCollectionAssignees())) {
                        runtimeService.setVariable(executionId, BpmnConstant.PREFIX_USERTASK_MULTIPLE_CANDIDATES + nodeId, Splitter.on(",").splitToList(flowDefInfo.getMultiCollectionAssignees()));
                    }

                    if (StringUtils.isNotEmpty(fixedOptions)) {
                    	for(String seq: fixedOptions.split(",")) {
                            runtimeService.setVariable(executionId, BpmnConstant.NR_OF_COMPLETED_INS_VAR + seq , 0);
                    	}
					}
                }

        		List<String> assignUserList =   FlowSeqContext.get().getAssignUserList();
				if(CollectionUtils.isNotEmpty(assignUserList)){
					runtimeService.setVariable(executionId, BpmnConstant.PREFIX_DYNAMIC_CANDIDATE_USER + nodeId, assignUserList);
				}else if (StringUtils.isEmpty(flowDefInfo.getNodeUserId())
						&& StringUtils.isEmpty(flowDefInfo.getNodeUserGroupid())) {
        			//页面未配置处理人，默认使用流程创建用户,当前登录处理用户
					TFlowInstance flowInstance = flowInstanceService.getFlowInstanceByProcInsId(procInstId);
					if(flowInstance == null){
						runtimeService.setVariable(executionId, BpmnConstant.PREFIX_DYNAMIC_CANDIDATE_USER + nodeId, String.valueOf(SecurityUtils.getUserId()));
					}else{
						runtimeService.setVariable(executionId, BpmnConstant.PREFIX_DYNAMIC_CANDIDATE_USER + nodeId, String.valueOf(flowInstance.getCreateUser()));
					}
        		}
			}else {
				//动态指派
				this.dynamicCandidateGroupUser(runtimeService,executionId,nodeId,procInstId,flowDefInfo);
			}

			/**
			 * 驳回的目标节点是并行节点的起始节点，需要把其他的并行节点任务删除
			 */
        	if(FlowSequenceEnum.BH.getCode().equals(FlowSeqContext.get().getSeq()) && flowDefInfo.isParallelFork()) {
        		CommandContext commandContext = Context.getCommandContext();
        		List<TaskEntity> teList = commandContext.getTaskEntityManager().findTasksByProcessInstanceId(procInstId);
        		ExecutionEntityManager executionEntityManager = commandContext.getExecutionEntityManager();
        		Collection<ExecutionEntity> inactiveExecutionEntities = executionEntityManager.findInactiveExecutionsByProcessInstanceId(procInstId);
        		teList.forEach(t->{
            		if(!t.getTaskDefinitionKey().equals(nodeId) && !t.getId().equals(FlowSeqContext.get().getTaskId()) ) {
            			List<VariableInstanceEntity> variableInstanceEntities = commandContext.getVariableInstanceEntityManager().findVariableInstancesByExecutionId(t.getExecutionId());
            			if(CollectionUtils.isNotEmpty(variableInstanceEntities)) {
            				variableInstanceEntities.forEach(commandContext.getVariableInstanceEntityManager()::delete);
            			}
            			commandContext.getTaskEntityManager().deleteTask(t, "并行节点删除", false,false);
            			executionEntityManager.delete(t.getExecution());
            		}
            	});
        		if(CollectionUtils.isNotEmpty(inactiveExecutionEntities)) {
        			List<VariableInstanceEntity> variableInstanceEntities = commandContext.getVariableInstanceEntityManager()
        					.findVariableInstancesByExecutionIds(inactiveExecutionEntities.stream()
        							.map(ExecutionEntity::getId).collect(Collectors.toSet()));
        			if(CollectionUtils.isNotEmpty(variableInstanceEntities)) {
        				variableInstanceEntities.forEach(commandContext.getVariableInstanceEntityManager()::delete);
        			}
					inactiveExecutionEntities.forEach(executionEntityManager::delete);
        		}
        	}
        }

    }

    /**
	 * 获取动态角色组
	 * @param procInstId
	 */
	public void dynamicCandidateGroupUser(RuntimeService runtimeService,String executionId,String taskDefId,String procInstId,TFlowDefInfo flowDefInfo) {
    	String multiIns =flowDefInfo.getMultiInstance();
    	String fixedOptions = flowDefInfo.getFixedOptions();

		boolean isMatch = false;
		List<DynamicCandidateConditionBO> dynamicCandidateConditionBOs = flowDefInfo.getDynamicCandidateConditionBOList();

		if (CollectionUtils.isNotEmpty(dynamicCandidateConditionBOs)) {
			for (DynamicCandidateConditionBO dynamicCandidateConditionBO : dynamicCandidateConditionBOs) {
				CommonQueryDto commonQueryResult = remoteDbQueryService.getByCode(dynamicCandidateConditionBO.getConditionCode());
				if(commonQueryResult == null) {
					throw new RuntimeException("通用查询异常");
				}
				String sql = String.format(commonQueryResult.getSql(),procInstId);

				Map<String, Object> resMap = commonMapper.selectMap(sql);
				if (resMap != null && !resMap.isEmpty()) {
					String candidates = dynamicCandidateConditionBO.getCandidates();
					String[] userGroups = candidates.split(";");
					if (CountersignEnum.isCountersignOpen(multiIns)) {
                        runtimeService.setVariable(executionId, BpmnConstant.PREFIX_USERTASK_MULTIPLE_CANDIDATES + taskDefId, Splitter.on(",").splitToList(userGroups[1]));

                        if (StringUtils.isNotEmpty(fixedOptions)) {
                        	for(String seq: fixedOptions.split(",")) {
                                runtimeService.setVariable(executionId, BpmnConstant.NR_OF_COMPLETED_INS_VAR + seq , 0);
                        	}
    					}

    					runtimeService.setVariable(executionId, FlowConstants.DYNAMIC_CANDIDATE_CONDITION_CODE, dynamicCandidateConditionBO.getConditionCode());

					}else {
						 runtimeService.setVariable(executionId, BpmnConstant.PREFIX_DYNAMIC_CANDIDATE_USER + taskDefId, userGroups[0]);
						 runtimeService.setVariable(executionId, BpmnConstant.PREFIX_DYNAMIC_CANDIDATE_GROUP + taskDefId, userGroups[1]);
					}


					isMatch = true;
					break;
				}


			}
		}

		if (!isMatch) {
			log.info("动态角色开启，未匹配到动态处理用户，使用默认处理角色!");
			//不匹配,使用默认角色
			String nodeUser = flowDefInfo.getNodeUserId();
			String nodeGroup = flowDefInfo.getNodeUserGroupid();
			if (CountersignEnum.isCountersignOpen(multiIns)) {
                runtimeService.setVariable(executionId, BpmnConstant.PREFIX_USERTASK_MULTIPLE_CANDIDATES + taskDefId, Splitter.on(",").splitToList(nodeGroup));

				if (StringUtils.isNotEmpty(fixedOptions)) {
                	for(String seq: fixedOptions.split(",")) {
                        runtimeService.setVariable(executionId, BpmnConstant.NR_OF_COMPLETED_INS_VAR + seq , 0);
                	}
				}

				runtimeService.setVariable(executionId, FlowConstants.DYNAMIC_CANDIDATE_CONDITION_CODE, FlowConstants.DYNAMIC_CANDIDATE_CONDITION_DEFAULT_CODE);

			}else {
				runtimeService.setVariable(executionId, BpmnConstant.PREFIX_DYNAMIC_CANDIDATE_USER + taskDefId, nodeUser);
				runtimeService.setVariable(executionId, BpmnConstant.PREFIX_DYNAMIC_CANDIDATE_GROUP + taskDefId, nodeGroup);
			}
		}

	}

	/**
     * activiti事件任务通知
     * @param event
     */
    public void activitiEventNotifiy(ActivitiEvent event) {

		String nodeId = EMPTY, procDefId = EMPTY, taskId = EMPTY, processInstanceId = EMPTY, executionId = EMPTY;
    	if (event instanceof ActivitiActivityEventImpl
    			 && ((ActivitiActivityEventImpl) event).getActivityType().equals(BpmnConstant.NODE_USER_TASK)) {
    		ActivitiActivityEventImpl activity = (ActivitiActivityEventImpl) event;
    		nodeId = activity.getActivityId();
    		procDefId = activity.getProcessDefinitionId();
            processInstanceId = activity.getProcessInstanceId();
            executionId = activity.getExecutionId();
    	}else if(event instanceof ActivitiEntityEventImpl) {
    		ActivitiEntityEventImpl activity = (ActivitiEntityEventImpl) event;
            TaskEntityImpl taskEntity = (TaskEntityImpl) activity.getEntity();
            nodeId = taskEntity.getTaskDefinitionKey();
            procDefId = taskEntity.getProcessDefinitionId();
            taskId = taskEntity.getId();
            processInstanceId = activity.getProcessInstanceId();
            executionId = activity.getExecutionId();
            if(event.getType().equals(ActivitiEventType.TASK_CREATED)) {
            	assignUser(taskEntity);
            }
    	}

		if (StringUtils.isAnyEmpty(processInstanceId, procDefId, nodeId)) {
			return;
		}

		RuntimeService runtimeService = SpringUtils.getBean(RuntimeService.class);
    	IFlowDefInfoService flowDefInfoService = SpringUtils.getBean(IFlowDefInfoService.class);

		TFlowDefInfo flowDefInfo = flowDefInfoService.getFlowDefInfoByNodeIdProcKey(nodeId,getProcDefKey(procDefId));
		Assert.notNull(flowDefInfo, "未查询到节点配置信息");
		if(Boolean.TRUE.equals(flowDefInfo.getTransparentTransmission())) {
    		String seq = FlowSeqContext.get().getSeq().equals("-1") ? FlowSequenceEnum.PASS.getCode() : FlowSeqContext.get().getSeq();
    		List<String> seqList = Arrays.asList(flowDefInfo.getFixedOptions().split(","));
    		if(!seqList.contains(seq)) seq = seqList.get(0);
    		runtimeService.setVariable(executionId, "var" + nodeId, seq);
		}

		ThreadFlowNodeSeq threadFlowNodeSeq = FlowSeqContext.get();
		boolean isCountersignTaskCreated = nodeId.equals(FlowSeqContext.get().getNodeId()) && event.getType().equals(ActivitiEventType.TASK_CREATED);
		if(!isCountersignTaskCreated) {
			threadFlowNodeSeq.getEventList().add(new ActivitiEventNotify(nodeId, taskId, event.getType()));
		}

		switch (event.getType()) {
		case ACTIVITY_COMPLETED:
			threadFlowNodeSeq.setNodeCompleted(true);
			if(threadFlowNodeSeq.isFlowVerification() &&
					CountersignEnum.isCountersignOpen(flowDefInfo.getMultiInstance())){
				countersignCheckFlow(threadFlowNodeSeq);
			}
			break;
		case TASK_CREATED:
			if(!isCountersignTaskCreated) {
				threadFlowNodeSeq.addNextTask(new NextTask(nodeId,taskId));
				NotifyMessageDto upstream = new NotifyMessageDto();
				upstream.setOrderNo(threadFlowNodeSeq.getOrderNo());
				upstream.setNodeId(threadFlowNodeSeq.getNodeId());
				NotifyMessageDto completed = new NotifyMessageDto();
				completed.setOrderNo(threadFlowNodeSeq.getOrderNo());
				completed.setNodeId(nodeId);
				CompleteTaskInfo completeTaskInfo = new CompleteTaskInfo(upstream, completed);
				completeTaskInfo.setContextValue(threadFlowNodeSeq);
				applicationEventPublisher.publishEvent(new TaskCreatedDispatchEvent(completeTaskInfo));
			}
			break;
		case TASK_COMPLETED:
			threadFlowNodeSeq.removeTask(taskId);
			break;
		default:
			break;
		}
    }

    /**
	 *
	 * @Title: assignUser
     * @Description: 指派用户
	 * @param: @param event
	 * @return: void
     * @throws
     */
    private void assignUser(TaskEntityImpl task) {
        try {
            ThreadFlowNodeSeq threadFlowNodeSeq = FlowSeqContext.get();
            if(CollectionUtils.isNotEmpty(threadFlowNodeSeq.getAssignUserList())) {
            	TaskService taskService = SpringUtils.getBean(TaskService.class);
                String taskId = task.getId();
            	threadFlowNodeSeq.getAssignUserList().forEach(userId->{
            		taskService.deleteCandidateUser(taskId, userId);
                    taskService.addUserIdentityLink(taskId, userId, IdentityLinkType.CANDIDATE);
            	});
            }
        } catch (Exception e) {
			log.error("assignUser error:", e);
        }
    }

    /**
	 *
     * 获取流程key
     *
	 * @Title: getProcDefKey
     * @param procDefId
     * @return String
     * @throws
     */
    private String getProcDefKey(String procDefId) {
    	if(StringUtils.isEmpty(procDefId)) return StringUtils.EMPTY;
    	int index = procDefId.indexOf(":");
    	if(index > 0) {
    		return procDefId.substring(0,index);
    	}
    	return  procDefId;
    }

	/**
	 * 会签任务完成流转校验
	 *
	 *@param : threadFlowNodeSeq
	 *@return: void
	 *@throws:
	**/
	private void countersignCheckFlow(ThreadFlowNodeSeq threadFlowNodeSeq){
		threadFlowNodeSeq.getVerificationExtendParam().put("orderNo", threadFlowNodeSeq.getOrderNo());
		threadFlowNodeSeq.getVerificationExtendParam().put("siteIdCmcc", threadFlowNodeSeq.getOrderNo());
		threadFlowNodeSeq.getVerificationExtendParam().put("nodeId", threadFlowNodeSeq.getNodeId());
		threadFlowNodeSeq.getVerificationExtendParam().put("userId", threadFlowNodeSeq.getLoginUser().getUserId());
		threadFlowNodeSeq.getVerificationExtendParam().put("taskId", threadFlowNodeSeq.getTaskId());
		threadFlowNodeSeq.getVerificationExtendParam().put("sequenceCode", threadFlowNodeSeq.getSeq());
		tFlowRuleService.flowCheck(tFlowRuleService.qryFlowRules(threadFlowNodeSeq.getNodeId(), threadFlowNodeSeq.getSeq(), EMPTY, RuleTypeEnum.RULE.getCode()), threadFlowNodeSeq.getVerificationExtendParam());
	}
}
