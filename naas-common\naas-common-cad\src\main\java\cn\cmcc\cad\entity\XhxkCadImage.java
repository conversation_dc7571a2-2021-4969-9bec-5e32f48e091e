package cn.cmcc.cad.entity;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import cn.cmcc.cad.service.ICadService;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.core.util.SerializeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class XhxkCadImage implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private String file;
	private List<XhxkCadBaseEntity> entities;
	private List<XhxkCadBlockEntity> blockEntities;
	private int width;
	private int height;
	private XhxkCadPoint minPoint;
	private XhxkCadPoint maxPoint;
	private AjaxResult status;

	public XhxkCadImage(AjaxResult status) {
		super();
		this.status = status;
	}

	public XhxkCadImage(String file, List<XhxkCadBaseEntity> entities, List<XhxkCadBlockEntity> blockEntities, int width, int height, XhxkCadPoint minPoint, XhxkCadPoint maxPoint) {
		super();
		this.file = file;
		this.entities = entities;
		this.blockEntities = blockEntities;
		this.width = width;
		this.height = height;
		this.minPoint = minPoint;
		this.maxPoint = maxPoint;
		this.status = AjaxResult.success();
	}

	public XhxkCadImage deepCopy() {
		XhxkCadImage image = null;
		try {
			/*
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(baos);
			oos.writeObject(this);

			ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
			ObjectInputStream ois = new ObjectInputStream(bais);
			image = (XhxkCadImage) ois.readObject();*/
			image = SerializeUtil.clone(this);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
		return image;
	}

	/**
	 * 
	 * @param scale
	 * @param deviationPoint - 缩放后的最小坐标
	 */
	public void transfer(ICadService cadService,double scale,XhxkCadPoint deviationPoint) {
		this.minPoint = cadService.transfer(this.minPoint, scale, deviationPoint);
		this.maxPoint = cadService.transfer(this.maxPoint, scale, deviationPoint);
		this.width = (int) (this.width*scale);
		this.height = (int) (this.height*scale);
		
		List<XhxkCadBaseEntity> transEntities = new LinkedList<XhxkCadBaseEntity>();
		for(XhxkCadBaseEntity entity:entities) {
			entity.transfer(cadService,scale, deviationPoint);
			transEntities.add(entity);
		}
		this.entities = transEntities;
	}
}
