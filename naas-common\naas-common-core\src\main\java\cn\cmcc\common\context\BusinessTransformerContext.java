package cn.cmcc.common.context;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: niuyh
 * @date: 2024/1/15
 */
public class BusinessTransformerContext {
    public static final String TABLE_SUFFIX_SEP = "_";


    public static class Common {
        public static ThreadLocal<Map> additionQueryParam = InheritableThreadLocal.withInitial(HashMap::new);
        @SuppressWarnings("ReturnOfNull")
        public static ThreadLocal<String> specificTransformerKey = InheritableThreadLocal.withInitial(() -> null);
    }

    public static class LnChangeInterfaces {
        public static final String STUB_STAGE = "STUB_STAGE";
        @SuppressWarnings("ReturnOfNull")
        public static ThreadLocal<String> handleOrderType = InheritableThreadLocal.withInitial(() -> null);
        @SuppressWarnings("ReturnOfNull")
        public static ThreadLocal<String> lnChangeResourceType = InheritableThreadLocal.withInitial(() -> null);
        @SuppressWarnings("ReturnOfNull")
        public static ThreadLocal<String> resourceCreationNodeId = InheritableThreadLocal.withInitial(() -> null);

        public static void clearLnChangeInterfacesContext() {
            Common.specificTransformerKey.remove();
            lnChangeResourceType.remove();
            handleOrderType.remove();
        }

        public static String getBusinessCodeWithContext(String businessType) {
            String orderType = handleOrderType.get();
            String suffix = TABLE_SUFFIX_SEP + orderType;
            /**
             @see  cn.cmcc.plan.enums.OrderType
             */
            return ("ADA".equals(orderType) || "Back".equals(orderType)) ?
                    (businessType != null && !businessType.endsWith(suffix) ? (businessType + suffix) : businessType) :
                    businessType;
        }

    }

    public static class PlanOrders {
        public static final ThreadLocal<Map<String, List<Map<String, Object>>>> orderDetail = InheritableThreadLocal.withInitial(HashMap::new);
    }
}
