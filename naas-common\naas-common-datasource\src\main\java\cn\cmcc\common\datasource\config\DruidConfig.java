package cn.cmcc.common.datasource.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandler;
import org.apache.seata.rm.datasource.DataSourceProxy;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ResourceLoader;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;

import cn.cmcc.common.datasource.config.properties.DruidProperties;
import cn.cmcc.common.datasource.enums.DataSourceType;
import cn.cmcc.common.utils.spring.SpringUtils;
import lombok.Data;

/**
 * druid 配置多数据源
 * 
 * <AUTHOR>
 */
@Data
@Configuration
public class DruidConfig
{

    @Value("${seata.enabled:false}")
    private boolean enabledSeata;

	private final MybatisPlusProperties properties;
	
	private final Interceptor[] interceptors;

    private final TypeHandler[] typeHandlers;

    private final ResourceLoader resourceLoader;

    private final DatabaseIdProvider databaseIdProvider;

	public DruidConfig(MybatisPlusProperties properties,
			 ObjectProvider<Interceptor[]> interceptorsProvider,
             ObjectProvider<TypeHandler[]> typeHandlersProvider,
             ResourceLoader resourceLoader,
             ObjectProvider<DatabaseIdProvider> databaseIdProvider) {
		 	this.properties = properties;
		 	this.interceptors = interceptorsProvider.getIfAvailable();
	        this.typeHandlers = typeHandlersProvider.getIfAvailable();
	        this.resourceLoader = resourceLoader;
	        this.databaseIdProvider = databaseIdProvider.getIfAvailable();
	}
	
    @Bean(name = "masterDataSource")
    @ConfigurationProperties("spring.datasource.druid.master")
    public DataSource masterDataSource(DruidProperties druidProperties)
    {
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        return druidProperties.dataSource(dataSource);
    }

    @Bean
    @ConfigurationProperties("spring.datasource.druid.slave")
    @ConditionalOnProperty(prefix = "spring.datasource.druid.slave", name = "enabled", havingValue = "true")
    public DataSource slaveDataSource(DruidProperties druidProperties)
    {
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        return druidProperties.dataSource(dataSource);
    }

    @Bean(name = "dynamicDataSource")
    @Primary
    public DynamicDataSource dynamicDataSource()
    {
    	DataSource masterDataSource = SpringUtils.getBean("masterDataSource");
        DataSource targetDataSource = enabledSeata ? new DataSourceProxy(masterDataSource): masterDataSource;

        DynamicDataSource dynamicDataSource = DynamicDataSource.getInstance();
    	dynamicDataSource.setDefaultTargetDataSource(targetDataSource);
    	
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceType.MASTER.name(), targetDataSource);
        try {
        	targetDataSources.put(DataSourceType.SLAVE.name(), SpringUtils.getBean("slaveDataSource"));
		} catch (Exception e) {
		}
    	dynamicDataSource.setTargetDataSources(targetDataSources);
    	
        return dynamicDataSource;
    }
    
    @Bean(name = "dynamicSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory() throws Exception {
    	MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
    	factory.setDataSource(dynamicDataSource());
        if (StringUtils.hasText(this.properties.getConfigLocation())) {
            factory.setConfigLocation(this.resourceLoader.getResource(this.properties.getConfigLocation()));
        }
        if (this.properties.getConfigurationProperties() != null) {
            factory.setConfigurationProperties(this.properties.getConfigurationProperties());
        }
        if (!ObjectUtils.isEmpty(this.interceptors)) {
            factory.setPlugins(this.interceptors);
        }
        if (this.databaseIdProvider != null) {
            factory.setDatabaseIdProvider(this.databaseIdProvider);
        }
        if (StringUtils.hasLength(this.properties.getTypeAliasesPackage())) {
            factory.setTypeAliasesPackage(this.properties.getTypeAliasesPackage());
        }
        if (this.properties.getTypeAliasesSuperType() != null) {
            factory.setTypeAliasesSuperType(this.properties.getTypeAliasesSuperType());
        }
        if (StringUtils.hasLength(this.properties.getTypeHandlersPackage())) {
            factory.setTypeHandlersPackage(this.properties.getTypeHandlersPackage());
        }
        if (!ObjectUtils.isEmpty(this.typeHandlers)) {
            factory.setTypeHandlers(this.typeHandlers);
        }
        if (!ObjectUtils.isEmpty(this.properties.resolveMapperLocations())) {
            factory.setMapperLocations(this.properties.resolveMapperLocations());
        }

        factory.setConfiguration(this.properties.getConfiguration());
        
        GlobalConfig globalConfig  = GlobalConfigUtils.defaults();
        if (!ObjectUtils.isEmpty(this.properties.getGlobalConfig())) {
        	globalConfig = this.properties.getGlobalConfig();
		}
        
        /**
         * 自定义公共字段自动注入
         */
        globalConfig.setMetaObjectHandler(new CustomMetaObjectHandler());
        
        factory.setGlobalConfig(globalConfig);

        return factory.getObject();
    }
    

    @Bean
    public TransactionTemplate  transactionTemplate(PlatformTransactionManager transactionManager) {
    	TransactionTemplate requiredTransactionTemplate = new TransactionTemplate(transactionManager);
        requiredTransactionTemplate.setPropagationBehavior(TransactionTemplate.PROPAGATION_REQUIRED);
        return requiredTransactionTemplate;
	}
    
    /**
     * 
     * @param transactionManager
     * @return
     * @version v1.0
     * @date 2023年5月29日上午9:14:58
     */
    @Bean(name = "newTransactionTemplate")
    public TransactionTemplate  requiresNewTransactionTemplate(PlatformTransactionManager transactionManager) {
    	TransactionTemplate requiredTransactionTemplate = new TransactionTemplate(transactionManager);
        requiredTransactionTemplate.setPropagationBehavior(TransactionTemplate.PROPAGATION_REQUIRES_NEW);
        return requiredTransactionTemplate;
	}
}
