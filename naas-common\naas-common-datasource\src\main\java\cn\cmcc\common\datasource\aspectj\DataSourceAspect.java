package cn.cmcc.common.datasource.aspectj;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.cache.service.CacheService;
import cn.cmcc.common.datasource.annotation.DS;
import cn.cmcc.common.datasource.config.DynamicDataSource;
import cn.cmcc.common.datasource.config.DynamicDataSourceContextHolder;
import cn.cmcc.common.datasource.enums.DataSourceType;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.spring.SpringUtils;
import cn.cmcc.common.web.page.TableSupport;
import lombok.extern.slf4j.Slf4j;

/**
 * 多数据源处理
 * 
 * <AUTHOR>
 */
@Aspect
@Slf4j
@Order(1)
@Component
public class DataSourceAspect
{
    protected Logger logger = LoggerFactory.getLogger(getClass());
    
    private final static List<String> excludeKey = Arrays.asList("userId",TableSupport.PAGE_NUM,TableSupport.PAGE_SIZE);

    @Pointcut("@annotation(cn.cmcc.common.datasource.annotation.DS)"
            + "|| @within(cn.cmcc.common.datasource.annotation.DS)")
    public void dsPointCut()
    {

    }

    @SuppressWarnings("unchecked")
	@Around("dsPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable
    {
    	MethodSignature signature = (MethodSignature) point.getSignature();
        DS dataSource = AnnotationUtils.findAnnotation(signature.getMethod(), DS.class);
        
    	// 方法参数
    	Object[] args = point.getArgs();
    	// 数据源
        String dsName = getDataSource(signature,dataSource,args);

        Object cacheData = args[args.length-1];
    	if( cacheData == null || !(cacheData instanceof Boolean)) {
            return execDbOper(point,dsName);
    	}
    	
    	if(!(Boolean)cacheData) {
    		return execDbOper(point,dsName);
    	}
    	
    	String mastpId = (String)args[1];
    	String cacheKey = mastpId;
    	Map<String, Object> parameterMap = (Map<String, Object>)args[2] ;
    	if(null != parameterMap && !parameterMap.isEmpty()) {
    		 for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
    			 Object value = entry.getValue();
    			 if(!excludeKey.contains(entry.getKey()) &&  value != null) {
    				 Class<?> clazz = value.getClass();
    				 if (clazz.isArray() || Collection.class.isAssignableFrom(clazz) || Map.class.isAssignableFrom(clazz) ){
    				 }else {
    					 String val = value.toString();
    					 if(!"".equals(val)) cacheKey = cacheKey + "_" + val;
    				 }
    			 }
    	     }
    	}
    	
    	CacheService cacheService = SpringUtils.getBean(CacheService.class);
    	
    	Object ret = cacheService.get(cacheKey, CachesEnums.CACHE_DEFAULT.name());
    	if(null == ret) {
    		ret = execDbOper(point,dsName);
    		if (null != ret) {
    			cacheService.set(cacheKey, ret,  CachesEnums.CACHE_DEFAULT.name());
    		}
    	}
    	
    	return ret;
    }
    
    private Object execDbOper(ProceedingJoinPoint point,String dsName) throws Throwable {
    	 if (StringUtils.isNotNull(dsName))
         {
         	if(DynamicDataSource.dynamicTargetDataSources.containsKey(dsName)){
         		log.info("切换数据源：" + dsName);
                 DynamicDataSourceContextHolder.setDataSource(dsName);
         	}else {
         		log.info("动态数据源【" + dsName + "】不存在！");
         	}
         	
         }

         try
         {
             return point.proceed();
         }
         finally
         {
             // 销毁数据源 在执行方法之后
             DynamicDataSourceContextHolder.clearDataSource();
         }
    }

    /**
     * 获取需要切换的数据源
     */
    private String getDataSource(MethodSignature signature ,DS dataSource ,Object[] args)
    {
        if (Objects.nonNull(dataSource))
        {
        	if (!dataSource.useParamDs()) {
				return dataSource.value().name();
			}else {
				if (!Objects.isNull(args)) {
					Object dsName = args[0];
					if (dsName != null && !dsName.equals(DataSourceType.MASTER.name())) {
						return dsName.toString();
					}
				}
			}
        }
        
        DS dataSourceClass = AnnotationUtils.findAnnotation(signature.getDeclaringType(), DS.class) ;
        return dataSourceClass == null ? DataSourceType.MASTER.name() : dataSourceClass.value().name();
    }
}
