/**
 * @Title: MethodParams.java
 * @Package cn.cmcc.common.annotation
 * 
 * <AUTHOR>
 * @date 2021年9月18日
 * @version V1.0
 */
package cn.cmcc.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.METHOD,ElementType.ANNOTATION_TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface MethodParams {
	/**
	 * 字段名
	 */
	String name() default "";

	/**
	 * 字段值
	 */
    String value() default "";
}
