package cn.cmcc.auth.controller;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.cmcc.common.utils.http.HttpClientUtils;
import cn.hutool.core.map.MapUtil;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.CapchaCodeService;
import cn.cmcc.auth.service.SysLoginService;
import cn.cmcc.common.context.SecurityUtils;
import cn.cmcc.common.pojo.LoginBody;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.encryption.AESECBUtil;
import cn.cmcc.common.web.domain.AjaxResult;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class AuthController {

    @Value("${sso.openAdminSSO:true}")
    private Boolean openAdminSSO;
    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private CapchaCodeService capchaCodeService;

    @Autowired
    private CasConfig casConfig;

    @PostMapping("/auth/login")
    public AjaxResult login(@RequestBody LoginBody form) {
        AjaxResult ajr = capchaCodeService.checkCapcha(form);
        if (!ajr.isSuccess()) {
            return ajr;
        }
        return AjaxResult.success(sysLoginService.login(form));
    }

    @PostMapping("/auth/obtain/token")
    public AjaxResult thirdToken(@RequestBody LoginBody loginBody) {
        return AjaxResult.success("登录成功", sysLoginService.login(loginBody));
    }

    /**
     * 单点登录方法
     *
     * @param key 密文
     * @return 结果
     */
    @PostMapping("/auth/ssoLogin")
    public AjaxResult ssoLogin(String userName, String key) {
        Assert.notNull(userName, "用户名不能为空");
        String name = null;
        if (StringUtils.isNotEmpty(key)) {
            Assert.notNull(key, "key不能为空");

            String key2 = "sde@5f98H*^hsff%dfs$r344&df8543*er";
            key = AESECBUtil.decrypt(key.replace(" ", "+"), key2);
            Assert.isTrue("nwom".equalsIgnoreCase(key), "KEY错误");

            name = AESECBUtil.decrypt(userName.replace(" ", "+"), key2);
        }
        if (!openAdminSSO) {
            // TODO: 2024/10/25 <ljh>: 暂不开启，考虑到查询工单信息，流转历史等功能37上都绑定了system，如要启用，则需要改造工单查询SQL
            return AjaxResult.error();
        }
        return AjaxResult.success(sysLoginService.ssoLogin(name == null ? userName : name));
    }

    @DeleteMapping("/auth/logout")
    @SneakyThrows
    public AjaxResult logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            casConfig.logout(request);
            sysLoginService.logout(token);
        }
        return AjaxResult.success();
    }

    /**
     * CAS 登录
     *
     * @param request  请求
     * @param response 响应
     * @return AjaxResult
     */
    @GetMapping(value = "/auth/casLogin")
    public AjaxResult casLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        return casConfig.login(request);
    }

    /**
     * 生成验证码
     */
    @GetMapping("/auth/captchaImage")
    public AjaxResult getCode() throws IOException {
        return capchaCodeService.createCapcha();
    }


}
