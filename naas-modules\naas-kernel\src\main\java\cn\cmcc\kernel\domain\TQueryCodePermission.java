package cn.cmcc.kernel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.cmcc.common.datasource.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 通用查询编码权限表实体类
 * 对应数据库表：t_query_code_permission
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_query_code_permission")
public class TQueryCodePermission extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 查询编码
     */
    private String queryCode;

    /**
     * 权限类型：read-查询，write-修改
     */
    private String permissionType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 构造函数
     */
    public TQueryCodePermission() {
        super();
    }

    /**
     * 构造函数
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     */
    public TQueryCodePermission(String userId, String username, String queryCode, String permissionType) {
        this.userId = userId;
        this.username = username;
        this.queryCode = queryCode;
        this.permissionType = permissionType;
    }

    /**
     * 权限类型常量
     */
    public static class PermissionType {
        public static final String READ = "read";
        public static final String WRITE = "write";
    }
}
