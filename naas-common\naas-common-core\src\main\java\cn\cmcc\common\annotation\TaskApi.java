/**
 * @Title: CallThird.java
 * @Package cn.cmcc.common.annotation
 * 
 * <AUTHOR>
 * @date 2021年9月17日
 * @version V1.0
 */
package cn.cmcc.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import cn.cmcc.common.enums.MsgTypeEnums;
import cn.cmcc.common.enums.TaskApiType;
import cn.cmcc.common.enums.ThirdBusinessType;

@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface TaskApi {

	/**
     * 功能 
     */
    public String name() default "请求三方系统接口";
    
    /**
     * 业务编码
     */
    ThirdBusinessType businessCode() default ThirdBusinessType.UNKNOWE;
    
    /**
     * 版本
     */
    String version() default "通用版本";  
    
    /**
     * 备注
     */
    String remark() default "";  
    
    /**
     * 接口类型
     */
    TaskApiType apiType() default TaskApiType.FLOW_NOTICE;
    
    /**
     * 页面显示派单
     */
    boolean pageDispatch() default true;
    
    /**
     * key的code
     */
    String licensekeyCode() default "";
    
    /**
     * 消息类型
     */
    MsgTypeEnums msgType() default MsgTypeEnums.JSON;
    
    String failCode() default "code";
    String failMsg() default "desc";
}
