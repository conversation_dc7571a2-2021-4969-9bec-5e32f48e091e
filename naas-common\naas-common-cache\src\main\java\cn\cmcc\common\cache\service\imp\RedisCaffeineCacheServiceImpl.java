package cn.cmcc.common.cache.service.imp;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache.ValueWrapper;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.cache.enums.ChannelTopicEnum;
import cn.cmcc.common.cache.listener.RedisPublishMessage;
import cn.cmcc.common.cache.service.CacheService;
import cn.cmcc.common.cache.support.RedisCaffeineCache;

/**
 * 本地缓存 工具类
 *
 * <AUTHOR>
 * 
 **/

@Service
public class RedisCaffeineCacheServiceImpl implements CacheService{
	
	@Autowired
	private CacheManager cacheManager;
	
	@Override
	public void set(String key, Object value) {
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(CachesEnums.CACHE_DEFAULT.name());
		cache.put(key, value);
	}

	@Override
	public void set(String key, Object value,String cacheType) {
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(cacheType);
		cache.put(key, value);
	}

	@Override
	public void set(String key, Object value, long timeout, TimeUnit timeUnit,String cacheType) {
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(cacheType);
		cache.put(key, value,timeout,timeUnit);
	}

	@Override
	public boolean expire(String key, long timeout,String cacheType) {
		return true;
	}

	@Override
	public boolean expire(String key, long timeout, TimeUnit unit,String cacheType) {
		return true;
	}
	
	@Override
	public Object get(final String key) {
		ValueWrapper valueWrapper =  cacheManager.getCache(CachesEnums.CACHE_DEFAULT.name()).get(key);
		if (valueWrapper != null) {
			return valueWrapper.get();
		}
		return null;
	}

	@Override
	public Object get(String key,String cacheType) {
		ValueWrapper valueWrapper =  cacheManager.getCache(cacheType).get(key);
		if (valueWrapper != null) {
			return valueWrapper.get();
		}
		return null;
	}
	
	@Override
	public Object get(final String key,String cacheType,boolean prefix) {
		if(prefix) {
			String keyTemp = key.replace(cacheType + "::", "");
			return this.get(keyTemp, cacheType);
		}else {
			return this.get(key, cacheType);
		}
		
	}

	@Override
	public boolean del(String key,String cacheType) {
		cacheManager.getCache(cacheType).evict(key);
		return true;
	}
	
	@Override
	public boolean clear(String cacheType) {
		cacheManager.getCache(cacheType).evict(null);
		return true;
	}
	
	@Override
	public Boolean hasKey(String key,String cacheType) {
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(cacheType);
		return cache.hasKey(key);
	}
	
	@Override
	public Map<Object, Object> getMap(String cacheType){
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(cacheType);
		return cache.getMap();
	}

	@Override
	public Set<Object> keys(String pattern,String cacheType) {
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(cacheType);
		return cache.keys(pattern);
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public long del(Collection collection,String cacheType) {
		collection.forEach(key->{
			cacheManager.getCache(cacheType).evict(key);
		});
		return 0;
	}
	
	@Override
    public boolean lock(final String key,String cacheType) {
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(cacheType);
		return cache.lock(key);
	}
	
	@Override
	public void redisPublisher(ChannelTopicEnum channelTopicEnum,Object message) {
		RedisCaffeineCache cache = (RedisCaffeineCache) cacheManager.getCache(CachesEnums.CACHE_DEFAULT.name());
		cache.redisPublisher(new RedisPublishMessage(message), channelTopicEnum);
	}
}
