package cn.cmcc.common.utils.file.excel.cellHandle;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;

/**  
 * 
 * <AUTHOR>
 * @date 2024-01-22 12:12:01
*/
public class ExcelImportTemplateWriteHandler implements CellWriteHandler{
	 /**
     * 无参构造
     */
    public ExcelImportTemplateWriteHandler() {
    }
    
  
    /**
     * 在创建单元格之前调用
     */
    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
    }

    /**
     * 在单元格创建后调用
     */
    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
    }

    /**
     * 在单元上的所有操作完成后调用
     */
    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        if(context.getHead()) {
        	/**
        	 * 表头
        	 */
        	WriteCellData<?> cellData = context.getCellDataList().get(0);
        	
        	if(context.getCell().getStringCellValue().contains("*")) {
        		//必填
        		WriteCellStyle headWriteCellStyle = cellData.getOrCreateStyle();
        		//设置表头居中对齐
    	        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
    	        //表头前景设置淡蓝色
    	        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.index);
    	        WriteFont headWriteFont = new WriteFont();
    	        headWriteFont.setBold(true);
    	        headWriteFont.setFontName("宋体");
    	        headWriteFont.setFontHeightInPoints((short) 10);
    	        headWriteFont.setColor(IndexedColors.RED.index);
    	        headWriteCellStyle.setWriteFont(headWriteFont);
        	}else {
        		//必填
       		 	WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
              //设置表头居中对齐
                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
    	        //表头前景设置淡蓝色
                writeCellStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.index);
    	        WriteFont headWriteFont = new WriteFont();
    	        headWriteFont.setBold(true);
    	        headWriteFont.setFontName("宋体");
    	        headWriteFont.setFontHeightInPoints((short) 10);
    	        headWriteFont.setColor(IndexedColors.BLACK.index);
    	        writeCellStyle.setWriteFont(headWriteFont);
        	}
        }else {
        	//必填
        	 WriteCellData<?> cellData = context.getFirstCellData();
             // 这里需要去cellData 获取样式
             // 很重要的一个原因是 WriteCellStyle 和 dataFormatData绑定的 简单的说 比如你加了 DateTimeFormat
             // ，已经将writeCellStyle里面的dataFormatData 改了 如果你自己new了一个WriteCellStyle，可能注解的样式就失效了
             // 然后 getOrCreateStyle 用于返回一个样式，如果为空，则创建一个后返回
             WriteCellStyle contentWriteCellStyle = cellData.getOrCreateStyle();
             // 设置背景颜色白色
             contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
             // 设置垂直居中为居中对齐
             contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
             // 设置左右对齐为靠左对齐
             contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
             // 设置单元格上下左右边框为细边框
             contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
             contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
             contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
             contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
             //创建字体对象
             WriteFont contentWriteFont = new WriteFont();
             //内容字体大小
             contentWriteFont.setFontName("宋体");
             contentWriteFont.setFontHeightInPoints((short) 10);
             contentWriteCellStyle.setWriteFont(contentWriteFont);
        }
    }
    @Override
    public int order() {
        return 50000;
    }
}
