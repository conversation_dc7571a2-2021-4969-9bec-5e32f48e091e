package cn.cmcc.common.datasource.handler;

import java.util.HashMap;
import java.util.Map;

import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.checkerframework.checker.units.qual.K;

/**  
 * 数据库查询出来的两列，直接存储为Map的key，value
 * 
 * <AUTHOR>
 * @date 2024-06-14 11:14:03
*/

@SuppressWarnings("all")
public class MapResultHandler<K,V> implements ResultHandler<Map<K,V>>{

	private final Map<K,V> mappedResults = new HashMap<>();

    @Override
    public void handleResult(ResultContext context) {
        Map map = (Map) context.getResultObject();
        mappedResults.put((K)map.get("key"), (V)map.get("value"));
    }

    public Map<K,V> getMappedResults() {
        return mappedResults;
    }
}
