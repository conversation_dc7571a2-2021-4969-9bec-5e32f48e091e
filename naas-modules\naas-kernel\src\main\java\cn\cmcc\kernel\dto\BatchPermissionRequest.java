package cn.cmcc.kernel.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量权限请求 DTO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
public class BatchPermissionRequest {

    /**
     * 用户名（用户权限时使用）
     */
    private String username;

    /**
     * 角色标识（角色权限时使用）
     */
    private String roleKey;

    /**
     * 查询编码列表
     */
    @NotEmpty(message = "查询编码列表不能为空")
    private List<String> queryCodes;

    /**
     * 权限类型：read-查询，write-修改
     */
    @NotBlank(message = "权限类型不能为空")
    private String permissionType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 构造函数
     */
    public BatchPermissionRequest() {
        this.permissionType = "read"; // 默认为读权限
    }

    /**
     * 构造函数（用户权限）
     *
     * @param username 用户名
     * @param queryCodes 查询编码列表
     * @param permissionType 权限类型
     */
    public BatchPermissionRequest(String username, List<String> queryCodes, String permissionType) {
        this.username = username;
        this.queryCodes = queryCodes;
        this.permissionType = permissionType;
    }

    /**
     * 构造函数（角色权限）
     *
     * @param roleKey 角色标识
     * @param queryCodes 查询编码列表
     * @param permissionType 权限类型
     */
    public static BatchPermissionRequest forRole(String roleKey, List<String> queryCodes, String permissionType) {
        BatchPermissionRequest request = new BatchPermissionRequest();
        request.setRoleKey(roleKey);
        request.setQueryCodes(queryCodes);
        request.setPermissionType(permissionType);
        return request;
    }

    /**
     * 验证是否为用户权限请求
     */
    public boolean isUserPermission() {
        return username != null && !username.trim().isEmpty();
    }

    /**
     * 验证是否为角色权限请求
     */
    public boolean isRolePermission() {
        return roleKey != null && !roleKey.trim().isEmpty();
    }
}
