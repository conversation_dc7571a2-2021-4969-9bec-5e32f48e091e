package cn.cmcc.auth.service.cas;

import static cn.cmcc.auth.config.CasConfig.ROOT_SUFFIX;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.auth.utils.JwtUtil;
import cn.cmcc.common.cache.config.CacheConfig;
import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.utils.JwtUtils;
import cn.cmcc.common.utils.http.HttpClientUtils;
import cn.cmcc.common.utils.http.bo.HttpReqParam;
import cn.cmcc.common.utils.text.CharsetKit;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.cmcc.system.api.pojo.dto.SysUserDto;
import cn.cmcc.system.api.service.RemoteUserService;
import lombok.extern.slf4j.Slf4j;

/**
 * 单点登录oauth2实现
 *
 * <AUTHOR>
 * @date 2023-10-19 04:09:27
 */
@Slf4j
public class CasServiceOauth2Impl extends CacheConfig implements ICasService {

	public static final String OAUTH2 = "oauth2",ACCESS_TOKEN_PREFIX = "access_token:";


	private final CasConfig config;

	private final RemoteUserService remoteUserService;

	/**
	 * 应用代码
	 */
	private final String responseType;

	/**
	 * 应用程序密钥
	 */
	private final String clientId;

	/**
	 * 权限
	 */
	private String scope;

	/**
	 * 密码
	 */
	private final String clientSecret;

	/**
	 * 用户资源URL
	 */
	private String userResourceUrl;

	/***
	 * 用户信息URL
	 */
	private String userInfoUrl;


	public CasServiceOauth2Impl(CasConfig config,RemoteUserService remoteUserService) {
		this.config = config;
		Map<String, Object> otherParameters = JSON.parseObject(config.getOtherParameters());
		this.responseType = (String) otherParameters.get("responseType");
		this.clientId = (String) otherParameters.get("clientId");
		this.scope = (String) otherParameters.get("scope");
		this.clientSecret = (String) otherParameters.get("clientSecret");
		String resourceBaseUrl = MapUtils.getString(otherParameters, "resourceUrl");
		String resourceName = MapUtils.getString(otherParameters, "resourceName","NAAS");
		this.remoteUserService = remoteUserService;

		try {
			this.userResourceUrl = String.format("%s/sysUsers/resource/%s",resourceBaseUrl,resourceName);
			this.userInfoUrl = String.format("%s/userinfo?scope=%s",config.getCasServerUrl(),this.scope);
			String casLoginUrl = URLEncoder
					.encode(String.format("%s%s", config.getServerUrl(), config.getServerLoginSuffix()), CharsetKit.UTF_8);
			config.setCasLoginUrl(
					String.format("%s%s?response_type=%s&client_id=%s&redirect_uri=%s&scope=%s", config.getCasServerUrl(),
							config.getCasServerLoginSuffix(), this.responseType, this.clientId, casLoginUrl,this.scope));
			log.info("casLoginUrl: {}", config.getCasLoginUrl());
			config.setCasValidateUrl(
					String.format("%s%s", config.getCasServerUrl(), config.getCasServerValidateSuffix()));
			log.info("casValidateUrl: {}", config.getCasValidateUrl());
			config.setExpire(CacheConstants.EXPIRE_AFTER_WRITE);
			log.info("expire: {}", config.getExpire());
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
	}

	@Override
	public AjaxResult login(HttpServletRequest request) {
		Map<String, Object> data = new HashMap<>(16);
		try {
			// 已经有登录信息
			if (validToken(request)) {
				data.put("redirect", ROOT_SUFFIX);
				return AjaxResult.success(data);
			}

			String ticket = getParameter(request, config.getCasTicketParameter());
			if (StringUtils.isEmpty(ticket)) {
				log.info("Ticket is empty, jumps to the CAS login page.");
				data.put("redirect", getURL(request));
			} else {
				log.info("Ticket: {}\nAfter encode: {}.", ticket,
						ticket = URLEncoder.encode(ticket, StandardCharsets.UTF_8));
				Map<String, Object> params = new LinkedHashMap<>();
				params.put("redirect_uri", getServiceUrl(request));
				params.put("grant_type", "authorization_code");
				params.put("code", ticket);
				params.put("client_id", this.clientId);
				params.put("client_secret", this.clientSecret);
				params.put("scope", this.scope);
				String requestJ = JSON.toJSONString(params);
				log.info("Request:\n{}", requestJ);
				AjaxResult result = HttpClientUtils.sendFormUrlEncode(config.getCasValidateUrl(), params, null);
				if (result.isSuccess()) {
					String response = result.getData().toString();
					log.info("Response:\n{}", response);

					JSONObject responseJ = JSONObject.parseObject(response);
					if (responseJ.containsKey("access_token")) {
						String accessToken = responseJ.getString("access_token");
						String userName = JwtUtil.getJwtString(accessToken, "name");
						if(StringUtils.isEmpty(userName)) {
							userName = JwtUtil.getJwtString(accessToken, "sub");
						}

						// 票据-TOKEN
						config.getCacheService().set(ACCESS_TOKEN_PREFIX + userName, responseJ, CachesEnums.CACHE_LOGIN.name());

						try {
							SysUserDto sysUserDto = getUserInfo(accessToken);
							if(sysUserDto != null) {
								remoteUserService.checkInitUser(sysUserDto,getUserResource(accessToken));
							}
						} catch (Exception e) {
							log.error("调用获取用户[" + userName + "]信息资源异常",e);
						}

						String token = config.getLoginService().ssoLogin(userName).get(SecurityConstants.TOKEN).toString();
						data.put("redirect", ROOT_SUFFIX);
						data.put(SecurityConstants.TOKEN, token);
						data.put("userName", userName);
						data.put("accessToken", accessToken);
						data.put("idToken", responseJ.getString("id_token"));
					} else {
						data.put("redirect", config.getCasLoginUrl());
						data.put("msg", responseJ.getString("error"));
					}
				} else {
					data.put("redirect", config.getCasLoginUrl());
					data.put("msg", result.getMsg());
				}
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);

			data.put("redirect", config.getCasLoginUrl());
			data.put("msg", ex.getMessage());
		}
		return AjaxResult.success(data);
	}

	@Override
	public void logout(HttpServletRequest request) {
		try {
			/*
			String token = SecurityUtils.getToken();
			String account = JwtUtils.getUserAccount(token);
			String key = ACCESS_TOKEN_PREFIX + account;
			JSONObject responseJ = (JSONObject)config.getCacheService().get(key,CachesEnums.CACHE_LOGIN.name());

			config.getCacheService().del(key, CachesEnums.CACHE_LOGIN.name());

			log.info("token-info:{}",responseJ.toJSONString());
			String idToken = responseJ.getString("id_token");
			String accessToken = responseJ.getString("access_token");
			HttpClientUtils.sendGet(String.format("%s%s?id_token_hint=%s", config.getCasServerUrl(),config.getCasServerLogoutSuffix(),idToken));

			String authorization = "Basic " + Base64.encode(this.clientId + ":" + this.clientSecret);
			String url = String.format("%s/oauth2/revoke", config.getCasServerUrl());

//			HttpClientUtils.send(HttpReqParam.builder(url, HttpMethod.POST)
//					.addHeaderParam("Authorization", authorization)
//					.params(JSON.toJSONString(ImmutableMap.<String, Object>builder()
//							.put("token", accessToken).build()))
//					.build());

			Map<String,Object> params = new HashMap<>();
			Map<String,String> headers = new HashMap<>();
			params.put("token", accessToken);
			headers.put("Authorization", authorization);
			HttpClientUtils.sendFormUrlEncode(url, params, headers);*/
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
	}

	@Override
	public boolean tokenExpired(String token) {
		return false;
	}

	/**
     * 获取 URL
     *
     * @param request 请求
     * @param ticket  票据
     * @return URL
     */
    public String getURL(HttpServletRequest request) throws UnsupportedEncodingException {
        String chapters = getParameter(request, "chapters");
        if (StringUtils.isEmpty(chapters)) {
           return config.getCasLoginUrl();
        } else {
            String service = chapters.startsWith("/") ?
                    URLEncoder.encode(String.format("%s%s%s", config.getServerUrl(), config.getServerLoginSuffix(), chapters), CharsetKit.UTF_8) :
                    URLEncoder.encode(String.format("%s%s/%s", config.getServerUrl(), config.getServerLoginSuffix(), chapters), CharsetKit.UTF_8);

			return String.format("%s%s?response_type=%s&client_id=%s&redirect_uri=%s", config.getCasServerUrl(),
							config.getCasServerLoginSuffix(), this.responseType, this.clientId, service);
        }
    }

	public String getServiceUrl(HttpServletRequest request){
    	String chapters = getParameter(request, "chapters");
        if (StringUtils.isEmpty(chapters)) {
           return String.format("%s%s", config.getServerUrl(), config.getServerLoginSuffix());
        }
        String service = chapters.startsWith("/") ?
                String.format("%s%s%s", config.getServerUrl(), config.getServerLoginSuffix(), chapters):
                String.format("%s%s/%s", config.getServerUrl(), config.getServerLoginSuffix(), chapters);

		return service;
    }

	/**
	 * 获取用户资源
	 *
	 * @param accessToken
	 * @return
	 * List<String>
	 */
	private List<String> getUserResource(String accessToken) {
		AjaxResult ajr = HttpClientUtils.send(HttpReqParam.builder(userResourceUrl, HttpMethod.GET)
				.addHeaderParam("Authorization", "Bearer " + accessToken).build());
		if(ajr.isSuccess()) {
			log.info("获取用户资源：{}",ajr.getData().toString());
			JSONObject responseJ = JSONObject.parseObject(ajr.getData().toString());
			return Optional.ofNullable(responseJ.getJSONArray("data")).orElse(new JSONArray())
				.stream().map(o->{
					JSONObject jObject = (JSONObject)o;
					return jObject.getString("menuName");
				}).collect(Collectors.toList());
		}
		return null;
	}

	/**
	 * 获取用户资源
	 *
	 * @param accessToken
	 * @return
	 * List<String>
	 */
	private SysUserDto getUserInfo(String accessToken) {
		AjaxResult ajr = HttpClientUtils.send(HttpReqParam.builder(userInfoUrl, HttpMethod.GET)
				.addHeaderParam("Authorization", "Bearer " + accessToken).build());
		if(ajr.isSuccess()) {
			log.info("获取用户信息：{}",ajr.getData().toString());

			JSONObject responseJ = JSONObject.parseObject(ajr.getData().toString());

			SysUserDto sysUserDto = new SysUserDto();
			sysUserDto.setAccount(MapUtils.getString(responseJ, "name"));
			sysUserDto.setName(MapUtils.getString(responseJ, "name"));
			return sysUserDto;
		}
		return null;
	}

	public static void main(String[] args) {
		String token = "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************.cqa6oPG5W8T3QgUtBRfa_3ZUc7EOTpgPnwIf6DHs2rkACfdP3p8dvlE957u7fUZX4B9pHK4l90M3a7ct5nQWKg";
		log.info(JwtUtils.getUserAccount(token));
	}

}
