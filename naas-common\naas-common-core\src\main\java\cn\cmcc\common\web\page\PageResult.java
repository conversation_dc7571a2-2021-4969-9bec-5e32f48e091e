package cn.cmcc.common.web.page;

import java.util.List;

import cn.cmcc.common.web.domain.AjaxResult;

/**
 * 表格分页数据对象
 * 
 * <AUTHOR>
 */
public class PageResult extends AjaxResult
{
    /** 总记录数 */
    private long total;
    
    /** 当前页数 */
    private Integer pageNum;

    /**
     * 表格数据对象
     */
    public PageResult()
    {
    }
    
    public PageResult(int code,String msg)
    {
    	this.code = code;
    	this.msg = msg;
    }

    /**
     * 分页
     * 
     * @param list 列表数据
     * @param total 总记录数
     */
    public PageResult(List<?> list, int total)
    {
        this.data = list;
        this.total = total;
    }

    public long getTotal()
    {
        return total;
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
    
    
}
