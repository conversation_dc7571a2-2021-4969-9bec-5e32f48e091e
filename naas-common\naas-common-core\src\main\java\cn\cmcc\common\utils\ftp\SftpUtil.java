package cn.cmcc.common.utils.ftp;

import cn.cmcc.common.pojo.template.FtpParam;
import cn.cmcc.common.utils.file.CompressUtil;
import cn.cmcc.common.utils.file.FileUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Filter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ssh.JschRuntimeException;
import com.jcraft.jsch.*;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.ChannelSftp.LsEntrySelector;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class SftpUtil implements Ftp{
	/** log */
	protected static Logger log = LoggerFactory.getLogger(SftpUtil.class);

	public static final String NO_FILE = "No such file";

	private ChannelSftp sftp = null;

	private Session sshSession = null;

	private String username;

	private String password;

	private String host;

	private int port;

	// 私钥
    private String privateKey;

	public SftpUtil(String username, String password, String host, int port,String privateKey) {
		this.username = username;
		this.password = password;
		this.host = host;
		this.port = port;
		this.privateKey = privateKey;
	}

	public SftpUtil(FtpParam ftpParam) {
		this.username = ftpParam.getUserName();
		this.password = ftpParam.getPassword();
		this.host = ftpParam.getIp();
		this.port = ftpParam.getPort();
		this.privateKey = ftpParam.getPrivateKey();
	}

	public static void main(String[] args) {

		//Ftp ftp = new SftpUtil("root","2wsx4rfv;lkjh","***********",22);
		//Ftp ftp = new SftpUtil("root","***********",22,"D://id_rsa","2wsx4rfv;lkjh");
		String xx ="-----BEGIN RSA PRIVATE KEY-----\r\n"
				+ "MIIEpAIBAAKCAQEA61zvTuhiigMXkXeOr5Xst+Iqg9uI8v480IfQ+58cUSZOZAD3\r\n"
				+ "3ghAzVDYGqxLtSwZx2nf6f4SpOzoIAhWInjmbVKBi/wNtaKezxeL18JQbs7mTrUw\r\n"
				+ "SkIlMi2NXaP9zFrTclBqkWIpWE+NG7SE/g6kofvb/ehAsde7iMBxfRRUOwZS1Blx\r\n"
				+ "GYTAjtLC6sRcCNXtAYhkeZbHwXi0PTjOKuERevbHwIVX09WxKZe0QDe30OgErLLQ\r\n"
				+ "ChXlsWJAlt5pyHezkLrHSjuWdjhUBzBKeoTe2y8i7dRnzsE1mwfHMxlYZ94fttfk\r\n"
				+ "AD8B7viV/F88+uQoyvApu7lgjClhjQ9MclI1gwIDAQABAoIBAQCoHpk+FxaUGMNU\r\n"
				+ "AD+mwoP1kZLm3wEC+YG9PtaTdcMHYEJsgpiLO//16MKEwv/NKPQ9Ei6EeR0eO69P\r\n"
				+ "ekT0mMGX43Bv6ppsXP3tVZdphf5oOFggbqJQ2NCy+ADbm5Fl0JbrN+/3a1O3JhPY\r\n"
				+ "b2j0BMeqgTpSMv1LS5tEgppOSyAji/EJjtg05criwXtWbvIrD/ALmoW+o4SSGRTL\r\n"
				+ "G1DQU2D+KQmMRo60b+T+hXA4kG5VnT/42BJ0eyZph6HPfnvjhd+enhi+54YmPG9N\r\n"
				+ "ovKzabvRLDPfGQYGBHhEm8HoVN2bO9O34/XbZLy4o46twPUB/rllmx83/jci06OL\r\n"
				+ "saPSwHzpAoGBAPrv2Y1G1KA8iK5IU/c7dSIBEuYs4uaINCl7+6i+F/TdXGlGzV3D\r\n"
				+ "Y2OYJRZ6NW42afRth4sBVn2iEKHOYZe8UTYUsiHc+fdQH509+FRY9h3Pmpz9ykCd\r\n"
				+ "9dGW/lw7UKfL7BMObDHXcnOQwo0slQyjNzBXrh1oxC/VaToIkg4bqAl/AoGBAPAc\r\n"
				+ "pF7jl2HWW9TATopAPqhYMvHIrz8rmSWUcfp7dOp+R2VlnwCBmUqhF0a4J0s6NxKi\r\n"
				+ "0SbKO7JXVCOvXmySnN4RUPXlkDXMVnezQsxdnG4BbMhq2QtxYCUjRIOJpfQx01rJ\r\n"
				+ "8duPWQsAA3QzoaQGWT6e/iVaYbgAIQn2rWcAKq39AoGAev8zeE7+i2gAasSi+2zW\r\n"
				+ "VguGsvHPacXbI3ZU/PnoDiquUB/XK51IyxlZHHJcXCApwRgqttS6Cyxz0IuzREeW\r\n"
				+ "2tY8FeMVVsacKFumq9lLEYOfulGfYBrM0aWcGaMtPoHyvch0+d0riT+sTLxMdnxN\r\n"
				+ "4dEoU9v1RajqQfrPdEDUFYsCgYEArN0SATZ87/TxW/OvF500/uGwm51NfX8X3JRI\r\n"
				+ "knp7K68KG5fTpvumrrHXHhTKRUB+Ea6iQPjrjXONobtJkYTj5oQ08zL4ZaLcxYSL\r\n"
				+ "HLJwQkpIVOmHpNNU0qfXVZOtpGaSsRQeSWGkS2URF4FsZ7giXBD/C0lMfLmrD49G\r\n"
				+ "O5dFIskCgYAsG8Ll0qk+tYq6QyNZJIGR1xdqgMvYIv0dIg9T6Xe7jh6+0Z6pznZE\r\n"
				+ "RrNYIOu73SsubzBrgawC4ZNJuN+YnDkbCdsk7pCIdYTaUixWeuvZo+Ww1cdJNMqK\r\n"
				+ "/02sK/hNB3BzOdReZrjGZJUF3OX/DMwfZ8rZJzsi0NniRJAuomwe1w==\r\n"
				+ "-----END RSA PRIVATE KEY-----\r\n"
				+ "";
		Ftp ftp = new SftpUtil("root",null,"***********",22,xx);
		if(ftp.ftpLogin()) {
			List<String> fileList = ftp.downloadFile("bootstrap.yml","D:/test/test1/bootstrap.yml", "/data/naas");
			System.out.println(fileList);
		}
		ftp.ftpLogOut();
	}

	@Override
	public void ftpLogOut() {
		if (this.sftp != null) {
			if (this.sftp.isConnected()) {
				this.sftp.disconnect();
				this.sftp = null;
				log.info("sftp is closed already");
			}
		}
		if (this.sshSession != null) {
			if (this.sshSession.isConnected()) {
				this.sshSession.disconnect();
				this.sshSession = null;
				log.info("sshSession is closed already");
			}
		}
	}

	/**
	 * 连接sftp服务器
	 *
	 * @return ChannelSftp sftp类型
	 * @throws Exception
	 */
	public boolean ftpLogin() {
		log.info("SftpUtil-->connect--ftp连接开始>>>>>>host=" + host + ">>>port" + port + ">>>username=" + username);
		JSch jsch = new JSch();
		try {
			if(StringUtils.isNotEmpty(privateKey)) {
				// 设置私钥
				if(StringUtils.isNotEmpty(password)) {
					jsch.addIdentity("id_rsa",privateKey.getBytes(),null,password.getBytes());
				}else {
	                jsch.addIdentity("id_rsa",privateKey.getBytes(),null,null);
				}
			}
//			jsch.getSession(username, host, port);
			sshSession = jsch.getSession(username, host, port);
			log.info("ftp---Session created.");
			if (StringUtils.isEmpty(privateKey)
					&& StringUtils.isNotEmpty(password)) {
				sshSession.setPassword(password);
			}
			Properties properties = new Properties();
			 // 设 No JSch将不会检查远程主机的主机密钥是否与本地已知主机密钥数据库中存储的密钥匹配
			properties.put("StrictHostKeyChecking", "no");
			sshSession.setConfig(properties);
			sshSession.connect();
			log.info("ftp---Session connected.");
			Channel channel = sshSession.openChannel("sftp");
			channel.connect();
			log.info("Opening Channel.");
			sftp = (ChannelSftp) channel;
			log.info("ftp---Connected to " + host);
			return true;
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
		return false;
	}

	/**
	 * 载单个文件
	 *
	 * @param directory      ：远程下载目录(以路径符号结束)
	 * @param remoteFileName FTP服务器文件名称 如：xxx.txt ||xxx.txt.zip
	 * @param localFile      本地文件路径 如 D:\\xxx.txt
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<String> downloadFile(String remoteFileName, String localFilePath, String remoteDownLoadPath) {
		List<String> downList = new ArrayList<>();
		log.info(">>>>>>>>SftpUtil-->downloadFile--ftp下载文件" + remoteFileName + "开始>>>>>>>>>>>>>");
		try {
			createDirectory(localFilePath);
			sftp.cd(remoteDownLoadPath);

			List<String> fileList = Collections.singletonList(remoteFileName);

			log.info(">>>>>>>>SftpUtil下载文件列表" + fileList);
			if(remoteFileName.contains("{rand}")) {
				List<String> sepFileNameList = Arrays.asList(remoteFileName.replace("{rand}", ",").split(","));
				fileList = ls(remoteDownLoadPath,entry->{
					return sepFileNameList.stream().allMatch(s-> entry.getFilename().contains(s));
				});
			}

			for (String realFileName : fileList) {
				String realLocalFileName = localFilePath;
				if(remoteFileName.contains("{rand}")) {
					if(localFilePath.contains("{rand}")) {
						realLocalFileName = localFilePath.replace(remoteFileName,realFileName);
					}else {
						realLocalFileName = localFilePath + realFileName;
					}
				}
				log.info(">>>>>>>>下载文件：>>>>>>>" + realLocalFileName);

				try(OutputStream output = new FileOutputStream(FileUtils.getFile(realLocalFileName))){
					sftp.get(realFileName, output);
					log.info("===DownloadFile:" + realFileName + " success from sftp.");
					if (realLocalFileName.indexOf("tsv") >0) {
						// 处理.tsv文件
						String csvFilePath = realLocalFileName.replace("tsv.gz","csv");
						boolean TsvToCsv =  processTsvToCsv(realLocalFileName, csvFilePath);
						// 注意：这里存储的是转换后的CSV文件路径
						if(TsvToCsv){
							downList.add(csvFilePath);
							// 可选：删除原始的.tsv文件
							File file = new File(realLocalFileName);// NOSONAR
							file.delete();// NOSONAR
						}
					}else{
						downList.add(realLocalFileName);
						log.info(realFileName + "成功下载到" + realLocalFileName);
					}
				} catch (FileNotFoundException e) {
					// TODO Auto-generated catch block
					log.error(e.getMessage(),e);
				} catch (IOException e) {
					// TODO Auto-generated catch block
					log.error(e.getMessage(),e);
				}
			}
		} catch (SftpException e) {
			if (e.toString().equals(NO_FILE)) {
				log.info(
						">>>>>>>>SftpUtil-->downloadFile--sftp下载文件失败" + remoteDownLoadPath + remoteFileName + "不存在>>>>>>>>>>>>>");
			}
		}
		log.info(">>>>>>>>SftpUtil-->downloadFile--sftp下载文件结束>>>>>>>>>>>>>");
		return downList;
	}

	public  boolean processTsvToCsv(String tsvFilePath, String csvFilePath) throws IOException {
		String pathName = CompressUtil.unCompressGzip(tsvFilePath);
		File file = new File(pathName);// NOSONAR
		Path tsvPath = Paths.get(pathName);// NOSONAR
		Path csvPath = Paths.get(csvFilePath);// NOSONAR
		boolean herad = true;
		try (BufferedReader reader = Files.newBufferedReader(tsvPath, StandardCharsets.UTF_8);
			 BufferedWriter writer = Files.newBufferedWriter(csvPath, StandardCharsets.UTF_8)) {

			String line;
			int size = 0;
			while ((line = reader.readLine()) != null) {
				String lineStr = "";
				if(herad) {
					lineStr = line.replace(".","_");
					size = lineStr.split("\t").length;
				}else{
					lineStr = line;
				}
				lineStr = lineStr.replace(",","，");
				String[] fields = lineStr.split("\t");
				String csvLine = String.join(",", fields);
				if(size > fields.length){
					for(int s = size-fields.length; s>0;s--){
						csvLine	+=",";
					}
				}
				csvLine = csvLine.replace("\\N","0");
				writer.write(csvLine);
				writer.newLine();
				herad = false;
			}
			file.delete();// NOSONAR
			return true;
		} catch (IOException e) {
			e.printStackTrace();
		}
		file.delete();// NOSONAR
		return false;
	}

	@Override
	public List<String> downLoadDirectory(String localDirectoryPath, String remoteDirectory) {
		return downLoadDirectory(localDirectoryPath,remoteDirectory,null);
	}

	/**
     * 列出目录下的文件
     *
     * @param directory 要列出的目录
     */
    public Vector<?> listFiles(String directory) throws SftpException {
		Vector vector = sftp.ls(directory);
		final java.util.Vector rector = new Vector();
		for (int i = 0; i < vector.size(); i++) {
			LsEntry entry = (LsEntry) vector.get(i);
			if(!(entry.getFilename().indexOf(".") == 0)){//过滤隐藏文件 .开头为隐藏文件
                rector.addElement(entry);
            }
		}
        return rector;
    }

	public List<String> downLoadDirectory(String localDirectoryPath, String remoteDirectory,final FileNameFilter filter) {
		List<String> downList = new ArrayList<>();
		try {
			log.info(">>>>>>>>服务器文件路径>>>>>>>>>>>>>"+remoteDirectory);
			log.info(">>>>>>>>本地文件路径>>>>>>>>>>>>>"+localDirectoryPath);
			File localDir = FileUtils.getFile(localDirectoryPath);
			if(!localDir.exists()) localDir.mkdirs();

			Vector vector = listFiles(remoteDirectory);

			log.info(">>>>>>>>服务器文件列表>>>>>>>>>>>>>"+vector);
			 for (int i = 0; i < vector.size(); i++) {
	            LsEntry entry = (LsEntry) vector.get(i);
            	final String subFileName = entry.getFilename();
				 log.info(">>>>>>>>下载文件名称>>>>>>>>>>>>>"+subFileName);
	            if (entry.getAttrs().isDir()) {
	            	String strremoteDirectoryPath = remoteDirectory + "/" + subFileName;
	            	downList.addAll(downLoadDirectory(localDirectoryPath, strremoteDirectoryPath,filter));
	            }else {
	            	if (null == filter || filter.accept(subFileName)) {
	            		downList.addAll(downloadFile(subFileName, localDirectoryPath + "/" + subFileName, remoteDirectory));
	            	}
	            }
	        }
		} catch (Exception var7) {
			var7.printStackTrace();
			log.info("下载文件夹失败");
		}
		return downList;
	}

	public List<String> ls(String path, final Filter<LsEntry> filter) {
		final List<LsEntry> entries = lsEntries(path, filter);
		if (CollUtil.isEmpty(entries)) {
			return ListUtil.empty();
		}
		return CollUtil.map(entries, LsEntry::getFilename, true);
	}

	/**
	 * 上传单个文件
	 *
	 * @param directory      ：远程下载目录(以路径符号结束)
	 * @param uploadFilePath 要上传的文件 如：D:\\test\\xxx.txt
	 * @param fileName       FTP服务器文件名称 如：xxx.txt ||xxx.txt.zip
	 * @throws Exception
	 */
    @Override
	public boolean uploadFile(String directory, String uploadFilePath, String fileName){
		log.info(">>>>>>>>SftpUtil-->uploadFile--ftp上传文件开始>>>>>>>>>>>>>");
		try {
			sftp.cd(directory);
		} catch (SftpException e) {
			try {
				mkdir(directory);
				sftp.cd(directory);
			} catch (Exception e1) {
				log.info("ftp创建文件路径失败，路径为：" + directory,e1);
				return false;
			}

		}
		File file = FileUtils.getFile(uploadFilePath);
		try(FileInputStream in = new FileInputStream(file);){
			sftp.put(in, fileName);
			log.info(">>>>>>>>SftpUtil-->uploadFile--ftp上传文件结束>>>>>>>>>>>>>");
			return true;
		}  catch (Exception e1) {
			e1.printStackTrace();
			return false;
		}
	}

	@Override
	public boolean uploadFile(File localFile, String directory) {
    	log.info(">>>>>>>>SftpUtil-->uploadFile--ftp上传文件开始>>>>>>>>>>>>>");
		try {
			sftp.cd(directory);
		} catch (SftpException e) {
			try {
				mkdir(directory);
				sftp.cd(directory);
			} catch (Exception e1) {
				log.info("ftp创建文件路径失败，路径为" + directory);
				return false;
			}

		}
		try(FileInputStream in = new FileInputStream(localFile);){
			sftp.put(in, localFile.getName());
			log.info(">>>>>>>>SftpUtil-->uploadFile--ftp上传文件结束>>>>>>>>>>>>>");
			return true;
		}  catch (Exception e1) {
			log.error("文件上传失败", e1);
			return false;
		}
    }

	public void mkdir(String directory) throws Exception {
		log.info(">>>>>>>>SftpUtil-->mkdir--ftp开始创建目录>>>>>>>>>>>>>" + directory);
		String[] directories = directory.split("/"); // 拆分目录路径
		sftp.cd("/");//先进入根目录
		// 逐级创建目录
		for (String dir : directories) {
		    if (StringUtils.isNotBlank(dir)) {
		    	try {
					sftp.cd(dir);
				} catch (SftpException e) {
					try {
						sftp.mkdir(dir);
						sftp.cd(dir);
					} catch (SftpException e1) {
						throw new Exception("ftp创建文件路径失败，路径为" + directory);
					}
				}
		    }
		}
		log.info(">>>>>>>>SftpUtil-->mkdir--ftp创建目录结束>>>>>>>>>>>>>");
	}

	/**
     * 带过滤器列出目录下的文件
	 *
     * @param path
     * @param filter
     * @return
     */
	public List<LsEntry> lsEntries(String path, Filter<LsEntry> filter) {
		final List<LsEntry> entryList = new ArrayList<>();
		try {
			sftp.ls(path, entry -> {
				final String fileName = entry.getFilename();
				if (false == StrUtil.equals(".", fileName) && false == StrUtil.equals("..", fileName)) {
					if (null == filter || filter.accept(entry)) {
						entryList.add(entry);
					}
				}
				return LsEntrySelector.CONTINUE;
			});
		} catch (SftpException e) {
			if (false == StrUtil.startWithIgnoreCase(e.getMessage(), "No such file")) {
				throw new JschRuntimeException(e);
			}
		}
		return entryList;
	}

}
