# 查询编码权限管理 - 快速开始指南

## 🚀 5分钟快速上手

本指南将帮助你快速了解和使用查询编码权限管理功能。

## 前置条件

- ✅ 数据库已执行权限表创建脚本
- ✅ 用户已登录并具有相应权限
- ✅ 系统已部署权限管理模块

## 第一步：了解权限模型

### 权限检查优先级
```
超级管理员 > 用户直接权限 > 角色权限 > 拒绝访问
```

### 权限类型
- `read`: 查询权限（默认）
- `write`: 修改权限（预留扩展）

## 第二步：使用注解进行权限验证

### 在控制器方法上添加权限验证

```java
@RestController
@RequestMapping("/api/data")
public class DataController {

    @GetMapping("/{code}")
    @CheckQueryCodePerms(message = "无权限访问该数据")
    public AjaxResult getData(@PathVariable("code") String code) {
        // 自动进行权限验证，验证失败会抛出异常
        // 这里编写业务逻辑
        return AjaxResult.success(data);
    }
    
    @GetMapping("/custom/{queryCode}")
    @CheckQueryCodePerms(codeParam = "queryCode", message = "权限不足")
    public AjaxResult getCustomData(@PathVariable("queryCode") String queryCode) {
        // 自定义参数名进行权限验证
        return AjaxResult.success(data);
    }
}
```

## 第三步：编程式权限检查

### 在业务代码中手动检查权限

```java
@Service
public class BusinessService {
    
    @Autowired
    private IQueryCodePermissionService permissionService;
    
    public void processData(String queryCode) {
        String username = SecurityUtils.getUserName();
        
        // 检查权限
        if (!permissionService.hasPermission(username, queryCode)) {
            throw new QueryCodePermissionException(queryCode, username, "无权限访问");
        }
        
        // 执行业务逻辑
        doBusinessLogic(queryCode);
    }
}
```

## 第四步：权限管理操作

### 4.1 用户权限管理

```bash
# 授予用户权限
curl -X POST "http://localhost:9260/kernel/queryCodePermission/user/grant" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "queryCode": "qryTestData",
    "permissionType": "read"
  }'

# 查看用户权限
curl -X GET "http://localhost:9260/kernel/queryCodePermission/user/testuser"

# 撤销用户权限
curl -X DELETE "http://localhost:9260/kernel/queryCodePermission/user/revoke/testuser/qryTestData"
```

### 4.2 角色权限管理

```bash
# 授予角色权限
curl -X POST "http://localhost:9260/kernel/queryCodePermission/role/grant" \
  -H "Content-Type: application/json" \
  -d '{
    "roleKey": "BASE_PRI_ROLE",
    "queryCode": "qryTestData",
    "permissionType": "read"
  }'

# 批量授予角色权限
curl -X POST "http://localhost:9260/kernel/queryCodePermission/role/batchGrant" \
  -H "Content-Type: application/json" \
  -d '{
    "roleKey": "BASE_PRI_ROLE",
    "queryCodes": ["qryData1", "qryData2", "qryData3"],
    "permissionType": "read"
  }'
```

## 第五步：权限查询和统计

### 查询用户可访问的编码

```bash
# 获取用户可访问的所有查询编码
curl -X GET "http://localhost:9260/kernel/queryCodePermission/accessible/testuser"

# 响应示例
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "username": "testuser",
    "queryCodes": ["qryData1", "qryData2"],
    "permissionType": "read",
    "totalCount": 2
  }
}
```

### 查询编码的权限分配情况

```bash
# 查看谁有权限访问指定编码
curl -X GET "http://localhost:9260/kernel/queryCodePermission/users/qryTestData"

# 响应示例
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "queryCode": "qryTestData",
    "users": ["user1", "user2"],
    "roles": ["BASE_PRI_ROLE"],
    "userCount": 2,
    "roleCount": 1
  }
}
```

## 第六步：缓存管理

### 清除权限缓存

```bash
# 清除用户权限缓存
curl -X POST "http://localhost:9260/kernel/queryCodePermission/clearUserCache/testuser"

# 清除查询编码权限缓存
curl -X POST "http://localhost:9260/kernel/queryCodePermission/clearCodeCache/qryTestData"
```

## 常用场景示例

### 场景1：新用户权限初始化

```java
@Service
public class UserInitService {
    
    @Autowired
    private IQueryCodePermissionService permissionService;
    
    public void initNewUserPermissions(String username, String userRole) {
        // 根据用户角色批量授予基础权限
        List<String> basicCodes = Arrays.asList(
            "qryUserInfo", "qryBasicData", "qryPublicReport"
        );
        
        if ("MANAGER".equals(userRole)) {
            // 管理员额外权限
            basicCodes.addAll(Arrays.asList("qryManagerData", "qryStatistics"));
        }
        
        permissionService.batchGrantUserPermissions(username, basicCodes, "read");
    }
}
```

### 场景2：动态权限检查

```java
@RestController
public class DynamicDataController {
    
    @Autowired
    private IQueryCodePermissionService permissionService;
    
    @PostMapping("/query")
    public AjaxResult dynamicQuery(@RequestBody QueryRequest request) {
        String username = SecurityUtils.getUserName();
        String queryCode = request.getQueryCode();
        
        // 动态检查权限
        if (!permissionService.hasPermission(username, queryCode)) {
            return AjaxResult.error("无权限访问查询编码: " + queryCode);
        }
        
        // 执行查询
        return executeQuery(request);
    }
}
```

### 场景3：权限变更通知

```java
@Service
public class PermissionChangeService {
    
    @Autowired
    private IQueryCodePermissionService permissionService;
    
    @EventListener
    public void handleUserRoleChange(UserRoleChangeEvent event) {
        String username = event.getUsername();
        
        // 清除用户权限缓存，确保权限变更立即生效
        permissionService.clearUserPermissionCache(username);
        
        // 记录权限变更日志
        log.info("用户 [{}] 角色发生变更，已清除权限缓存", username);
    }
}
```

## 最佳实践

### ✅ 推荐做法

1. **优先使用注解**：在控制器方法上使用 `@CheckQueryCodePerms` 注解
2. **角色权限为主**：优先通过角色分配权限，减少直接用户权限
3. **定期清理**：定期清理无效的权限记录
4. **权限最小化**：只授予必要的权限，遵循最小权限原则

### ❌ 避免的做法

1. **不要绕过权限检查**：不要在业务代码中跳过权限验证
2. **不要硬编码权限**：避免在代码中硬编码查询编码
3. **不要忽略缓存**：权限变更后记得清除相关缓存
4. **不要过度授权**：避免给用户过多不必要的权限

## 故障排除

### 问题1：权限检查总是失败
```bash
# 检查用户是否有权限
curl -X GET "http://localhost:9260/kernel/queryCodePermission/check/username/queryCode"

# 检查用户权限列表
curl -X GET "http://localhost:9260/kernel/queryCodePermission/user/username"

# 检查用户可访问的编码
curl -X GET "http://localhost:9260/kernel/queryCodePermission/accessible/username"
```

### 问题2：权限变更不生效
```bash
# 清除用户缓存
curl -X POST "http://localhost:9260/kernel/queryCodePermission/clearUserCache/username"

# 清除编码缓存
curl -X POST "http://localhost:9260/kernel/queryCodePermission/clearCodeCache/queryCode"
```

### 问题3：批量操作失败
- 检查数据库约束
- 确认所有参数有效
- 查看应用日志

## 下一步

- 📖 阅读完整的 [API 文档](./QueryCodePermissionAPI.md)
- 🔧 查看详细的 [功能说明](./QueryCodePermissionService.md)
- 🧪 编写单元测试验证权限功能
- 📊 配置权限监控和统计

---

**需要帮助？** 查看完整文档或联系系统管理员。

**文档版本**: v1.0.0  
**最后更新**: 2025-01-28
