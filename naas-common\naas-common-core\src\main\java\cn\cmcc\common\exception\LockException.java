package cn.cmcc.common.exception;

/**
 * 加锁异常
 *
 * <AUTHOR>
 * @date 2023年2月14日
 * @version V1.0
 */
public class LockException  extends RuntimeException{

	/**
	 * @Fields serialVersionUID : 
	 */
	private static final long serialVersionUID = 1L;

	private String message;

    public LockException(String message)
    {
        this.message = message;
    }

    public LockException(String message, Throwable e)
    {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage()
    {
        return message;
    }
}
