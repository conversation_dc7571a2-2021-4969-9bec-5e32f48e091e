package cn.cmcc.kernel.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 权限授予请求 DTO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
public class PermissionGrantRequest {

    /**
     * 用户名（用户权限时使用）
     */
    private String username;

    /**
     * 角色标识（角色权限时使用）
     */
    private String roleKey;

    /**
     * 查询编码
     */
    @NotBlank(message = "查询编码不能为空")
    private String queryCode;

    /**
     * 权限类型：read-查询，write-修改
     */
    @NotBlank(message = "权限类型不能为空")
    private String permissionType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 构造函数
     */
    public PermissionGrantRequest() {
        this.permissionType = "read"; // 默认为读权限
    }

    /**
     * 构造函数
     *
     * @param username 用户名
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     */
    public PermissionGrantRequest(String username, String queryCode, String permissionType) {
        this.username = username;
        this.queryCode = queryCode;
        this.permissionType = permissionType;
    }

    /**
     * 构造函数（角色权限）
     *
     * @param roleKey 角色标识
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     */
    public static PermissionGrantRequest forRole(String roleKey, String queryCode, String permissionType) {
        PermissionGrantRequest request = new PermissionGrantRequest();
        request.setRoleKey(roleKey);
        request.setQueryCode(queryCode);
        request.setPermissionType(permissionType);
        return request;
    }

    /**
     * 验证是否为用户权限请求
     */
    public boolean isUserPermission() {
        return username != null && !username.trim().isEmpty();
    }

    /**
     * 验证是否为角色权限请求
     */
    public boolean isRolePermission() {
        return roleKey != null && !roleKey.trim().isEmpty();
    }
}
