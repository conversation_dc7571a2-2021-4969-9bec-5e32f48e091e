/**
 * @Title: RedisCaffeineCache.java
 * @Package cn.cmcc.common.core.cache.support
 *
 * <AUTHOR>
 * @date 2021年9月2日
 * @version V1.0
 */
package cn.cmcc.common.cache.support;

import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.cache.enums.ChannelTopicEnum;
import cn.cmcc.common.cache.listener.RedisCaffeineMessage;
import cn.cmcc.common.cache.listener.RedisPublishMessage;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.cache.support.AbstractValueAdaptingCache;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class RedisCaffeineCache extends AbstractValueAdaptingCache {

	 /**
     * 缓存的名称
     */
    private final String name;

    /**
     * Caffeine缓存
     */

	private final Cache<Object, Object> caffeineCache;
	/**
	 * Caffeine缓存key失效时间
	 */
	private final ConcurrentMap<Object, Long> expires = new ConcurrentHashMap<>();
    /**
     * 是否使用Caffeine缓存
     */
	private boolean usedCaffeineCache;

    /**
     * redisTemplate
     */
    private final RedisTemplate<Object, Object> redisTemplate;

    /**
     * 锁
     */
    private final Map<String, ReentrantLock> keyLockMap = new ConcurrentHashMap<>();


	/**
	 *
     * @param name
     * @param redisTemplate
     * @param caffeineCache
     * @param usedCaffeineCache
     * @param usedRedisCache
     * @param allowNullValues
     */
    public RedisCaffeineCache(String name,RedisTemplate<Object, Object> redisTemplate,
    		Cache<Object, Object> caffeineCache,boolean usedCaffeineCache,
    		boolean allowNullValues) {
    	super(allowNullValues);
		this.name = name;
		this.redisTemplate = redisTemplate;
		this.usedCaffeineCache = usedCaffeineCache;
		this.caffeineCache = caffeineCache;

	}

    @Override
    public String getName() {
        return this.name;
    }

    public Cache<Object, Object> getCaffeineCache() {
		return caffeineCache;
	}

	@Override
	public Object getNativeCache() {
		return this;
	}

	/**
	 * 	通过key获取缓存值，注意返回的是ValueWrapper，为了兼容存储空值的情况，将返回值包装了一层，通过get方法获取实际值
	 */
    @Override
    public ValueWrapper get(Object key) {
    	Object value = lookup(key);
    	return toValueWrapper(value);
    }

    /**
     * 通过key获取缓存值，返回的是实际值，即方法的返回值类型
     */
    @SuppressWarnings("unchecked")
	@Override
    public <T> T get(Object key, Class<T> type) {
    	Object value = lookup(key);
		if (value != null) {
			return (T) value;
		}
		return null;
    }

    /**
     * 通过key获取缓存值，可以使用valueLoader.call()来调使用@Cacheable注解的方法。
     * 当@Cacheable注解的sync属性配置为true时使用此方法。
     * 因此方法内需要保证回源到数据库的同步性。避免在缓存失效时大量请求回源到数据库
     */
	@SuppressWarnings("unchecked")
	@Override
	public <T> T get(Object key, Callable<T> valueLoader) {
		Object value = lookup(key);
		if (value != null) {
			return (T) value;
		}

		ReentrantLock lock = keyLockMap.computeIfAbsent(key.toString(), s -> {
			log.trace("create lock for key : {}", s);
			return new ReentrantLock();
		});

		try {
			lock.lock();
			value = lookup(key);
			if (value != null) {
				return (T) value;
			}
			value = valueLoader.call();
			Object storeValue = toStoreValue(value);
			put(key, storeValue);
			return (T) value;
		}
		catch (Exception e) {
			throw new ValueRetrievalException(key, valueLoader, e.getCause());
		}
		finally {
			lock.unlock();
		}
	}

	/**
	 * 将@Cacheable注解方法返回的数据放入缓存中
	 */
	@Override
	public void put(Object key, Object value) {
		if (!super.isAllowNullValues() && value == null) {
			this.evict(key);
			return;
		}

		if(usedCaffeineCache) {
			 caffeineCache.put(key, value);
		}

		redisTemplate.opsForValue().set(getKey(key), toStoreValue(value));
		if(usedCaffeineCache) {
			RedisPublishMessage message = new RedisPublishMessage();
			message.setData(new RedisCaffeineMessage(this.name, key));
			redisPublisher(message,ChannelTopicEnum.REDIS_CAFFEINE_DEL_TOPIC);
		}
	}

	/**
	 *
	 * @Title: put
	 * @Description: 带有失效时间
	 * @param: @param key
	 * @param: @param value
	 * @param: @param timeout
	 * @param: @param timeUnit
	 * @return: void
	 * @throws
	 */
	public void put(Object key, Object value,long timeout, TimeUnit timeUnit) {
		if (!super.isAllowNullValues() && value == null) {
			this.evict(key);
			return;
		}

		if (usedCaffeineCache) {
			caffeineCache.put(key, value);
			expires.put(key, System.currentTimeMillis() + TimeUnit.MILLISECONDS.convert(timeout, timeUnit));
		}

		redisTemplate.opsForValue().set(getKey(key), toStoreValue(value),timeout, timeUnit);
		if(usedCaffeineCache) {
			RedisPublishMessage message = new RedisPublishMessage();
			message.setData(new RedisCaffeineMessage(this.name, key));
			redisPublisher(message,ChannelTopicEnum.REDIS_CAFFEINE_DEL_TOPIC);
		}
	}

	/**
	 * 当缓存中不存在key时才放入缓存。返回值是当key存在时原有的数据
	 */
	@Override
	public ValueWrapper putIfAbsent(Object key, Object value) {
		if (!super.isAllowNullValues() && value == null) {
			this.evict(key);
			return null;
		}

		if (usedCaffeineCache) {
			PutIfAbsentFunction callable = new PutIfAbsentFunction(value);
			this.caffeineCache.get(key, callable);
        }

		redisTemplate.opsForValue().set(getKey(key), toStoreValue(value));
		if(usedCaffeineCache) {
			RedisPublishMessage message = new RedisPublishMessage();
			message.setData(new RedisCaffeineMessage(this.name, key));
			redisPublisher(message,ChannelTopicEnum.REDIS_CAFFEINE_DEL_TOPIC);
		}

		return toValueWrapper(value);
	}

	/**
	 * 删除缓存
	 */
	@Override
	public void evict(Object key) {

		/**
		 * 先清除redis中缓存数据，然后清除caffeine中的缓存，
		 * 避免短时间内如果先清除caffeine缓存后其他请求会再从redis里加载到caffeine中
		 * */
		redisTemplate.delete(getKey(key));
		if(usedCaffeineCache) {
			RedisPublishMessage message = new RedisPublishMessage();
			message.setData(new RedisCaffeineMessage(this.name, key));
			redisPublisher(message,ChannelTopicEnum.REDIS_CAFFEINE_DEL_TOPIC);
		}

		if(usedCaffeineCache) caffeineCache.invalidate(key.toString().replace(this.name.concat("::"), ""));
	}

	/**
	 * 删除缓存中的所有数据。需要注意的是，具体实现中只删除使用@Cacheable注解缓存的所有数据，不要影响应用内的其他缓存
	 */
	@Override
	public void clear() {
		// 先清除redis中缓存数据，然后清除caffeine中的缓存，避免短时间内如果先清除caffeine缓存后其他请求会再从redis里加载到caffeine中
		Set<Object> keys = redisTemplate.keys(this.name.concat(":*"));

		if (!CollectionUtils.isEmpty(keys)){
			redisTemplate.delete(keys);
		}

		if(usedCaffeineCache ) {
			RedisPublishMessage message = new RedisPublishMessage();
			message.setData(new RedisCaffeineMessage(this.name, null));
			redisPublisher(message,ChannelTopicEnum.REDIS_CAFFEINE_CLEAR_TOPIC);
		}

		if(usedCaffeineCache) caffeineCache.invalidateAll();
	}

	@Override
	protected Object lookup(Object key) {
		Object value = null;
		if(usedCaffeineCache) {
			if(checkExpiry(key)) {
				return null;
			}
			value = caffeineCache.getIfPresent(key);
		}

		if (value == null) {
			value = redisTemplate.opsForValue().get(getKey(key));
			if (value != null && usedCaffeineCache) {
				caffeineCache.put(key, value);
			}
		}
		return value;
	}

	/**
	 *
	 * @Title: redisPublisher
	 * @Description: redis发布消息
	 * @param: @param message
	 * @param: @param channelTopicEnum
	 * @return: void
	 * @throws
	 */
	public void redisPublisher(RedisPublishMessage message,ChannelTopicEnum channelTopicEnum) {
		redisTemplate.convertAndSend(channelTopicEnum.getChannelTopic().toString(), message);
	}

	/**
	 *
	 * @Title: clearLocal
	 * @Description: 通过redis消息清除caffeine缓存
	 * @param: @param key
	 * @return: void
	 * @throws
	 */
	public void clearLocal(Object key) {
		log.debug("clear local cache, the key is : {}", key);
		if (key == null) {
			caffeineCache.invalidateAll();
		}
		else {
			caffeineCache.invalidate(key);
			// 触发redis同步到caffeine
			this.get(key);
		}
	}

	/**
	 *
	 * @Title: getMap
	 * @Description: 返回所有缓存，只支持caffeine
	 * @param: @return
	 * @return: Map<Object, Object>
	 * @throws
	 */
	public Map<Object, Object> getMap(){
		if (usedCaffeineCache) {
			return caffeineCache.asMap();
		}
		return null;
	}

	/**
	 *
	 * @Title: keys
	 * @Description: 获取所有key
	 * @param: @param pattern
	 * @param: @return
	 * @return: Collection<String>
	 * @throws
	 */
	public Set<Object> keys(String pattern){
		if (usedCaffeineCache) {
			Map<Object, Object> map = this.getMap();
			if(MapUtils.isNotEmpty(map)) {
				String pattStr = pattern.replaceAll("\\*", ".*");
				return map.keySet().stream().filter(s -> Pattern.matches(pattStr, s.toString())).collect(Collectors.toSet());
			}
			return null;

		}else {
			if(!pattern.startsWith(this.name + "::")) {
				pattern = this.name + "::" + pattern;
			}
			return redisTemplate.keys(pattern);
		}
	}

	/**
     * @Description: 判断key是否存在
     **/
	public Boolean hasKey(String key) {
		if (usedCaffeineCache) {
			return caffeineCache.asMap().containsKey(key);
		}else {
			return redisTemplate.hasKey(getKey(key));
		}
	}

	/**
     * @Description: 是否过期
     **/
    private boolean checkExpiry(Object key){
        if(expires.containsKey(key)){
        	 long expiryTime = expires.get(key);

             boolean flag = System.currentTimeMillis() > expiryTime;
             if(flag){
             	caffeineCache.invalidate(key);
                expires.remove(key);

				 return true;
             }

			return false;
        }

		return false;
    }

	/**
	 *
	 * @Title: lock
	 * @Description: 锁
	 * @param: @param key
	 * @param: @return
	 * @return: boolean
	 * @throws
	 */
	public boolean lock(String key) {
		if(usedCaffeineCache) {
			return caffeineLock(key);
		}else {
			return redisLock(key);
		}
	}

	/**
	 *
	 * @Title: caffeineLock
	 * @Description: 单机caffeineLock
	 * @param: @param key
	 * @return: void
	 * @throws
	 */
	private boolean caffeineLock(String key) {
		ReentrantLock lock = new ReentrantLock();
		try {
			lock.lock();
			Object object = this.get(key);
			if (object == null) {
				this.put(key,1);
				return true;
			}else {
				this.put(key, Integer.parseInt(object.toString()) + 1);
				return false;
			}

		}catch (Exception e) {
			log.error("caffeineLock error:",e);
			return false;
		} finally {
			lock.unlock();
		}
	}

	/**
	 *
	 * @Title: redisLock
	 * @Description: redis 锁，简单实现，并不完善
	 * @param: @param key
	 * @param: @return
	 * @return: boolean
	 * @throws
	 */
	private boolean redisLock(String key) {
		return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(getKey(key), 1, CacheConstants.TEN_SECONDES, TimeUnit.SECONDS));
	}

	/**
	 *
	 * @Title: getKey
	 * @Description: 生成redis key
	 * @param: @param key
	 * @param: @return
	 * @return: String
	 * @throws
	 */
	private String getKey(Object key) {
		if(key.toString().startsWith(this.name + "::")) return key.toString();
		return this.name + "::" + key;
	}

	private class PutIfAbsentFunction implements Function<Object, Object> {

		private final Object value;

		@SuppressWarnings("unused")
		private boolean called;

		public PutIfAbsentFunction(Object value) {
			this.called = false;
			this.value = value;
		}

		@Override
		public Object apply(Object key) {
			this.called = true;
			return toStoreValue(this.value);
		}
	}
}
