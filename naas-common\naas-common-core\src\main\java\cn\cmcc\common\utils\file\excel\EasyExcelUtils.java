/**
 * @Title: EasyExcelUtils.java
 * @Package cn.cmcc.common.utils.file
 * 
 * <AUTHOR>
 * @date 2022年1月10日
 * @version V1.0
 */
package cn.cmcc.common.utils.file.excel;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.cmcc.common.pojo.ExcelUpdateSheetInfo;
import cn.cmcc.common.pojo.ExcelUpdateSheetInfo.ExcelUpdateCellInfo;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.file.FileUtils;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.commons.collections4.CollectionUtils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;

import cn.cmcc.common.pojo.ExeclWriteData;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Slf4j
public class EasyExcelUtils {

    /**
     *
     * @Title: write
     * @Description: 导出打个sheet
     * @param: @param data
     * @param: @throws IOException
     * @return: void
     * @throws
     */
    public static void write(ExeclWriteData data) throws IOException {

        // 这里需要设置不关闭流
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(data.getFileName()).head(head(data));
        excelWriterBuilder.excelType(ExcelTypeEnum.XLSX);
        if(CollectionUtils.isNotEmpty(data.getSheetWriteHandlers())) {
            data.getSheetWriteHandlers().forEach(handle->{
                excelWriterBuilder.registerWriteHandler(handle);
            });
        }
        if(CollectionUtils.isNotEmpty(data.getDataList())) {
            excelWriterBuilder.sheet(data.getSheetName()).doWrite(data.getDataList());
        }else {
            excelWriterBuilder.sheet(data.getSheetName()).doWrite(dataList(data.getDataMapList(), data.getDataStrMap()));
        }
    }

    /**
     *
     * @Title: write
     * @Description: 导出多个sheet
     * @param: @param fileName
     * @param: @param data
     * @param: @throws IOException
     * @return: void
     * @throws
     */
    public static void write(String fileName,List<ExeclWriteData> dataList) throws IOException {
        File file = FileUtils.getFile(fileName);
        if(!file.exists()) file.getParentFile().mkdirs();

        //新建ExcelWriter
        ExcelWriterBuilder builder = new ExcelWriterBuilder();
        builder.file(fileName);
        builder.excelType(ExcelTypeEnum.XLSX);
        builder.needHead(true);

        ExcelWriter excelWriter = builder.build();
        for (int index = 0; index <  dataList.size();++index) {
            ExeclWriteData data = dataList.get(index);
            //获取sheet对象
            ExcelWriterSheetBuilder writerSheetBuilder = EasyExcel.writerSheet(index, data.getSheetName())
                    .head(head(data)).automaticMergeHead(false);
            if(CollectionUtils.isNotEmpty(data.getSheetWriteHandlers())) {
                data.getSheetWriteHandlers().forEach(handle->{
                    writerSheetBuilder.registerWriteHandler(handle);
                });
            }
            WriteSheet sheetData = writerSheetBuilder.build();
            //向sheet写入数据 传入空list这样只导出表头
            if(CollectionUtils.isNotEmpty(data.getDataList())) {
                excelWriter.write(data.getDataList(),sheetData);
            }else {
                excelWriter.write(dataList(data.getDataMapList(), data.getDataStrMap()),sheetData);
            }
        }

        //关闭流
        excelWriter.finish();
    }

    /**
     * Excel修改
     * @param fileUrl
     * @param sheets
     */
    public static void edit(String fileUrl, List<ExcelUpdateSheetInfo> sheets) {
        if (StringUtils.isNotEmpty(fileUrl) && CollectionUtils.isNotEmpty(sheets)) {
            try(FileInputStream fis = FileUtils.openInputStream(FileUtils.getFile(fileUrl)); XSSFWorkbook wb = new XSSFWorkbook(fis);) {
                for (ExcelUpdateSheetInfo sheet : sheets) {
                    XSSFSheet xsssheet = wb.getSheet(sheet.getSheetName());
                    List<ExcelUpdateCellInfo> datas = sheet.getCellInfos();
                    if (xsssheet != null && CollectionUtils.isNotEmpty(datas)) {
                        for (ExcelUpdateCellInfo data : datas) {
                            if (data.getColumn() != null && data.getRow() != null) {
                                XSSFRow row = xsssheet.getRow(data.getRow());
                                if (row == null) {
                                    continue;
                                }
                                row.getCell(data.getColumn()).setCellValue(data.getValue());
                                if (data.getCellStyle() != null) {
                                    row.getCell(data.getColumn()).setCellStyle(data.getCellStyle());
                                }
                            }
                        }
                    }
                }
                FileOutputStream fos = FileUtils.openOutputStream(FileUtils.getFile(fileUrl));
                //写入
                wb.write(fos);
                fos.close();
            } catch (Exception e) {
            	log.error(e.getMessage(),e);
            }
        }
    }


    //设置表头
    private static List<List<String>> head(ExeclWriteData sheet) {
        if(CollectionUtils.isNotEmpty(sheet.getHeadList())) {
            return sheet.getHeadList();
        }

        List<List<String>> list = new ArrayList<List<String>>();

        for (String head : sheet.getHeadMap()) {
            List<String> headList = new ArrayList<String>();
            headList.add(head);
            list.add(headList);
        }
        return list;
    }

    //设置导出的数据内容
    private static List<List<Object>> dataList(List<Map<String, Object>> dataList, List<String> dataStrMap) {
        List<List<Object>> list = new ArrayList<List<Object>>();
        for (Map<String, Object> map : dataList) {
            List<Object> data = new ArrayList<Object>();
            for (int i = 0; i < dataStrMap.size(); i++) {
                data.add(map.get(dataStrMap.get(i)));
            }
            list.add(data);
        }
        return list;
    }

    /*
     * 默认样式
     */
    public static HorizontalCellStyleStrategy defaultStyles() {
        //TODO 默认样式
        //表头样式策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        //设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //表头前景设置淡蓝色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.index);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteFont.setFontName("宋体");
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);

        //内容样式策略策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置背景颜色白色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        // 设置垂直居中为居中对齐
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置左右对齐为靠左对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置单元格上下左右边框为细边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //创建字体对象
        WriteFont contentWriteFont = new WriteFont();
        //内容字体大小
        contentWriteFont.setFontName("宋体");
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 初始化表格样式
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        return horizontalCellStyleStrategy;
    }

    public static List<Map<String, Object>> read(File file, int sheetIndex, Map<String, String> headAttribute) {
    	 Map<Integer, String> indexFieldMap = new HashMap<>();
         List<Map<Integer, String>> originDataList = EasyExcel.read(file).sheet(sheetIndex).headRowNumber(0).doReadSync();
         if (originDataList == null || originDataList.size() <= 1) {
             return Collections.emptyList();
         }

         String attribute, value;
         for (Map.Entry<Integer, String> entry : originDataList.get(0).entrySet()) {
             value = processValue(entry.getValue());
             if ((attribute = headAttribute.get(value)) != null) {
                 indexFieldMap.put(entry.getKey(), attribute);
             }
         }
         List<Map<String, Object>> dataList = new ArrayList<>();
         boolean firstRow = true;
         for (Map<Integer, String> rowData : originDataList) {
             if (firstRow) {
                 firstRow = false;
                 continue;
             }
             Map<String, Object> dataMap = new HashMap<>(rowData.size());
             for (Map.Entry<Integer, String> entry : rowData.entrySet()) {
                 value = processValue(entry.getValue());
                 if ((attribute = indexFieldMap.get(entry.getKey())) != null) {
                     dataMap.put(attribute, value);
                 }
             }
             dataList.add(dataMap);
         }

         return dataList;
    }
    
    /**
     * 处理值
     *
     * @param value 值
     * @return 结果
     */
    public static final String processValue(String value) {
        if (value == null) return StringUtils.EMPTY;
        return value.trim();
    }
}
