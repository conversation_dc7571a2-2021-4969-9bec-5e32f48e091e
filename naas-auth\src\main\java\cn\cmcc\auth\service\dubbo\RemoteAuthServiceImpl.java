package cn.cmcc.auth.service.dubbo;

import cn.cmcc.auth.api.service.RemoteAuthService;
import cn.cmcc.auth.service.SysLoginService;
import cn.hutool.core.map.MapUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@DubboService
@RequiredArgsConstructor
public class RemoteAuthServiceImpl implements RemoteAuthService {

    @Autowired
    private SysLoginService sysLoginService;

    @Override
    public Object channelLogin() {
        return MapUtil.getStr(sysLoginService.ssoLogin("system"), "token");
    }
}
