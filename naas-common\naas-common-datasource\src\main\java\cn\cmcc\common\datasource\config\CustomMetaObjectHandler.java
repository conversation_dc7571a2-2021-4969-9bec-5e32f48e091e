package cn.cmcc.common.datasource.config;

import java.util.Date;

import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.ReflectionException;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;

import cn.cmcc.common.cache.context.SysConfigContext;
import cn.cmcc.common.context.SecurityUtils;
import cn.cmcc.common.utils.bean.ObjectUtils;
import cn.hutool.log.Log;

/**
 * 自定义sql字段填充器，自动填充创建修改相关字段
 *
 * <AUTHOR>
 * @date 2021/3/30 15:21
 */
public class CustomMetaObjectHandler implements MetaObjectHandler {

    private static final Log log = Log.get();

    private static final String CREATE_USER = "createUser";

    private static final String CREATE_TIME = "createTime";
    
    private static final String CREATE_USER_NAME = "createUserName";
    
    private static final String PROVINCE = "province";

    private static final String UPDATE_USER = "updateUser";

    private static final String UPDATE_TIME = "updateTime";

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
        	
            //设置createUser（BaseEntity)
            setFieldValByName(CREATE_USER, this.getUserUniqueId(), metaObject);
                        
            //设置createTime（BaseEntity)
            setFieldValByName(CREATE_TIME, new Date(), metaObject);
            
            setFieldValByName(CREATE_USER_NAME, this.getLoginUserName(), metaObject);
            //setFieldValByName(PROVINCE,SysConfigContext.getCurrentProvinceCode() , metaObject);

        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无相关字段，不做处理");
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            //设置updateUser（BaseEntity)
            setFieldValByName(UPDATE_USER, this.getUserUniqueId(), metaObject);
            //设置updateTime（BaseEntity)
            setFieldValByName(UPDATE_TIME, new Date(), metaObject);
        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无相关字段，不做处理");
        }
    }

    /**
     * 获取用户唯一id
     */
    private Long getUserUniqueId() {
        try {
            return SecurityUtils.getUserId();
        } catch (Exception e) {
            //如果获取不到就返回-1
            return -1L;
        }
    }
    
    /**
     * 获取用户唯一id
     */
    private String getLoginUserName() {
        try {
            return SecurityUtils.getUserName();
        } catch (Exception e) {
            //如果获取不到就返回-1
            return "admin";
        }
    }
}