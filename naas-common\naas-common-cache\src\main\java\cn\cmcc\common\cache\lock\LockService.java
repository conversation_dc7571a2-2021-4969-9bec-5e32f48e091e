package cn.cmcc.common.cache.lock;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * String lock service
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Service
@Slf4j
public class LockService {

    private static final String DEFAULT_KEY_PREFIX = "lock_prefix";
    
    /**
     * Thread id + key, RLock
     */
    private final Map<String, RLock> THREAD_LOCK = new ConcurrentHashMap<>(32, 1F);

    @Autowired
    private RedissonClient redissonClient;
    
    /**
     * 
     * 加锁，锁存在则返回失败，不等待
     *
     * @Title: lock   
     * @param key
     * @return
     * @throws InterruptedException boolean
     * @throws
     */
    public boolean lock(String key){
        return tryLock(DEFAULT_KEY_PREFIX, key, 0,300, TimeUnit.SECONDS);
    }

    /**
     * 尝试获得锁
     *
     * @param key      键
     * @param waitTime 等待时间，小于等于0，则不等待
     * @param unit     时间单位
     * @return 是否获得
     */
    public boolean tryLock(String key, long waitTime,long leaseTime, TimeUnit unit){
        return tryLock(DEFAULT_KEY_PREFIX, key, waitTime,leaseTime, unit);
    }
    
    /**
     * 尝试获得锁
     *
     * @param prefix   前缀
     * @param key      键
     * @param waitTime 等待时间，小于等于0，则不等待
     * @param unit     时间单位
     * @return 是否获得
     */
    public boolean tryLock(String prefix, String key, long waitTime,long leaseTime, TimeUnit unit){        
        RLock lock = redissonClient.getLock(prefix + ":" + key);
        boolean get = false;
		try {
			get = lock.tryLock(waitTime,leaseTime, unit);
			if (get) THREAD_LOCK.put(String.format("%d_%s", Thread.currentThread().getId(), prefix + ":" + key), lock);
		} catch (InterruptedException e) {
			log.error("tryLock error:",e);
			Thread.currentThread().interrupt();
		}
        return get;
    }


    /**
     * 释放锁，必须在 finally 中调用，调用时无需判断是否获得锁
     *
     * @param key 键
     */
    public void unlock(String key) {        
    	unlock(DEFAULT_KEY_PREFIX,key);
    }
    
    /**
     * 释放锁，必须在 finally 中调用，调用时无需判断是否获得锁
     *
     * @param prefix 前缀
     * @param key 键
     */
    public void unlock(String prefix, String key) {
    	RLock lock = THREAD_LOCK.remove(String.format("%d_%s", Thread.currentThread().getId(), prefix + ":" + key));
        if (lock != null && lock.isLocked()) lock.unlock();
    }

}
