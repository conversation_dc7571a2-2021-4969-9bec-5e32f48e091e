package cn.cmcc.common.config;

import cn.cmcc.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "cmcc")
@Slf4j
public class NaasConfig {
    /**
     * 项目路径
     */
    public static String SERVER_PORT_CONTEXT = "";

    // 静态字段，保持向后兼容
    public static String SERVER_ID = IdUtils.fastSimpleUUID();
    private boolean analytic;
    private boolean checkPerimissonSwitch;
    private static volatile NaasConfig instance;// NOSONAR
    // 静态缓存字段，用于向后兼容
    private static String staticStorageType;
    private static String staticVersion;
    private static List<String> staticSpareDirs = new ArrayList<>();
    private static String staticProfile;
    // 实例字段，支持配置刷新
    private String storageType;
    private String version;
    private List<String> spareDirs = new ArrayList<>();
    private String profile;

    public NaasConfig() {
        instance = this;
        updateStaticFields();
    }

    // 静态方法，保持向后兼容
    public static List<String> getSpareDirs() {
        return staticSpareDirs != null ? new ArrayList<>(staticSpareDirs) : new ArrayList<>();
    }

    // 实例方法
    public void setSpareDirs(Set<String> spareDirs) {
        this.spareDirs.clear();
        this.spareDirs.addAll(spareDirs);
        this.spareDirs = this.spareDirs.stream().distinct().collect(Collectors.toList());
        updateStaticFields();
    }

    public static String getProfile() {
        return staticProfile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
        if (this.spareDirs.isEmpty() || !this.spareDirs.get(0).equals(profile)) {
            this.spareDirs.add(0, profile);
        }
        updateStaticFields();
    }

    public static String getStorageType() {
        return staticStorageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
        updateStaticFields();
    }

    public static String getVersion() {
        return staticVersion;
    }

    public void setVersion(String version) {
        this.version = version;
        updateStaticFields();
    }

    // 获取当前实例的方法
    public static NaasConfig getInstance() {
        return instance;
    }

    /**
     * 配置刷新事件监听器
     */
    @EventListener
    public void handleConfigRefresh(RefreshScopeRefreshedEvent event) {
        log.info("Configuration refreshed, updating NaasConfig static fields: {}", event.getName());
        updateStaticFields();
    }

    public boolean isAnalytic() {
        return analytic;
    }

    public void setAnalytic(boolean analytic) {
        this.analytic = analytic;
    }

    public boolean isCheckPerimissonSwitch() {
        return checkPerimissonSwitch;
    }

    public void setCheckPerimissonSwitch(boolean checkPerimissonSwitch) {
        this.checkPerimissonSwitch = checkPerimissonSwitch;
    }

    /**
     * 更新静态字段，保持向后兼容
     */
    private void updateStaticFields() {
        staticStorageType = this.storageType;
        staticVersion = this.version;
        staticSpareDirs = new ArrayList<>(this.spareDirs);
        staticProfile = this.profile;
        log.info("Updated NaasConfig static fields - storageType: {}, version: {}, profile: {}",
                staticStorageType, staticVersion, staticProfile);
    }
}
