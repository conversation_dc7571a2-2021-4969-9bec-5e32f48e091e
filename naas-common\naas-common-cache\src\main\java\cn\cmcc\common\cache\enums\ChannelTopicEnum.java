/**
 * @Title: ChannelTopicEnum.java
 * @Package cn.cmcc.common.core.cache
 * 
 * <AUTHOR>
 * @date 2021年9月3日
 * @version V1.0
 */
package cn.cmcc.common.cache.enums;

import org.springframework.data.redis.listener.ChannelTopic;

public enum ChannelTopicEnum {

	REDIS_CAFFEINE_DEL_TOPIC("redis:caffeine:del:topic", "删除redis缓存消息频道"),
    REDIS_CAFFEINE_CLEAR_TOPIC("redis:caffeine:clear:topic", "清空redis缓存消息频道"),
	REDIS_MYBITS_RELOAD_TOPIC("redis:mybtis:reloadr:topic", "SQL修改，mybits重新加载"),
	REDIS_ANONYMOUS_VISIT_RELOAD_TOPIC("redis:anonymous:reload:topic", "接口匿名访问重新加载"),
	DATA_SOURCE_RELOAD_TOPIC("datasource:reload:topic", "多数据源新增修改加载"),
	DATA_SOURCE_DELETE_TOPIC("datasource:delete:topic", "多数据源新增修改加载"),
    SSE_TOPIC("sse:send:topic","sse消息推送");

    String code;
    String name;

    ChannelTopicEnum(String code, String name) {
        this.code = name;
        this.name = name;
    }

    public ChannelTopic getChannelTopic() {
        return new ChannelTopic(code);
    }

    public static ChannelTopicEnum getChannelTopicEnum(String channelTopic) {
        for (ChannelTopicEnum awardTypeEnum : ChannelTopicEnum.values()) {
            if (awardTypeEnum.getCode().equals(channelTopic)) {
                return awardTypeEnum;
            }
        }
        return null;
    }

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

    
}
