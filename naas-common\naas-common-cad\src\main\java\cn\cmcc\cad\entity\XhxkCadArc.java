package cn.cmcc.cad.entity;

import java.io.Serializable;

import cn.cmcc.cad.service.ICadService;
import lombok.Data;

@Data
public class XhxkCadArc extends XhxkCadCircle implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private double startAngle;
	private double endAngle;
	private int angleStep = 4;

	public XhxkCadArc(String id, String type, String block,String layer, XhxkCadPoint centerPoint, double radius, double startAngle, double endAngle) {
		super(id, type, block,layer, centerPoint, radius);
		this.startAngle = startAngle;
		this.endAngle = endAngle;
		setDeviceCode(centerPoint.getX()+"/"+centerPoint.getY()+"/"+radius+"/"+startAngle+"/"+endAngle);
	}
	
	@Override
	public void transfer(ICadService cadService,Double scale, XhxkCadPoint minPoint) {
		// TODO Auto-generated method stub
		super.transfer(cadService,scale, minPoint);
	}


}
