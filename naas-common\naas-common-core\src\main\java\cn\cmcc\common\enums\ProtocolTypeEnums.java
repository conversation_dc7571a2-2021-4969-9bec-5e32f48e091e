package cn.cmcc.common.enums;

import cn.cmcc.common.utils.StringUtils;

/**
 *
 *
 * <AUTHOR>
 * @date 2023年2月21日
 * @version V1.0
 */
public enum ProtocolTypeEnums {
	HTTP("http"),
	WEBSERVICE("webservice");

	/**
     * 值
     */
    private final String value;

    ProtocolTypeEnums(String value) {
        this.value = value;
    }

	public String getValue() {
		return value;
	}
	
	public static ProtocolTypeEnums getProtocolType(String value) {
		if(StringUtils.isEmpty(value)) return null;
        for (ProtocolTypeEnums protocolTypeEnum : ProtocolTypeEnums.values()) {
            if (protocolTypeEnum.getValue().equals(value.toLowerCase())) {
                return protocolTypeEnum;
            }
        }
        return null;
    }
}
