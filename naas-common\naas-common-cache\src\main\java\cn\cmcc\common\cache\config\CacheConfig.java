package cn.cmcc.common.cache.config;

import java.io.IOException;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.cache.support.RedisCaffeineCacheManager;
import cn.cmcc.common.utils.StringUtils;

/**
 * redis配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class CacheConfig extends CachingConfigurerSupport
{
	@Value("${spring.redis.host}")
    private String address;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.database}")
    private Integer database;

    @Value("${spring.redis.timeout}")
    private Integer timeout;

    /**
     * 连接池最大连接数（使用负值表示没有限制）
     */
    @Value("${spring.redis.pool.maxActive:10000}")
    private Integer maxActive;

    @Value("${spring.redis.pool.maxWait:3000}")
    private Integer maxWait;

    @Value("${spring.redis.pool.minIdle:100}")
    private Integer minIdle;

	@Bean
    public RedissonClient redisson() throws IOException {
		Config config = new Config();
        config.useSingleServer()
                .setAddress(address())
                .setDatabase(database)
                .setConnectionPoolSize(maxActive)   // 设置连接池最大连接数
                .setConnectionMinimumIdleSize(minIdle)  // 设置连接池最小空闲连接数
                .setIdleConnectionTimeout(maxWait) // 设置连接空闲超时时间，单位毫秒
                .setConnectTimeout(timeout);
        if(StringUtils.isNotEmpty(password)) config.useSingleServer().setPassword(password);
        return Redisson.create(config);
    }

    private String address() {
        return "redis://" + address + ":" + port;
    }

    @Bean
    public RedissonConnectionFactory redissonConnectionFactory(RedissonClient redisson) {
        return new RedissonConnectionFactory(redisson);
    }

    /**
     * RedisTemplate
     */
    @Bean
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory connectionFactory)
    {
    	GenericFastJsonRedisSerializer serializer = new GenericFastJsonRedisSerializer();
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setValueSerializer(serializer);
        template.setHashValueSerializer(serializer);
        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(StringRedisSerializer.UTF_8);
        template.setHashKeySerializer(StringRedisSerializer.UTF_8);
        template.afterPropertiesSet();
        return template;
    }


    @Bean
    public CacheManager redisCacheManager(RedisTemplate<Object, Object> redisTemplate) {
        return new RedisCaffeineCacheManager(CachesEnums.values(),redisTemplate);
    }

    @Bean
    @ConditionalOnBean({RedisTemplate.class,CacheManager.class})
    public RedisMessageListenerContainer redisContainer(RedisConnectionFactory redisConnectionFactory,
    		RedisTemplate<Object, Object> redisTemplate,
    		CacheManager redisCaffeineCacheManager) {
        final RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        return container;
    }

}
