package cn.cmcc.cad.entity;

import java.io.Serializable;

import cn.cmcc.cad.service.ICadService;
import lombok.Data;

@Data
public class XhxkCadCircle extends XhxkCadBaseEntity implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private XhxkCadPoint centerPoint;
	private double radius;
	private int angleStep = 4;
	

	public XhxkCadCircle(String id, String type, String block,String layer, XhxkCadPoint centerPoint, double radius) {
		super(id, type, block,layer);
		this.centerPoint = centerPoint;
		this.radius = radius;
		setPosition(centerPoint);
	}
	
	@Override
	public void transfer(ICadService cadService,Double scale, XhxkCadPoint minPoint) {
		// TODO Auto-generated method stub
		super.transfer(cadService,scale, minPoint);
		this.centerPoint = cadService.transfer(centerPoint, scale, minPoint);
	}

}
