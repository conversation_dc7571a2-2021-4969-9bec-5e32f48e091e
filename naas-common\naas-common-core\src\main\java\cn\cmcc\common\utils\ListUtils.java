package cn.cmcc.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;

import cn.cmcc.common.utils.funciton.Consumer;

public class ListUtils {
    
	/**
	 * list 转 Stirng
	 * @param <T>
	 * @param list
	 * @param separator
	 * @return 
	 * @return
	 */
	public static  <T>  String listToString(List<T> list, char separator) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < list.size(); i++) {
			if (i == list.size() - 1) {
				sb.append("'"+list.get(i)+"'");
			} else {
				sb.append("'"+list.get(i)+"'");
				sb.append(separator);
			}
		}
		return sb.toString();
	}
	
	
	/**
	 * list 转 Stirng
	 * @param <T>
	 * @param list
	 * @param separator
	 * @return 
	 * @return
	 */
	public static  <T>  String listToStr(List<T> list, char separator) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < list.size(); i++) {
			if (i == list.size() - 1) {
				sb.append(""+list.get(i)+"");
			} else {
				sb.append(""+list.get(i)+"");
				sb.append(separator);
			}
		}
		return sb.toString();
	}
	
	/**
	 * list 切片
	 * @param <T>
	 * @param list
	 * @param pageSize
	 * @return 
	 * @return
	 */
	public static <T> List<List<T>> splitList(List<T> list, int pageSize) {  
        int listSize = list.size(); // list的大小  
        int page = (listSize + (pageSize - 1)) / pageSize;// 页数  
        List<List<T>> listArray = new ArrayList<List<T>>();// 创建list数组,用来保存分割后的list  
        for (int i = 0; i < page; i++) { // 按照数组大小遍历  
            List<T> subList = new ArrayList<T>(); // 数组每一位放入一个分割后的list  
            for (int j = 0; j < listSize; j++) {// 遍历待分割的list  
                int pageIndex = ((j + 1) + (pageSize - 1)) / pageSize;// 当前记录的页码(第几页)  
                if (pageIndex == (i + 1)) {// 当前记录的页码等于要放入的页码时  
                    subList.add(list.get(j)); // 放入list中的元素到分割后的list(subList)  
                }  
                if ((j + 1) == ((j + 1) * pageSize)) {// 当放满一页时退出当前循环  
                    break;  
                }  
            }  
            listArray.add(subList);// 将分割后的list放入对应的数组的位中  
        }  
        return listArray;  
    } 
	
	public static <E> List<E> mergeList(final List<? extends E> list1, final List<? extends E> list2) {
        final ArrayList<E> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list1) ) {
        	result.addAll(list1);
		 }
		 
		 if(CollectionUtils.isNotEmpty(list2) ) {
			 result.addAll(list2);
		 }
        return result;
	}
	
	
	/**
	 * list 转 Stirng
	 * @param <T>
	 * @param list
	 * @param separator
	 * @return 
	 * @return
	 */
	public static  <T>  String listToString(List<T> list) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < list.size(); i++) {
			if (i == list.size() - 1) {
				sb.append(list.get(i));
			} else {
				sb.append(list.get(i));
				sb.append(",");
			}
		}
		return sb.toString();
	}

	/**
	 * 遍历
	 * 
	 * @param <T>
	 * @param list
	 * @param action 
	 * void
	 */
	public static <T> void forEach(List<T> list,Consumer<? super T> action) {
		 if(CollectionUtils.isNotEmpty(list) && action != null) {
			 for(int index = 0; index < list.size(); ++ index) {
				 action.accept(list.get(index), index);
			 }
		 }
	}

	/**
	 * list 转 用于IN后面的Stirng
	 * @param list
	 * @return
	 * @return
	 */
	public static String listToInStr(List<String> list) {
		if (list == null || list.isEmpty()) {
			return "''"; // 如果列表为空，返回单引号包裹的空字符串
		}
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < list.size(); i++) {
			sb.append("'").append(list.get(i)).append("'");
			if (i < list.size() - 1) {
				sb.append(", ");
			}
		}
		return sb.toString();
	}

}
