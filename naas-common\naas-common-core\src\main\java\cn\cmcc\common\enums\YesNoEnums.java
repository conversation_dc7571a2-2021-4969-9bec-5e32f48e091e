/**
 * @Title: YesNoEnums.java
 * @Package cn.cmcc.common.enums
 * 
 * <AUTHOR>
 * @date 2022年5月18日
 * @version V1.0
 */
package cn.cmcc.common.enums;

public enum YesNoEnums {

	YES("Y", "是"), NO("N", "否");

	String code;
	String message;

	YesNoEnums(String code, String message) {
	    this.code = code;
	    this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	
}
