package cn.cmcc.common.security.handler;

import javax.servlet.http.HttpServletRequest;

import cn.cmcc.common.exception.QueryCodePermissionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import cn.cmcc.common.constant.HttpStatus;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.exception.LockException;
import cn.cmcc.common.exception.ServiceException;
import cn.cmcc.common.exception.auth.NotPermissionException;
import cn.cmcc.common.exception.auth.NotRoleException;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.web.domain.AjaxResult;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 通用查询编码权限异常处理
     */
    @ExceptionHandler(QueryCodePermissionException.class)
    public AjaxResult handleQueryCodePermissionException(QueryCodePermissionException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',查询编码权限异常'{}'", requestURI, e.getMessage());
        return AjaxResult.error(HttpStatus.WARN, e.getMessage());
    }


    /**
     * 业务异常
     */
    @ExceptionHandler(CustomException.class)
    public AjaxResult businessException(CustomException e)
    {
        log.error(e.getMessage(), e);
    	if(e.getCode() == null) {
            return AjaxResult.warn(e.getMessage());
    	}
    	return new AjaxResult(e.getCode(),e.getMessage(),e.getData());
    }

    @ExceptionHandler(LockException.class)
    public AjaxResult lockException(LockException e)
    {
       return AjaxResult.warn(e.getMessage());
    }

    /**
     * 权限码异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public AjaxResult handleNotPermissionException(NotPermissionException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限码校验失败'{}'", requestURI, e.getMessage(), e);// NOSONAR
        return AjaxResult.warn(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 角色权限异常
     */
    @ExceptionHandler(NotRoleException.class)
    public AjaxResult handleNotRoleException(NotRoleException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',角色权限校验失败'{}'", requestURI, e.getMessage(), e);// NOSONAR
        return AjaxResult.warn(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
            HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e.getMethod(), e);// NOSONAR
        return AjaxResult.error(String.format("请求地址%s,不支持%s请求", requestURI, e.getMethod()));
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e, HttpServletRequest request)
    {
        log.error(e.getMessage(), e);
        Integer code = e.getCode();
        return StringUtils.isNotNull(code) ? AjaxResult.error(code, e.getMessage()) : AjaxResult.error(e.getMessage());
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestURI, e);// NOSONAR
        if(e.getCause() != null && e.getCause() instanceof CustomException ){
            return AjaxResult.warn(e.getCause().getMessage());
        }
        return AjaxResult.error("服务器内部错误.运行时异常.");
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);// NOSONAR
        if(e.getCause() != null && e.getCause() instanceof CustomException ){
            return AjaxResult.warn(e.getCause().getMessage());
        }
        return AjaxResult.error("服务器内部错误");
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return AjaxResult.error(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException ex)
    {
        log.error(ex.getMessage(), ex);
        StringBuilder sb = new StringBuilder();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((org.springframework.validation.FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            sb.append(fieldName).append(":").append(errorMessage).append(";");
        });
        return AjaxResult.warn(sb.toString());
    }
}
