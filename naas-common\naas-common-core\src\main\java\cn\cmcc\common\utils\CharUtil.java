package cn.cmcc.common.utils;

import java.util.Objects;

/**
 * 字符通用工具类
 *
 * <AUTHOR>
public class CharUtil {

	private static boolean isChinese(char c) {
		Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
		return Objects.equals(ub, Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS)
				|| Objects.equals(ub, Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS)
				|| Objects.equals(ub, Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A)
				|| Objects.equals(ub, Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B)
				|| Objects.equals(ub, Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION)
				|| Objects.equals(ub, Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS)
				|| Objects.equals(ub, Character.UnicodeBlock.GENERAL_PUNCTUATION);
	}

	public static boolean containChinese(String strName) {
		char[] ch = strName.toCharArray();
		for (char c : ch) {
			if (isChinese(c)) {
				return true;
			}
		}
		return false;
	}
}
