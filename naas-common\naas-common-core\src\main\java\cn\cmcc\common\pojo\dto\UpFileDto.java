package cn.cmcc.common.pojo.dto;

import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

@Data
public class UpFileDto {
    /**
     * 上传文件
     */
    @JSONField(serialize =false)
    private MultipartFile file;


    /**
     * 上传的加密文件
     */
    @JSONField(serialize =false)
    private MultipartFile fileKey;

    /**
     * 存储路径
     */
    private String path;

    /*
     * 文件名称
     */
    private String fileName;
    

    /**
     * 当前记录id
     */
    private String id;
    
    /** 对象类型 */
    private String objectType;

    /** 对象id */
    private String objectId;
    
    /**
     * 模块
     */
    private String module;


    
    /**
     * 站点编号
     */
    private String siteIdCmcc;
    
    /**
     * 阶段
     */
    private String stageCode;
    
    /**
     * 节点
     */
    private String nodeId;
    
    /**
     */
    private String businessType = "upload";
    
    /**
     * 分类
     */
    private String classify;
    
    /**
     * 校验重复上传
     */
    private boolean checkRepeat = false;

}
