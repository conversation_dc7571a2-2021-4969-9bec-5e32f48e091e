package cn.cmcc.auth.service;

import java.io.IOException;

import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.pojo.LoginBody;
import cn.cmcc.common.web.domain.AjaxResult;

/**
 * 验证码处理
 * 
 * <AUTHOR>
 */
public interface CapchaCodeService
{
    /**
     * 生成验证码
     */
    public AjaxResult createCapcha() throws IOException, CustomException;

    /**
     * 校验验证码
     */
    public AjaxResult checkCapcha(LoginBody form) throws CustomException;
}
