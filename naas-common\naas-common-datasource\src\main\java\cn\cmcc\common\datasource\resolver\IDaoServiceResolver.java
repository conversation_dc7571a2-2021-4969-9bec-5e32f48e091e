package cn.cmcc.common.datasource.resolver;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description:
 * @author: niuyh
 * @date: 2023/1/29
 */
public interface IDaoServiceResolver {
    /**
     * get corresponding {@link IService} for a given {@code  entity}
     *
     * @param entity
     * @param <T>    entity type
     * @return
     */
    <T> IService<T> resolve(T entity);
}
