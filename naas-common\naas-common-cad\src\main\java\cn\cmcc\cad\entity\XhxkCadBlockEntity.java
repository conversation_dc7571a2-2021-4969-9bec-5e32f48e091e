package cn.cmcc.cad.entity;

import java.io.Serializable;
import java.util.List;

import cn.cmcc.cad.cuting.entity.CadCutingBoundaryConfig;
import lombok.Data;

@Data
public class XhxkCadBlockEntity implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private String blockName;
	private List<XhxkCadBaseEntity> blockEntities;
	/**
	 * 冗余字段，用于自动切图，图框基本信息
	 */
	private CadCutingBoundaryConfig boundaryConfig;

	public XhxkCadBlockEntity(String blockName, List<XhxkCadBaseEntity> blockEntities) {
		super();
		this.blockName = blockName;
		this.blockEntities = blockEntities;
	}

}
