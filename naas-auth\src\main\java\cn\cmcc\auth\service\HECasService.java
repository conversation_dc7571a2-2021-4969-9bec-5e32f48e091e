package cn.cmcc.auth.service;

import lombok.Data;
import org.xml.sax.SAXException;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.io.IOException;

/**
 * <p>
 * CAS 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@WebService(targetNamespace = "http://services.cas.he")
public interface HECasService {

    /**
     * 注销
     *
     * @param root 根
     * @return 是否成功
     */
    @WebMethod
    boolean logout(@WebParam(name = "root") Root root) throws IOException, SAXException;

    // Root
    @Data
    public static class Root {

        private String ticket;

    }

}
