package cn.cmcc.flow.service.imp;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.activiti.engine.delegate.VariableScope;
import org.activiti.engine.impl.persistence.entity.VariableInstance;

public class VariableScopeImpl implements VariableScope {

	private Map<String, Object> variables=new HashMap<String, Object>();
	
    public VariableScopeImpl(Map<String, Object> variables){
        this.variables=variables;
    }
    
    @Override
    public Map<String, Object> getVariables() {
        // TODO Auto-generated method stub
        return variables;
    }
 
    @Override
    public Map<String, VariableInstance> getVariableInstances() {
        return null;
    }
 
    @Override
    public Map<String, Object> getVariables(Collection<String> variableNames) {
        // TODO Auto-generated method stub
        return variables;
    }
 
    @Override
    public Map<String, VariableInstance> getVariableInstances(Collection<String> variableNames) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Map<String, Object> getVariables(Collection<String> variableNames, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return variables;
    }
 
    @Override
    public Map<String, VariableInstance> getVariableInstances(Collection<String> variableNames,
            boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Map<String, Object> getVariablesLocal() {
        // TODO Auto-generated method stub
        return variables;
    }
 
    @Override
    public Map<String, VariableInstance> getVariableInstancesLocal() {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Map<String, Object> getVariablesLocal(Collection<String> variableNames) {
        // TODO Auto-generated method stub
        return variables;
    }
 
    @Override
    public Map<String, VariableInstance> getVariableInstancesLocal(Collection<String> variableNames) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Map<String, Object> getVariablesLocal(Collection<String> variableNames, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return variables;
    }
 
    @Override
    public Map<String, VariableInstance> getVariableInstancesLocal(Collection<String> variableNames,
            boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Object getVariable(String variableName) {
        // TODO Auto-generated method stub
        return variables.get(variableName);
    }
 
    @Override
    public VariableInstance getVariableInstance(String variableName) {
        return new VariableInstanceImpl(variableName,getVariable(variableName));
    }
 
    @Override
    public Object getVariable(String variableName, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return this.getVariable(variableName);
    }
 
    @Override
    public VariableInstance getVariableInstance(String variableName, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Object getVariableLocal(String variableName) {
        // TODO Auto-generated method stub
        return this.getVariable(variableName);
    }
 
    @Override
    public VariableInstance getVariableInstanceLocal(String variableName) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Object getVariableLocal(String variableName, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return this.getVariable(variableName);
    }
 
    @Override
    public VariableInstance getVariableInstanceLocal(String variableName, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public <T> T getVariable(String variableName, Class<T> variableClass) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public <T> T getVariableLocal(String variableName, Class<T> variableClass) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Set<String> getVariableNames() {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Set<String> getVariableNamesLocal() {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public void setVariable(String variableName, Object value) {
        // TODO Auto-generated method stub
        variables.put(variableName,value);
    }
 
    @Override
    public void setVariable(String variableName, Object value, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        this.setVariable(variableName, value);
    }
 
    @Override
    public Object setVariableLocal(String variableName, Object value) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public Object setVariableLocal(String variableName, Object value, boolean fetchAllVariables) {
        // TODO Auto-generated method stub
        return null;
    }
 
    @Override
    public void setVariables(Map<String, ? extends Object> variables) {
        // TODO Auto-generated method stub
        this.variables= (Map<String, Object>) variables;
    }
 
    @Override
    public void setVariablesLocal(Map<String, ? extends Object> variables) {
        // TODO Auto-generated method stub
        this.variables=(Map<String, Object>) variables;
    }
 
    @Override
    public boolean hasVariables() {
        // TODO Auto-generated method stub
        return variables.size()>0;
    }
 
    @Override
    public boolean hasVariablesLocal() {
        // TODO Auto-generated method stub
        return variables.size()>0;
    }
 
    @Override
    public boolean hasVariable(String variableName) {
        // TODO Auto-generated method stub
        return variables.containsKey(variableName);
    }
 
    @Override
    public boolean hasVariableLocal(String variableName) {
        // TODO Auto-generated method stub
        return variables.containsKey(variableName);
    }
 
    @Override
    public void removeVariable(String variableName) {
        // TODO Auto-generated method stub
        
    }
 
    @Override
    public void removeVariableLocal(String variableName) {
        // TODO Auto-generated method stub
        
    }
 
    @Override
    public void removeVariables(Collection<String> variableNames) {
        // TODO Auto-generated method stub
        
    }
 
    @Override
    public void removeVariablesLocal(Collection<String> variableNames) {
        // TODO Auto-generated method stub
        
    }
 
    @Override
    public void removeVariables() {
        // TODO Auto-generated method stub
        
    }
 
    @Override
    public void removeVariablesLocal() {
        // TODO Auto-generated method stub
        
    }

	@Override
	public void setTransientVariable(String variableName, Object variableValue) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void setTransientVariableLocal(String variableName, Object variableValue) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void setTransientVariables(Map<String, Object> transientVariables) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Object getTransientVariable(String variableName) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map<String, Object> getTransientVariables() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setTransientVariablesLocal(Map<String, Object> transientVariables) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Object getTransientVariableLocal(String variableName) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map<String, Object> getTransientVariablesLocal() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void removeTransientVariableLocal(String variableName) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void removeTransientVariable(String variableName) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void removeTransientVariables() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void removeTransientVariablesLocal() {
		// TODO Auto-generated method stub
		
	}


}
