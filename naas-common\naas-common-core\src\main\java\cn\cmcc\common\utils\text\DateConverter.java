package cn.cmcc.common.utils.text;

import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;

import cn.cmcc.common.utils.DateUtils;

/**  
 * 
 * <AUTHOR>
 * @date 2024-02-18 04:05:12
*/
@SuppressWarnings("rawtypes")
public class DateConverter  extends AbstractBeanField{

	@Override
	protected Object convert(String value) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
		return DateUtils.parseDate(value);
	}

}
