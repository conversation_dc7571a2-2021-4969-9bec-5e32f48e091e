package cn.cmcc.cad.entity;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import cn.cmcc.cad.service.ICadService;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.SerializeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class XhxkCadInsertObject extends XhxkCadBaseEntity implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private XhxkCadPoint insertionPoint;
	private List<XhxkCadBaseEntity> childObjects;//主要表示模板中的动态值，一般是文字
	private double scaleX;
	private double scaleY;
	private double rotationAngle;
	private String name;
	private List<XhxkCadBaseEntity> modelEntities;//主要表示模板中的内容，如线条等
	private List<XhxkCadBaseEntity> indoorCadEntities;//表示模板中的线条按真实坐标还原

	public XhxkCadInsertObject(String id, String type, String block,String layer, XhxkCadPoint insertionPoint, List<XhxkCadBaseEntity> childObjects,List<XhxkCadBaseEntity> modelEntities, double scaleX, double scaleY, double rotationAngle, String name) {
		super(id, type, block,layer);
		this.insertionPoint = insertionPoint;
		this.childObjects = childObjects;
		this.scaleX = scaleX;
		this.scaleY = scaleY;
		this.rotationAngle = rotationAngle;
		this.name = name;
		this.modelEntities = modelEntities;
		//setPosition(insertionPoint);
	}
	@Override
	public void transfer(ICadService cadService,Double scale, XhxkCadPoint minPoint) {
		// TODO Auto-generated method stub
		super.transfer(cadService,scale, minPoint);
		this.insertionPoint = cadService.transfer(insertionPoint, scale, minPoint);
		List<XhxkCadBaseEntity> transChildObjects = new LinkedList<XhxkCadBaseEntity>();
		for(XhxkCadBaseEntity entity:childObjects) {
			entity.transfer(cadService,scale, minPoint);
			transChildObjects.add(entity);
		}
		this.childObjects = transChildObjects;
	}
	
	/**
	 * 初始化完成之后数据处理
	 */
	public void initialize() {
		if((this.childObjects!=null)&&!this.childObjects.isEmpty()) {
			for(XhxkCadBaseEntity entity:this.childObjects) {
				entity.setLayer(this.getLayer());
				entity.setParentId(this.getId());
			}
		}
		if((this.modelEntities!=null)&&!this.modelEntities.isEmpty()) {
			for(XhxkCadBaseEntity entity:this.modelEntities) {
				entity.setLayer(this.getLayer());
				entity.setParentId(this.getId());
			}
		}
		if((this.indoorCadEntities!=null)&&!this.indoorCadEntities.isEmpty()) {
			for(XhxkCadBaseEntity entity:this.indoorCadEntities) {
				entity.setLayer(this.getLayer());
				entity.setParentId(this.getId());
			}
		}
	}


	/**
	 * 对象缩放后再旋转,原则：缩放只做等比缩放，以X值为参数
	 * @param scale
	 * @param angle
	 * @return
	 */
	public List<XhxkCadBaseEntity> scaleAndRotate(ICadService cadService) {
		
		long start = System.currentTimeMillis();
		List<XhxkCadBaseEntity> scaleEntities = new LinkedList<XhxkCadBaseEntity>();
		
		for (XhxkCadBaseEntity modelEntity :deepCopy( modelEntities)) {
			XhxkCadBaseEntity entity = moveCadBaseEntity(cadService,modelEntity);
			if(entity!=null) {
				List<XhxkCadPoint> entityPoints = entity.getPoints();
				List<XhxkCadPoint> lastEntityPoints = new LinkedList<XhxkCadPoint>();
				
				if((entityPoints!=null)&&!entityPoints.isEmpty()) {
					for (XhxkCadPoint point : entityPoints) {
						XhxkCadPoint scalePoint = scaleAndRotatePoint(cadService, point);
						lastEntityPoints.add(scalePoint);
					}
				}else {
					System.out.println("points is null");
				}
				entity.setPoints(lastEntityPoints);
			
				scaleEntities.add(entity);
			}
		}
		long end = System.currentTimeMillis();
		//System.out.println("scaleX:"+scaleX+"\tangle:"+angle+"\tsize:"+modelEntities.size()+"\ttime:"+(end-start)+"ms");
		return scaleEntities;
	}
	
	/**
	 * 针对组合体，需要整体移动所有元素
	 * @param baseEntity
	 * @param point
	 * @return
	 */
	private  XhxkCadBaseEntity moveCadBaseEntity(ICadService cadService,XhxkCadBaseEntity baseEntity) {
		String id = UUID.randomUUID().toString(true);
		String type = baseEntity.getClass().getSimpleName();
		if (baseEntity instanceof XhxkCadCircle && baseEntity.getClass().getSimpleName().equals("XhxkCadCircle")) {
			XhxkCadCircle circle = (XhxkCadCircle)baseEntity;
			XhxkCadPoint centerPoint = circle.getCenterPoint();
			centerPoint.setX(centerPoint.getX()+insertionPoint.getX());
			centerPoint.setY(centerPoint.getY()+insertionPoint.getY());
			XhxkCadCircle indoorEntity = new XhxkCadCircle(id, type, this.getBlock(),baseEntity.getLayer(), centerPoint, circle.getRadius()*scaleX);
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			
			List<XhxkCadPoint> basePoints = baseEntity.getPoints();
			List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
			String wkt = "LINESTRING(";
			for (XhxkCadPoint basePoint:basePoints) {
				XhxkCadPoint point = new XhxkCadPoint(basePoint.getX()*scaleX+insertionPoint.getX(),basePoint.getY()*scaleY+insertionPoint.getY());
				wkt += String.format("%f %f,", point.getX(), point.getY());
				points.add(point);
			}
			XhxkCadPoint point = points.get(0);
			points.add(point);
			wkt += String.format("%f %f,", point.getX(), point.getY());
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
			indoorEntity.setValues(centerPoint, wkt, points);
			return indoorEntity;
		} else if (baseEntity instanceof XhxkCadArc && baseEntity.getClass().getSimpleName().equals("XhxkCadArc")) {
			XhxkCadArc arc = (XhxkCadArc) baseEntity;
			XhxkCadPoint centerPoint = arc.getCenterPoint();
			centerPoint.setX(centerPoint.getX()+insertionPoint.getX());
			centerPoint.setY(centerPoint.getY()+insertionPoint.getY());
			XhxkCadArc indoorEntity = new XhxkCadArc(id, type, this.getBlock(),arc.getLayer(), centerPoint, arc.getRadius()*scaleX, arc.getStartAngle(), arc.getEndAngle());
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			
			List<XhxkCadPoint> basePoints = baseEntity.getPoints();
			List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
			String wkt = "LINESTRING(";
			for (XhxkCadPoint basePoint:basePoints) {
				XhxkCadPoint point = new XhxkCadPoint(basePoint.getX()*scaleX+insertionPoint.getX(),basePoint.getY()*scaleY+insertionPoint.getY());
				wkt += String.format("%f %f,", point.getX(), point.getY());
				points.add(point);
			}
			
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
			indoorEntity.setValues(centerPoint, wkt, points);
			return indoorEntity;
		} else if (baseEntity instanceof XhxkCadEllipse && baseEntity.getClass().getSimpleName().equals("XhxkCadEllipse")) {
			XhxkCadEllipse cadEllipse = (XhxkCadEllipse) baseEntity;			
			cadEllipse = initIndoorCadEllipseEntity(cadService, cadEllipse);
			cadEllipse.setBlock(this.getBlock());
			return cadEllipse;
		} else if (baseEntity instanceof XhxkCadLine) {
			XhxkCadLine cadLine = (XhxkCadLine) baseEntity;
			XhxkCadPoint firstPoint = cadLine.getFirstPoint();
			firstPoint.setX(firstPoint.getX()*scaleX+insertionPoint.getX());
			firstPoint.setY(firstPoint.getY()*scaleY+insertionPoint.getY());
			XhxkCadPoint secondPoint = cadLine.getSecondPoint();
			secondPoint.setX(secondPoint.getX()*scaleX+insertionPoint.getX());
			secondPoint.setY(secondPoint.getY()*scaleY+insertionPoint.getY());
			XhxkCadLine indoorEntity = new XhxkCadLine(id, type, this.getBlock(),cadLine.getLayer(), firstPoint, secondPoint);
			indoorEntity.setId(id);
			indoorEntity.setType(type);

			String wkt = "";
			List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
			points.add(firstPoint);
			points.add(secondPoint);
			wkt = String.format("LINESTRING(%f %f,%f %f)", firstPoint.getX(), firstPoint.getY(), secondPoint.getX(), secondPoint.getY());
			indoorEntity.setValues(points.get(0), wkt, points);
			
			
			return indoorEntity;
		} else if (baseEntity instanceof XhxkCadLwPolyline) {
			XhxkCadLwPolyline cadLwPolyline = (XhxkCadLwPolyline) baseEntity;
			List<XhxkCadPoint> resultPoints = new  LinkedList<XhxkCadPoint>();
			List<XhxkCadPoint> coordinates = cadLwPolyline.getCoordinates();
			if(getBlock().equals("mo")) {
				getBlock();
			}
			String wkt = "LINESTRING(";
			for(XhxkCadPoint point:coordinates) {
				point.setX(point.getX()*scaleX+insertionPoint.getX());
				point.setY(point.getY()*scaleY+insertionPoint.getY());
				wkt += String.format("%f %f,", point.getX(), point.getY());	
				resultPoints.add(point);
			}
			XhxkCadPoint point = resultPoints.get(0);
			point.setX(point.getX());
			point.setY(point.getY());
			resultPoints.add(point);
			wkt += String.format("%f %f,", point.getX(), point.getY());
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
			XhxkCadLwPolyline indoorEntity =  new XhxkCadLwPolyline(id, type, this.getBlock(),cadLwPolyline.getLayer(), cadLwPolyline.getFlag(), coordinates);	
			indoorEntity.setId(id);
			indoorEntity.setType(type);	
			
			indoorEntity.setValues(resultPoints.get(0), wkt, resultPoints);
			return indoorEntity;
		} 
		return null;
	}
	
	public  XhxkCadEllipse initIndoorCadEllipseEntity(ICadService cadService,XhxkCadEllipse indoorEntity) {
		int angleStep = indoorEntity.getAngleStep();
		XhxkCadPoint centerPoint = indoorEntity.getCenterPoint();
		centerPoint.setX(centerPoint.getX()*scaleX+insertionPoint.getX());
		centerPoint.setY(centerPoint.getY()*scaleX+insertionPoint.getY());
		
		XhxkCadPoint majorEndPoint = indoorEntity.getMajorEndPoint();
		majorEndPoint.setX(majorEndPoint.getX()*scaleX);
		majorEndPoint.setY(majorEndPoint.getY()*scaleY);

		XhxkCadPoint minorEndPoint =indoorEntity.getMinorEndPoint();
		minorEndPoint.setX(minorEndPoint.getX()*scaleX);
		minorEndPoint.setY(minorEndPoint.getY()*scaleY);

		XhxkCadPoint extrusion = new XhxkCadPoint(indoorEntity.getExtrusion().getX(), indoorEntity.getExtrusion().getY(), indoorEntity.getExtrusion().getZ());
		boolean counterclockwiseFlag = extrusion.getZ() > 0;// true为正序
		double a = Math.sqrt(Math.pow(majorEndPoint.getX(), 2) + Math.pow(majorEndPoint.getY(), 2));
		double b = Math.sqrt(Math.pow(minorEndPoint.getX(), 2) + Math.pow(minorEndPoint.getY(), 2));

		double startAngle = Math.toDegrees(indoorEntity.getStartAngle());
		double endAngle = Math.toDegrees(indoorEntity.getEndAngle());
		double defaultAngle = 90 - Math.toDegrees(Math.asin(majorEndPoint.getX() / (a)));
		double o = Math.toRadians(defaultAngle);
		double k = Math.pow(Math.cos(o), 2) + Math.pow(Math.sin(o), 2);
		double e = Math.pow(b, 2) * Math.pow(Math.cos(o), 2) + Math.pow(a, 2) * Math.pow(Math.sin(o), 2);
		double f = Math.pow(b, 2) * Math.pow(Math.sin(o), 2) + Math.pow(a, 2) * Math.pow(Math.cos(o), 2);
		double g = 2 * Math.sin(o) * Math.cos(o) * (Math.pow(a, 2) - Math.pow(b, 2));
		double h = Math.pow(a, 2) * Math.pow(b, 2) * Math.pow(k, 2);

		int majorQuadrant = cadService.quadrant(majorEndPoint);
		// int minorQuadrant = quadrant(minorEndPoint);
		double majorEX = majorEndPoint.getX();
		double majorEy = majorEndPoint.getY();
		// double minorEX=minorEndPoint.getX();
		// double minorEy=minorEndPoint.getY();
		double lastStartAngle = Math.toDegrees(Math.atan(Math.abs(majorEy) / Math.abs(majorEX)));
		if (majorQuadrant == 2) {// 0度在二象限
			lastStartAngle = 90 + Math.toDegrees(Math.atan(Math.abs(majorEX) / Math.abs(majorEy)));
		} else if (majorQuadrant == 3) {// 0度在三象限
			lastStartAngle = 180 + Math.toDegrees(Math.atan(Math.abs(majorEy) / Math.abs(majorEX)));
		} else if (majorQuadrant == 4) {// 0度在四象限
			lastStartAngle = Math.toDegrees(Math.atan(Math.abs(majorEX) / Math.abs(majorEy))) - 90;
		}

		// 判断正向还是反向
		if (counterclockwiseFlag) {// 正向

		} else {
			double tmp = startAngle;
			startAngle = 360 - endAngle;
			endAngle = 360 - tmp;
		}

		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		int startIndex = (int) (startAngle / angleStep);
		int endIndex = (int) (endAngle / angleStep) + 1;
		String wkt = "LINESTRING(";
		for (int i = startIndex; i <= endIndex; i = i + 1) {
			double angle = (double)i * angleStep;
			if (angle > endAngle) {
				angle = endAngle;
			}
			if (angle < startAngle) {
				angle = startAngle;
			}
			angle = angle + lastStartAngle;
			double x = Math.sqrt(h / (e + f * Math.pow(Math.tan(Math.toRadians(angle)), 2) + g * Math.tan(Math.toRadians(angle))));// Math.sqrt(1/(m+n*Math.pow(Math.tan(Math.toRadians(angle)),2)));
			double y = Math.tan(Math.toRadians(angle)) * x;
			double pi = 90;
			if (angle > pi && angle < 3 * pi) {
				y = 0 - y;
				x = 0 - x;
			}
			x = x + centerPoint.getX() ;
			y = y + centerPoint.getY() ;
			XhxkCadPoint point = new XhxkCadPoint(x, y);
			wkt += String.format("%f %f,", point.getX(), point.getY());
			points.add(point);
		}
		if (wkt.endsWith(",")) {
			wkt = wkt.substring(0, wkt.length() - 1);
		}
		wkt += ")";

		indoorEntity.setValues(centerPoint, wkt, points);
		return indoorEntity;
	}
	
	public XhxkCadPoint scaleAndRotatePoint(ICadService cadService,XhxkCadPoint point) {
		XhxkCadPoint scalePoint = new XhxkCadPoint(point.getX(),point.getY());	//提前移动过了，只用旋转
		//rotationAngle=90;
		if(rotationAngle!=0) {
			double scaleR= Math.sqrt(Math.pow(scalePoint.getX()-insertionPoint.getX(), 2) + Math.pow(scalePoint.getY()-insertionPoint.getY(), 2));
			
			double xDiff=scalePoint.getX()-insertionPoint.getX();
			double yDiff=scalePoint.getY()-insertionPoint.getY();
			double scaleAngle = Math.toDegrees(Math.atan(Math.abs(yDiff)/ Math.abs(xDiff)));
			if(xDiff>=0 && yDiff>=0) {
				//scaleAngle = Math.toDegrees(Math.atan(Math.abs(yDiff)/ Math.abs(xDiff)));
			}else if(xDiff<=0 && yDiff>=0) {
				scaleAngle = 180-scaleAngle;
			}else if(xDiff<=0 && yDiff<=0) {
				scaleAngle = 180+scaleAngle;
			}else if(xDiff>=0 && yDiff<=0) {
				scaleAngle = 360-scaleAngle;
			}
			
			double x = scaleR*Math.cos(Math.toRadians(scaleAngle+rotationAngle));
			double y = scaleR*Math.sin(Math.toRadians(scaleAngle+rotationAngle));
			if(Double.isNaN(scaleAngle)) {
				return point;
			}
			XhxkCadPoint anglePoint = new XhxkCadPoint(x+insertionPoint.getX(),y+insertionPoint.getY());
			scalePoint= anglePoint;
		}
		return scalePoint;
	}
	
	public List<XhxkCadBaseEntity> deepCopy(List<XhxkCadBaseEntity> baeBaseEntities) {
		List<XhxkCadBaseEntity> entity = null;
		try {
			/*
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(baos);
			oos.writeObject(baeBaseEntities);

			ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
			ObjectInputStream ois = new ObjectInputStream(bais);
			entity = (List<XhxkCadBaseEntity>) ois.readObject();*/
			entity = SerializeUtil.clone(baeBaseEntities);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
		return entity;
	}
	
}
