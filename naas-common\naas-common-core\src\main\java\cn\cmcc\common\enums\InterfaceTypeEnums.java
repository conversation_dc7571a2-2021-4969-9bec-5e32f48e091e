/**
 * @Title: InterfaceTypeEnums.java
 * @Package cn.cmcc.common.enums
 * 
 * <AUTHOR>
 * @date 2021年11月4日
 * @version V1.0
 */
package cn.cmcc.common.enums;

public enum InterfaceTypeEnums {

	HTTP_JSON("http/json", "http请求，报文为json"), 
	HTTP_XML("http/xml", "http请求，报文为xml"), 
	WEHSERVICE_JSON("webservice/json", "webservice,报文为json"),
	WEHSERVICE_XML("webservice/xml", "webservice,报文为xml");

    private final String code;
    private final String name;

    InterfaceTypeEnums(String code, String name)
    {
        this.code = code;
        this.name = name;
    }

    public String getCode()
    {
        return code;
    }

    public String getName()
    {
        return name;
    }
}
