/**
 * 附件来源类型
 *
 * @Title: AttachmentSourceType.java
 * <AUTHOR>
 * @date 2022年6月27日
 * @version V1.0
 */
package cn.cmcc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AttachmentSourceTypeEnum implements BaseEnum {
	SYNC("SYNC", "通过同步逻辑"),
	HTTP("HTTP", "HTTP接口"),
	SFTP("SFTP", "SFTP接口"),
	FTP("FTP", "FTP接口");
	private final String value;
	private final String desc;


	/**
	 * @return
	 */
	@Override
	public String getValue() {
		return value;
	}

	/**
	 * @return
	 */
	@Override
	public String getDesc() {
		return desc;
	}
}
