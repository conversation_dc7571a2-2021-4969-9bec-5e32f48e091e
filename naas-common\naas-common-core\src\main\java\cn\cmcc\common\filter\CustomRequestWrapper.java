package cn.cmcc.common.filter;

import javax.servlet.http.HttpServletRequestWrapper;

import cn.cmcc.common.utils.WebUtils;
import cn.cmcc.common.utils.text.CharsetKit;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Enumeration;
import java.util.Map;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

/**  
 * 
 * <AUTHOR>
 * @date 2024-06-21 02:11:28
*/
public class CustomRequestWrapper  extends HttpServletRequestWrapper{

	private final String body;

    public CustomRequestWrapper(HttpServletRequest request, ServletResponse response) throws IOException
    {
        super(request);
        request.setCharacterEncoding(CharsetKit.UTF_8);
        response.setCharacterEncoding(CharsetKit.UTF_8);
        body = WebUtils.getBodyString(request);
    }

    @Override
    public BufferedReader getReader() throws IOException
    {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException
    {
        final ByteArrayInputStream bais = new ByteArrayInputStream(body.getBytes(CharsetKit.UTF_8));
        return new ServletInputStream(){
            @Override
            public int read() throws IOException{
                return bais.read();
            }

            @Override
            public boolean isFinished(){
                return false;
            }

            @Override
            public boolean isReady(){
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener){

            }
        };
    }
    
    public String getBody() {
        return this.body;
    }
    
    @Override
    public String getParameter(String name) {
        return super.getParameter(name);
    }
    
    @Override
    public Map<String, String[]> getParameterMap() {
        return super.getParameterMap();
    }
    
    @Override
    public Enumeration<String> getParameterNames() {
        return super.getParameterNames();
    }
    
    @Override
    public String[] getParameterValues(String name) {
        return super.getParameterValues(name);
    }
}
