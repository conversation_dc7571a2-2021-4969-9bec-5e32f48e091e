package cn.cmcc.common.cache.config;

import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.filter.Filter;

import cn.cmcc.common.utils.text.CharsetKit;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2024-05-16 10:10:10
*/

@Slf4j
public class GenericFastJsonRedisSerializer implements RedisSerializer<Object>{

	private static final Filter autoTypeFilter;
    static {
        autoTypeFilter = JSONReader.autoTypeFilter(
                // 按需加上需要支持自动类型的类名前缀，范围越小越安全
                "com.",
                "cn.",
                "org."
        );
    }

    public GenericFastJsonRedisSerializer() {
    }

    @Override
    public byte[] serialize(Object object) throws SerializationException {
        if (object == null) {
            return new byte[0];
        }
        try {
            return JSON.toJSONString(object, JSONWriter.Feature.WriteClassName).getBytes(CharsetKit.CHARSET_UTF_8);
        } catch (Exception ex) {
            throw new SerializationException("Could not serialize: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Object deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        try {
        	return JSON.parseObject(new String(bytes, CharsetKit.CHARSET_UTF_8), Object.class, autoTypeFilter, JSONReader.Feature.SupportAutoType);
        } catch (Exception ex) {
            throw new SerializationException("Could not deserialize: " + ex.getMessage(), ex);
        }
    }
}
