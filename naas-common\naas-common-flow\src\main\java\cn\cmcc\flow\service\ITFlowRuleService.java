package cn.cmcc.flow.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;

import cn.cmcc.flow.domain.TFlowRule;

/**
 * 规划审核配置Service接口
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
public interface ITFlowRuleService extends IService<TFlowRule>
{

    /**
     * 查询规划审核配置列表
     *
     * @param TFlowRule 规划审核配置
     * @return 规划审核配置集合
     */
    List<TFlowRule> selectTFlowRuleList(TFlowRule TFlowRule);

    /**
     *
     * TODO(描述这个方法的作用)
     *
     * @Title: getById
     * @param id
     * @return TFlowRule
     * @throws
     */
    TFlowRule getById(String id);

    /**
     *
     * TODO(描述这个方法的作用)
     *
     * @Title: save
     * @param TFlowRule
     * @return boolean
     * @throws
     */
    boolean save(TFlowRule TFlowRule);

    /**
     *
     * TODO(描述这个方法的作用)
     *
     * @Title: save
     * @param TFlowRule
     * @return boolean
     * @throws
     */
    boolean update(TFlowRule TFlowRule);

    /**
     * 批量删除规划审核配置
     *
     * @param ids 需要删除的规划审核配置ID
     * @return 结果
     */
    boolean deleteTFlowRuleByIds(String[] ids);

    /**
     * 删除规划审核配置信息
     *
     * @param id 规划审核配置ID
     * @return 结果
     */
    boolean deleteTFlowRuleById(String id);

    /**
     * 查询流转校验列表
     *
     * @return 流转校验集合
     */
    List<TFlowRule> qryFlowRules(String nodeId, String sequenceCode, String triggerType,String ruleType);

    /**
     *
     * 流转校验/审核
     *
     * @Title: flow
     * @param Rules
     * @param param
     * @return boolean
     * @throws
     */
    boolean flowCheck(List<TFlowRule> Rules, Map<String, Object> param);

    /**
     *
     * 流转校验/提示
     *
     * @Title: flow
     * @param Rules
     * @param param
     * @return boolean
     * @throws
     */
    List<Map<String, Object>> flowCheckMsg(List<TFlowRule> Rules, Map<String, Object> param);
}
