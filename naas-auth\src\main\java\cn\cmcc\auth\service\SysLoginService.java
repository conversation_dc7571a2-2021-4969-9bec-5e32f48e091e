package cn.cmcc.auth.service;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.enums.BusinessStatus;
import cn.cmcc.common.enums.EnableStatus;
import cn.cmcc.common.exception.BaseException;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.pojo.LoginBody;
import cn.cmcc.common.pojo.LoginUser;
import cn.cmcc.common.security.enums.LoginSourceEnums;
import cn.cmcc.common.security.service.TokenAuthService;
import cn.cmcc.common.utils.JwtUtils;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.WebUtils;
import cn.cmcc.common.utils.bean.ObjectUtils;
import cn.cmcc.common.utils.encryption.BCryptPasswdUtil;
import cn.cmcc.common.utils.encryption.RSAEncryptUtil;
import cn.cmcc.system.api.pojo.dto.SysLogininfoDto;
import cn.cmcc.system.api.service.RemoteLogService;
import cn.cmcc.system.api.service.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SysLoginService
{
    @DubboReference
    private RemoteLogService remoteLogService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @Autowired
    private TokenAuthService tokenService;

    /**
     * 登录
     */
    public Map<String, Object> login(LoginBody form)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(form.getUsername(), form.getPassword()))
        {
            recordLogininfor(form.getUsername(), BusinessStatus.FAIL.name(), "用户/密码必须填写");
            throw new CustomException("用户/密码必须填写");
        }

        // 查询用户信息
        LoginUser userInfo = remoteUserService.getUserInfo(form.getUsername());
        if (StringUtils.isNull(userInfo))
        {
            recordLogininfor(form.getUsername(), BusinessStatus.FAIL.name(), "登录用户不存在");
            throw new CustomException("用户不存在或密码错误");
        }
        if (EnableStatus.DISABLE.getCode().equals(userInfo.getStatus()))
        {
            recordLogininfor(form.getUsername(), BusinessStatus.FAIL.name(), "用户已停用，请联系管理员");
            throw new CustomException("对不起，您的账号：" + form.getUsername() + " 已停用");
        }
        if (EnableStatus.LOCK.getCode().equals(userInfo.getStatus()))
        {
            recordLogininfor(form.getUsername(), BusinessStatus.FAIL.name(), "用户已锁定，请联系管理员");
            throw new CustomException("对不起，您的账号：" + form.getUsername() + " 已锁定");
        }

        String password = form.getPassword();
        try {
			password = RSAEncryptUtil.decrypt(password,RSAEncryptUtil.genPrivateKeyByStr(Constants.PRIVAGE_KEY));
		} catch (Exception e) {
			log.error("RSAEncryptUtil.decrypt error:",e);
			throw new BaseException("密码解密错误！");
		}

        if (!BCryptPasswdUtil.matchesPassword(password, userInfo.getPassword(),userInfo.getSalt()))
        {
            recordLogininfor(form.getUsername(), BusinessStatus.FAIL.name(), SecurityConstants.AUTHENTICATION_FAILED);
            Integer passwdErrorCount = remoteLogService.getPasswdErrorCount(form.getUsername());
        	if(SecurityConstants.MAX_PASSWD_ERROR_COUNT.equals(passwdErrorCount)) {
        		//锁定账户
        		remoteUserService.changeUserStutus(form.getUsername(), EnableStatus.LOCK.getCode());
        		throw new CustomException("用户不存在/密码错误,账户已锁定,请联系管理员！");
        	}else {
        		throw new CustomException("用户不存在/密码错误,还有" + (SecurityConstants.MAX_PASSWD_ERROR_COUNT - passwdErrorCount) + "次机会！");
        	}
        }
        recordLogininfor(form.getUsername(), BusinessStatus.SUCCESS.name(), "登录成功");

        return tokenService.createToken(userInfo,form.getSource());
    }

    public Map<String, Object> ssoLogin(String account) {
    	// 查询用户信息
        LoginUser userInfo = remoteUserService.getUserInfo(account);
        if (StringUtils.isNull(userInfo))
        {
            recordLogininfor(account, BusinessStatus.FAIL.name(), "登录用户不存在");
            throw new CustomException("登录用户：" + account + " 不存在");
        }
        if (EnableStatus.DISABLE.getCode().equals(userInfo.getStatus()))
        {
            recordLogininfor(account, BusinessStatus.FAIL.name(), "用户已停用，请联系管理员");
            throw new CustomException("对不起，您的账号：" + account + " 已停用");
        }
        if (EnableStatus.LOCK.getCode().equals(userInfo.getStatus()))
        {
            recordLogininfor(account, BusinessStatus.FAIL.name(), "用户已锁定，请联系管理员");
            throw new CustomException("对不起，您的账号：" + account + " 已锁定");
        }
        recordLogininfor(account, BusinessStatus.SUCCESS.name(), "登录成功");

        return tokenService.createToken(userInfo,LoginSourceEnums.WEB.getCode());
    }

    public void logout(String token)
    {
    	String username = JwtUtils.getUserAccount(token);
        if (ObjectUtils.isEmpty(username)) return;
        // 删除用户缓存记录
    	tokenService.delLoginUser(token);
        // 记录用户退出日志
        recordLogininfor(username, BusinessStatus.SUCCESS.name(), "退出成功");
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status 状态
     * @param message 消息内容
     * @return
     */
    public void recordLogininfor(String username, String status, String message)
    {
    	SysLogininfoDto logininfor = new SysLogininfoDto();
        logininfor.setAccount(username);
        logininfor.setIpAddress(WebUtils.getIpAddr());
        logininfor.setMessage(message);
        logininfor.setLogName(message);
        logininfor.setCreateTime(new Date());
        logininfor.setLogName("登录日志");

        // 日志状态
        if (StringUtils.equalsAny(status, BusinessStatus.SUCCESS.name()))
        {
            logininfor.setSucceed("成功");
        }
        else if (BusinessStatus.FAIL.name().equals(status))
        {
            logininfor.setSucceed("失败");
        }
        remoteLogService.saveLogininfor(logininfor);
    }
}
