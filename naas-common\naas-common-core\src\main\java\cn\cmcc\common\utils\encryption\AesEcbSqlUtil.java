package cn.cmcc.common.utils.encryption;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * 用于前端sql语句送到后端加解密*/
@Slf4j
@Component
public class AesEcbSqlUtil {
    private long serialVersionUID = 96548723L;
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";    //"算法/模式/补码方式"
    //由字母或数字组成,此方法使用AES-128-ECB加密模式，key需要为16位加密解密key必须相同，如：abcd1234abcd1234

    /*****************************************************
     * AES加密
     * @param content 加密内容
     * @return 加密密文
     ****************************************************/
    @SneakyThrows(Exception.class)
    public static String encrypt(String content,String key) {
        log.info("AES加密开始...");
        //获得密码的字节数组
        byte[] raw = key.getBytes();
        //根据密码生成AES密钥
        SecretKeySpec skey = new SecretKeySpec(raw, "AES");
        //根据指定算法ALGORITHM自成密码器
        Cipher cipher = Cipher.getInstance(ALGORITHM);// NOSONAR
        //初始化密码器，第一个参数为加密(ENCRYPT_MODE)或者解密(DECRYPT_MODE)操作，第二个参数为生成的AES密钥
        cipher.init(Cipher.ENCRYPT_MODE, skey);
        //获取加密内容的字节数组(设置为utf-8)不然内容中如果有中文和英文混合中文就会解密为乱码
        byte [] byte_content = content.getBytes(StandardCharsets.UTF_8);
        //密码器加密数据
        byte [] encode_content = cipher.doFinal(byte_content);
        //将加密后的数据转换为字符串返回
        return Base64.encodeBase64String(encode_content);
    }
    /*****************************************************
     * AES解密
     * @param content 加密密文
     * @return 解密明文
     ****************************************************/
    @SneakyThrows({Exception.class})
    public static String decrypt(String content,String key) {
        log.info("aes解密开始....");
        //获得密码的字节数组
        byte[] keyBytes = key.getBytes();
        //根据密码生成AES密钥
        SecretKeySpec skey = new SecretKeySpec(keyBytes, "AES");
        //根据指定算法ALGORITHM自成密码器
        Cipher cipher = Cipher.getInstance(ALGORITHM);// NOSONAR
        //初始化密码器，第一个参数为加密(ENCRYPT_MODE)或者解密(DECRYPT_MODE)操作，第二个参数为生成的AES密钥
        cipher.init(Cipher.DECRYPT_MODE, skey);
        //把密文字符串转回密文字节数组
        byte [] encode_content = Base64.decodeBase64(content);
        //密码器解密数据
        byte [] byte_content = cipher.doFinal(encode_content);
        //将解密后的数据转换为字符串返回
        String plaintext = new String(byte_content,"utf-8");
        log.info("AES解密结束.\n");
        return plaintext;
    }
}
