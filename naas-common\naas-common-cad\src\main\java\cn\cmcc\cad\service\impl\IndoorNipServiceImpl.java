package cn.cmcc.cad.service.impl;

import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.cad.service.IndoorNipService;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.utils.file.FileUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName IndoorNipServiceImpl
 * @Description 室分九天接口
 * <AUTHOR>
 * @Date 2022/10/13 16:41
 * @Version 1.0
 **/
@Service
@Slf4j
public class IndoorNipServiceImpl implements IndoorNipService {

    /**
     * 获取请求token
     *
     * @param tokenUrl 获取token的地址
     * @return
     */
    public static String getAccessToken(String tokenUrl) {
        HttpUtil httpUtil = new HttpUtil();
        String accessToken = null;
        try {
            log.info("尝试获取token");
            String responseContent = (String) httpUtil.sendHttpRequest(tokenUrl, "GET", null);

            //转化成json对象然后返回accessToken属性的值
            JSONObject demoJson = JSON.parseObject(responseContent);
            log.info("token response is:" + demoJson);
            accessToken = demoJson.getJSONObject("body").getString("token");
        } catch (Exception e) {
            log.error("调用九天token请求异常！" + e.getMessage());
        }
        log.info("getAccessToken accessToken:" + accessToken);
        return accessToken;
    }

    @Override
    public String sendReqByFile(String interfaceUrl, String tokenUrl, String fileParamCode, String filePath, Map<String, Object> otherParams) {
        log.info("接口地址:{}", interfaceUrl);
        log.info("token地址:{}", tokenUrl);
        log.info("文件路径:{}", filePath);
        log.info("其他参数:{}", otherParams);
        // 获取访问token
        String accessToken = getAccessToken(tokenUrl);
        // 拼接最终的调用地址
        String finalUrl = interfaceUrl + accessToken;
        HttpUtil httpUtil = new HttpUtil();
        Map<String, String> fileParamMap = new HashMap<>();
        fileParamMap.put(fileParamCode, filePath);
        return httpUtil.uploadFile(finalUrl, fileParamMap, otherParams);
    }

    @Override
    public Object sendReqByJson(String interfaceUrl, String tokenUrl, String paramJson) {
        // 获取访问token
        String accessToken = getAccessToken(tokenUrl);
        // 拼接最终的调用地址
        String finalUrl = interfaceUrl + accessToken;
        HttpUtil httpUtil = new HttpUtil();
        return httpUtil.sendHttpRequest(finalUrl, "POST", paramJson);
    }

    public static class HttpUtil {
        public String uploadFile(String url, Map<String, String> filePathMap, Map<String, Object> otherParams) {
            log.info("能力调用：url:{}", url);
            if (filePathMap == null || filePathMap.isEmpty()) {
                log.info("param is empty!");
                return null;
            }
            // 创建httpPost
            HttpPost httpPost = new HttpPost(url);
            try {
                // 设置参数
                MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
                for (Map.Entry<String, String> entry : filePathMap.entrySet()) {
                    File file = FileUtils.getFile(entry.getValue());
                    multipartEntityBuilder.addBinaryBody(entry.getKey(), file);
                }
                // 添加其他参数
                if (otherParams != null && !otherParams.isEmpty()) {
                    for (Map.Entry<String, Object> entry : otherParams.entrySet()) {
                        multipartEntityBuilder.addTextBody(entry.getKey(), String.valueOf(entry.getValue()));
                    }
                }
                HttpEntity reqEntity = multipartEntityBuilder.build();
                httpPost.setEntity(reqEntity);
            } catch (Exception e) {
                log.error("构造http请求异常！" + e.getMessage());
            }
            CloseableHttpClient httpClient = null;
            CloseableHttpResponse response = null;
            HttpEntity entity;
            String responseContent = null;
            try {
                // 创建默认的httpClient实例
                httpClient = HttpClients.createDefault();
                // 执行请求
                log.info("开始执行请求！");
                response = httpClient.execute(httpPost);
                log.info("请求结束！");
                String contentType = response.getFirstHeader("Content-Type")
                                             .getValue();
                if (contentType.startsWith("application/octet-stream") || contentType.startsWith(
                        "multipart/form-data")) {
                    // 处理文件流
                    log.info("响应类型为:" + contentType);
                    //response.getEntity().getContent(); 写入到文件
                    entity = response.getEntity();
//                    log.info("保存路径为:" + returnFileSavePath);
//                    // 关闭响应实体的输入流
//                    // 保存文件 到returnFileSavePath
//                    try (InputStream inputStream = entity.getContent();
//                         OutputStream outputStream = new FileOutputStream(returnFileSavePath)) {
//                        byte[] buffer = new byte[1024];
//                        int bytesRead;
//                        while ((bytesRead = inputStream.read(buffer)) != -1) {
//                            outputStream.write(buffer, 0, bytesRead);
//                        }
//                    }
//                    log.info("文件保存成功！");
//                    responseContent = returnFileSavePath;
                    responseContent = "";
                } else if (contentType.startsWith("text/plain") || contentType.startsWith(
                        "application/json")) {
                    // 处理文本流
                    log.info("响应类型为:" + contentType);
                    responseContent = EntityUtils.toString(response.getEntity(), "UTF-8");
                } else if (contentType.startsWith("text/html")) {
                    // 处理html流
                    log.info("响应类型为:" + contentType);
                    log.info("响应内容为:" + EntityUtils.toString(response.getEntity(), "UTF-8"));
                    responseContent = EntityUtils.toString(response.getEntity(), "UTF-8");
                    // 处理其他流
                } else {
                    log.error("未知的响应类型！" + contentType);
                    throw new CustomException("未知的响应类型！");
                }
                return responseContent;
            } catch (Exception e) {
                log.error("调用http请求上传文件异常！" + e.getMessage());
                throw new RuntimeException(e);
            } finally {
                // 放入集合中
                releaseResource(httpClient);
                releaseResource(response);
            }
        }

        public String sendHttpRequest(String url, String methodType, String content) {
            log.info("请求地址:{},类型:{},参数:{}", url, methodType, content);
            CloseableHttpClient client = HttpClientBuilder.create().build();
            HttpRequestBase method = null;
            CloseableHttpResponse httpResponse = null;
            try {
                int timeout = 6000;
                RequestConfig requestConfig = RequestConfig.custom()
                                                           .setSocketTimeout(timeout)
                                                           .setConnectTimeout(timeout)
                                                           .setConnectionRequestTimeout(timeout)
                                                           .build();
                if ("POST".equalsIgnoreCase(methodType)) {
                    HttpPost httpPost = new HttpPost(url);
                    httpPost.setConfig(requestConfig);
                    httpPost.setEntity(new StringEntity(content, ContentType.APPLICATION_JSON));
                    method = httpPost;
                } else if ("PUT".equalsIgnoreCase(methodType)) {
                    HttpPut httpPut = new HttpPut(url);
                    httpPut.setConfig(requestConfig);
                    httpPut.setEntity(new StringEntity(content, ContentType.APPLICATION_JSON));
                    method = httpPut;
                } else if ("GET".equalsIgnoreCase(methodType)) {
                    HttpGet httpGet = new HttpGet(new URIBuilder(url).build().toString());
                    httpGet.setConfig(requestConfig);
                    method = httpGet;
                } else if ("DELETE".equalsIgnoreCase(methodType)) {
                    HttpDelete httpDelete = new HttpDelete(url);
                    httpDelete.setConfig(requestConfig);
                    method = httpDelete;
                }
                httpResponse = client.execute(method);
                return EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            } catch (Exception e) {
                throw new RuntimeException("调用http接口异常！" +e.getMessage());
            } finally {
                releaseResource(client);
                releaseResource(httpResponse);
            }
        }

        private void releaseResource(Closeable resource) {
            if (resource != null) {
                try {
                    resource.close();
                } catch (IOException e) {
                    log.error("资源释放异常！" + e.getMessage());
                }
            }
        }

    }

}
