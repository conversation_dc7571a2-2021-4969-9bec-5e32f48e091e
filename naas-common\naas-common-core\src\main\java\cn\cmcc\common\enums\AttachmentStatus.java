/**
 * 附件状态
 *
 * @Title: AttachmentStatus.java
 * <AUTHOR>
 * @date 2022年6月27日
 * @version V1.0
 */
package cn.cmcc.common.enums;

public enum AttachmentStatus {
	
	NORMAL("S", "有效的"), 
	DEL("D", "已删除"), 
	PENDING("P", "待下载"), 
	FAIL("F", "下载失败");
	
	private final String code;
    private final String info;

    AttachmentStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
