package cn.cmcc.common.datasource.config;

import java.net.InetAddress;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Sequence;

import cn.cmcc.common.cache.context.SysConfigContext;
import cn.cmcc.common.utils.uuid.IdUtils;

/**  
 * 
 * <AUTHOR>
 * @date 2024-03-06 04:28:30
*/
public class CustomIdGenerator implements IdentifierGenerator {

	private final Sequence sequence;

    public CustomIdGenerator() {
        this.sequence = new Sequence(null);
    }

    public CustomIdGenerator(InetAddress inetAddress) {
        this.sequence = new Sequence(inetAddress);
    }

    public CustomIdGenerator(long workerId, long dataCenterId) {
        this.sequence = new Sequence(workerId, dataCenterId);
    }

    public CustomIdGenerator(Sequence sequence) {
        this.sequence = sequence;
    }
    
	@Override
	public Number nextId(Object entity) {
		return null;
	}
	
	@Override
	public String nextUUID(Object entity) {
		return SysConfigContext.getCurrentProvinceCode().toLowerCase() + IdUtils.fastSimpleUUID();
	}

}
