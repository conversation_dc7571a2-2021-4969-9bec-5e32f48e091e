package cn.cmcc.common.utils;

import cn.cmcc.common.constant.HttpStatus;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.utils.text.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Map;

/**
 * Jwt工具类
 *
 * <AUTHOR>
 */

@Slf4j
public class JwtUtils
{

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    public static String createToken(Map<String, Object> claims,Long SecretExpireSec)
    {
    	DateTime expirationDate = DateUtil.offsetSecond(new Date(),Convert.toInt(SecretExpireSec));
        String token = Jwts.builder()
        		.setClaims(claims)
        		.setIssuedAt(new Date())
        		.setExpiration(expirationDate)
        		.signWith(SignatureAlgorithm.HS512, SecurityConstants.SKEY).compact();

        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    public static Claims parseToken(String token)
    {
        return Jwts.parser().setSigningKey(SecurityConstants.SKEY).parseClaimsJws(token).getBody();
    }

    /**
     * 根据令牌获取用户标识
     *
     * @param token 令牌
     * @return 用户ID
     */
    public static String getTokenUuid(String token)
    {
    	try {
    		Claims claims = parseToken(token);
            return getValue(claims, SecurityConstants.TOKEN_UUID);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
            throw new CustomException(HttpStatus.UNAUTHORIZED, "无效的token", null);
		}
    }

    /**
     * 根据令牌获取用户标识
     *
     * @param token 令牌
     * @return 用户account
     */
    public static String getUserAccount(String token) {
        String returnVal = null;
        try {
            Claims claims = parseToken(token);
            returnVal = getValue(claims, SecurityConstants.ACCOUNT);
        } catch (ExpiredJwtException e) {
            // 如果token已过期，则记录日志，说明无需从缓存中删除
            log.info("Token已过期，无需删除缓存。 token: {}", token);// NOSONAR
            // 可选：可以在这里执行其他操作，例如：
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return returnVal;
    }

    /**
     * 根据令牌获取用户标识
     *
     * @param claims 身份信息
     * @return 用户ID
     */
    public static String getTokenUuid(Claims claims)
    {
        return getValue(claims, SecurityConstants.TOKEN_UUID);
    }


    /**
     * 根据身份信息获取用户ID
     *
     * @param claims 身份信息
     * @return 用户ID
     */
    public static String getUserId(Claims claims)
    {
        return getValue(claims, SecurityConstants.USER_ID);
    }

    /**
     * 根据身份信息获取用户ID
     *
     * @param claims 身份信息
     * @return 用户account
     */
    public static String getUserAccount(Claims claims)
    {
        return getValue(claims, SecurityConstants.ACCOUNT);
    }


    /**
     * 根据身份信息获取键值
     *
     * @param claims 身份信息
     * @param key 键
     * @return 值
     */
    public static String getValue(Claims claims, String key)
    {
        return Convert.toStr(claims.get(key), "");
    }
}
