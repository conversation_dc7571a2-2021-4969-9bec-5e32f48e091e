package cn.cmcc.flow.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import cn.cmcc.flow.domain.FlowLog;

/**
 * <p>
 * 定时流转 Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-06
 */
@Mapper
public interface TimedFlowMapper extends BaseMapper<FlowLog> {

    /**
     * 查询任务列表
     *
     * @param flowKey  流程键
     * @param stage    阶段
     * @param node     节点
     * @param date     日期
     * @param business 业务
     * @param positive 正
     * @return 任务列表
     */
    List<Map<String, Object>> selectTaskList(String flowKey, String stage, String node, Date date, boolean business, boolean positive);

    /**
     * 插入流转日志列表
     *
     * @param list 流转日志列表
     * @return 插入行数
     */
    int insertFlowLogList(List<FlowLog> list);

    /**
     * 查询节点
     *
     * @param taskId 任务ID
     * @return 节点
     */
    String selectNode(String taskId);

}
