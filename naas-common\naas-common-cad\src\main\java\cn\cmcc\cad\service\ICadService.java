package cn.cmcc.cad.service;

import java.util.List;
import java.util.Map;

import com.aspose.cad.fileformats.cad.CadBlockDictionary;
import com.aspose.cad.fileformats.cad.CadImage;
import com.aspose.cad.fileformats.cad.cadobjects.Cad2DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.Cad3DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.CadBaseEntity;
import com.aspose.cad.fileformats.cad.cadobjects.CadLine;
import com.aspose.cad.fileformats.cad.cadobjects.CadLwPolyline;

import cn.cmcc.cad.entity.XhxkCadArc;
import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadBlockEntity;
import cn.cmcc.cad.entity.XhxkCadCircle;
import cn.cmcc.cad.entity.XhxkCadEllipse;
import cn.cmcc.cad.entity.XhxkCadImage;
import cn.cmcc.cad.entity.XhxkCadInsertObject;
import cn.cmcc.cad.entity.XhxkCadLine;
import cn.cmcc.cad.entity.XhxkCadLwPolyline;
import cn.cmcc.cad.entity.XhxkCadPoint;
import cn.cmcc.cad.entity.XhxkCadText;

public interface ICadService {
	
	/**
	 * 读取Cad图纸，支持.dwg图形文件和.xhxk数据文件
	 * @param file - 图纸文件路径
	 * @return
	 * @throws Exception
	 */
	public  XhxkCadImage loadCadImage(String file) throws Exception ;
	

	/**
	 * 
	 * @param image
	 * @return
	 */
	public  XhxkCadImage transformCadImage(CadImage image);

	
	/**
	 * 读取Cad图纸，支持.dwg图形文件和.xhxk数据文件
	 * @param file - 图纸文件路径
	 * @param unitType - 图纸单位，默认值为mm
	 * @param scale - 缩放比例，默认为1
	 * @return
	 * @throws Exception
	 */
	public  XhxkCadImage loadCadImage(String file, String unitType, int scale) throws Exception ;

	/**
	 * 将CAD图纸对象保存成.xhxk文件
	 * 
	 * @param cadImage -  XhxkCadImage对象
	 * @param xhxkFile - .xhxk数据文件路径
	 * @return
	 */
	public  boolean saveCadImageToXhxk(XhxkCadImage cadImage, String xhxkFile) ;

	/**
	 * 读取.xhxk文件并转换成CadImage对象
	 * 
	 * @param xhxkFile
	 * @return
	 */
	public  XhxkCadImage loadXhxkToCadImage(String xhxkFile) ;

	/**
	 * 复制Cad图纸对象
	 * 
	 * @param cadImage
	 * @return
	 */
	public XhxkCadImage deepCopy(XhxkCadImage cadImage);

	/**
	 * 复制对象，和deepCopy一样的功能， deepCopy效率远低于saveCadImageToXhxk和readXhxkToCadImage组合，
	 * 所以使用后两个组合来复制对象，效率提升50%以上，用于解决频繁load效率过慢问题
	 * 
	 * @param cadImage
	 * @return
	 */
	public XhxkCadImage copy(XhxkCadImage cadImage) ;
	

	/**
	 * 图纸对象转化aspose --> xhxk
	 * 
	 * @param entities      - aspose图纸对象
	 * @param blockEntities - aspose图纸块对象
	 * @return
	 */
	public List<XhxkCadBaseEntity> transformCadBaseEntity(CadImage image, List<XhxkCadBlockEntity> blockEntities);

	/**
	 * CadBlockDictionary 转化成 XhxkCadBlockEntity
	 * 
	 * @param block - CadBlockDictionary块对象
	 * @return
	 */
	public List<XhxkCadBlockEntity> transformCadBlockEntity(CadImage image);

	/**
	 * 
	 * @param baseEntity    - aspose Entity to XhxkCadBaseEntity
	 * @param blockEntities - aspose blockEntity , 当baseEntity为
	 *                      CadInsertObject时，读取CadInsertObject组合体对象
	 * @return
	 */
	public XhxkCadBaseEntity transformCadBaseEntity(CadBaseEntity baseEntity, List<XhxkCadBlockEntity> blockEntities,Cad3DPoint minPoint,Cad3DPoint maxPoint);

	/**
	 * CadLwPolyline to XhxkCadLwPolyline
	 * 
	 * @param polyline - CadLwPolyline：多线段对象
	 * @return
	 */
	public XhxkCadLwPolyline transformLwPolyline(CadLwPolyline polyline);

	/**
	 * CadLine to XhxkCadLine
	 * 
	 * @param line - CadLine:直线对象
	 * @return
	 */
	public XhxkCadLine transformLine(CadLine line);

	/**
	 * 将aspose坐标转换为IndoorCadPoint
	 * 
	 * @param point - aspose Cad3DPoint
	 * @return
	 */
	public XhxkCadPoint transform3dPoint(Cad3DPoint point);

	/**
	 * 将aspose坐标转换为IndoorCadPoint
	 * 
	 * @param point - aspose Cad2DPoint
	 * @return
	 */
	public XhxkCadPoint transformPoint(Cad2DPoint point);

	/**
	 * 将aspose坐标转换为IndoorCadPoint
	 * 
	 * @param points - aspose Cad3DPoint
	 * @return
	 */
	public List<XhxkCadPoint> transform3dPoint(List<Cad3DPoint> points);

	/**
	 * 将aspose坐标转换为IndoorCadPoint
	 * 
	 * @param points - aspose Cad2DPoint
	 * @return
	 */
	public List<XhxkCadPoint> transformPoint(List<Cad2DPoint> points);

	/**
	 * CadBlockDictionary to XhxkCadBaseEntity ,
	 * 
	 * @param block - 组合体对象
	 * @return
	 */
	public Map<String, List<XhxkCadBaseEntity>> getBlockModel(CadBlockDictionary block);

	/**
	 * CadArc to XhxkCadArc ：主要处理坐标对象
	 * 
	 * @param entity       - CadArc对象
	 * @param indoorEntity - XhxkCadArc对象
	 * @return
	 */
	public XhxkCadArc initIndoorCadArcEntity(CadBaseEntity entity, XhxkCadArc indoorEntity);

	/**
	 * CadCircle to XhxkCadCircle ：主要处理坐标对象
	 * 
	 * @param entity       - CadCircle对象
	 * @param indoorEntity - XhxkCadCircle对象
	 * @return
	 */
	public XhxkCadCircle initIndoorCadCircleEntity(CadBaseEntity entity, XhxkCadCircle indoorEntity);

	/**
	 * CadEllipse to XhxkCadEllipse ：主要处理坐标对象
	 * 
	 * @param entity       - CadEllipse对象
	 * @param indoorEntity - XhxkCadEllipse对象
	 * @return
	 */
	public XhxkCadEllipse initIndoorCadEllipseEntity(CadBaseEntity entity, XhxkCadEllipse indoorEntity);

	/**
	 * CadLine to XhxkCadLine ：主要处理坐标对象
	 * 
	 * @param entity       - CadLinec对象
	 * @param indoorEntity - XhxkCadLine对象
	 * @return
	 */
	public XhxkCadLine initIndoorCadLineEntity(CadBaseEntity entity, XhxkCadLine indoorEntity);

	/**
	 * CadLwPolyline to XhxkCadLwPolyline ：主要处理坐标对象
	 * 
	 * @param entity       - CadLwPolyline 对象
	 * @param indoorEntity - XhxkCadLwPolyline对象
	 * @return
	 */
	public XhxkCadLwPolyline initIndoorCadLwPolylineEntity(CadBaseEntity entity, XhxkCadLwPolyline indoorEntity);

	/**
	 * CadAttrib/CadAttDef/CadMText/CadText to XhxkCadText ：主要处理坐标对象和文字内容
	 * 
	 * @param entity
	 * @param indoorEntity
	 * @return
	 */
	public XhxkCadText initIndoorCadTextEntity(CadBaseEntity entity, XhxkCadText indoorEntity);

	/**
	 * CadInsertObject to XhxkCadInsertObject : 主要处理坐标对象和文字内容
	 * 
	 * @param cadBaseEntity - CadInsertObject对象
	 * @param indoorEntity  - XhxkCadInsertObject对象
	 * @param blockEntities - 组合体模型对象
	 * @return
	 */
	public XhxkCadInsertObject initIndoorCadInsertObjectEntity(CadBaseEntity cadBaseEntity, XhxkCadInsertObject indoorEntity, List<XhxkCadBlockEntity> blockEntities);

	/**
	 * 坐标偏移
	 * @param point - XhxkCadPoint
	 * @param x - x方向偏移量
	 * @param y - y方向偏移量
	 * @return
	 */
	public XhxkCadPoint deviationPoint(XhxkCadPoint point, double x, double y);

	/**
	 * 坐标放大
	 * @param point - XhxkCadPoint
	 * @param scale - 放大量
	 * @return
	 */
	public  XhxkCadPoint pointScale(XhxkCadPoint point, double scale);

	/**
	 * 判断坐标相对于图纸原点或参考点所在位置，一般用于组合体计算
	 * @param point - XhxkCadPoint
	 * @return
	 */
	public int quadrant(XhxkCadPoint point) ;

	/**
	 * 坐标放大后偏移，先放大坐标，再偏移，偏移参数值需要是放大后的值
	 * @param point - XhxkCadPoint
	 * @param scale - 放大量
	 * @param minPoint - 偏移量 : 偏移参数值需要是放大后的值
	 * @return
	 */
	public  XhxkCadPoint transfer(XhxkCadPoint point, Double scale, XhxkCadPoint minPoint) ;

	/**
	 * 坐标放大后偏移，先放大坐标，再偏移，偏移参数值需要是放大后的值
	 * @param points - XhxkCadPoint
	 * @param scale - 放大量
	 * @param minPoint - 偏移量 : 偏移参数值需要是放大后的值
	 * @return
	 */
	public  List<XhxkCadPoint> transfer(List<XhxkCadPoint> points, Double scale, XhxkCadPoint minPoint) ;

	public void cadLicense();
}
