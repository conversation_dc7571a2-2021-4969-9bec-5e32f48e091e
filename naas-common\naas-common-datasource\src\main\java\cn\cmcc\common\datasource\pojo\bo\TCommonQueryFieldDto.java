package cn.cmcc.common.datasource.pojo.bo;

import cn.cmcc.common.enums.JavaTypeEnum;
import lombok.Data;
import java.io.Serializable;

/**
 * 通用查询字段配置对象 t_common_query_field
 *
 * <AUTHOR>
 * @date 2021-03-25
 */
@Data
public class TCommonQueryFieldDto implements Serializable
{
	private static final long serialVersionUID = -8648863592108170467L;
    private String id;
    /** 英文字段名 */
    private String name;
    /** 中文字段名 */
    private String title;
    /** 展示类型 */
    private String dataType;
    /** 数据类型 */
    private String javaType;
    /** 是否显示 */
    private boolean show;
    /** 是否搜索 */
    private boolean search;
    /** 排序 */
    private Integer sort;
    /** 数据接口地址 */
    private String href;
    /** 通用查询Code */
    private String commonQueryCode;
    /** 宽度 */
    private Integer width;
    /** 是否导出 */
    private boolean export;
    /**
     * 级联父级字段
     * */
    private String cascadeCode;
    /**
     * 默认值
     */
    private String defaultValue;
    /**
     * 是否可排序
     */
    private boolean sortable;
    /**
     * 更多搜索
     */
    private boolean moreSearch;

    /**
     * execl导出下拉值来源类型,数据类型：通用查询、数据字段
     */
    private String dropDownSourceType;
    /**
     * execl导出下拉，数据类型的值
     */
    private String dropDownSourceCode;
    /**
     * 是否必填，用于查询导出之后，将必填项单元格标红
     */
    private boolean required = false;

    /**
     * 隐藏列
     */
    private boolean exportHide = false;

    /**
     * 导出只读
     */
    private boolean exportReadOnly = false;

    /**
     * 是否主键
     */
    private boolean primaryKey = true;

    /**
     * 是否唯一列
     */
    private boolean uniqueKey = true;

    /**
     * 是否联合唯一列
     */
    private boolean unionUnique = true;

    /**
     * 报表属性
     */
    private String chartProperties;

    /**
     * 数据来源类型
     */
    private String dataSourceType;

    /**
     * 提示信息
     */
    private String tipInfo;

    /**
     * 表单显示
     */
    private boolean formShow;

    /**
     * 表单编辑
     */
    private boolean formEdit;

    /**
     * 预览详情标题
     */
    private boolean tagsViewTitle;

    /**
     * 值测量
     */
    private String valueStrategy;

    /**
     * 保护列，不可编辑
     */
    private boolean protectColumn = true;

    /**
     * 映射别名
     */
    private String mappingAlias;

    /**
     * 映射字段类型1
     */
    private String mappingType1;

    /**
     * 映射字段名称1
     */
    private String mappingColumn1;

    /**
     * 映射字段类型2
     */
    private String mappingType2;

    /**
     * 映射字段名称2
     */
    private String mappingColumn2;

    /**
     * 映射字段类型3
     */
    private String mappingType3;

    /**
     * 映射字段名称3
     */
    private String mappingColumn3;

    /**
     * 映射字段类型4
     */
    private String mappingType4;

    /**
     * 映射字段名称4
     */
    private String mappingColumn4;


    /**
     * 映射字段类型5
     */
    private String mappingType5;

    /**
     * 映射字段名称5
     */
    private String mappingColumn5;

    /**
     * 表单回填
     */
    private boolean formBackfill;

    /**
     * 搜索框回填
     */
    private boolean searchBackfill;
    //是否导入
    private boolean hasImport;

    public TCommonQueryFieldDto() {
		this.dataType = "text";
		this.javaType = JavaTypeEnum.STRING.getValue();
    	this.show = true;
    	this.primaryKey = false;
    	this.formShow = true;
    	this.search = false;
		this.uniqueKey = false;
		this.unionUnique = false;
		this.export = true;
		this.exportHide = false;
        this.hasImport = false;
    }
}
