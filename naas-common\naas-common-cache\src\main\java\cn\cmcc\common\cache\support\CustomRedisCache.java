/**
 * @Title: CustomRedisCache.java
 * @Package cn.cmcc.common.core.cache.support
 * 
 * <AUTHOR>
 * @date 2021年9月9日
 * @version V1.0
 */
package cn.cmcc.common.cache.support;

import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;

public class CustomRedisCache extends RedisCache{

	/**
	 * @param name
	 * @param cacheWriter
	 * @param cacheConfig
	 */
	public CustomRedisCache(String name, RedisCacheWriter cacheWriter, RedisCacheConfiguration cacheConfig) {
		super(name, cacheWriter, cacheConfig);
	}

}
