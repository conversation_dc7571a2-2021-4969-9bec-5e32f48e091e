<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.cmcc.flow.mapper.TFlowDefInfoMapper">
  <resultMap id="BaseResultMap" type="cn.cmcc.flow.domain.TFlowDefInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="proc_def_id" jdbcType="VARCHAR" property="procDefId" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="node_type" jdbcType="VARCHAR" property="nodeType" />
    <result column="options" jdbcType="BIGINT" property="options" />
    <result column="proc_def_key" jdbcType="VARCHAR" property="procDefKey" />
    <result column="expire_expression" jdbcType="VARCHAR" property="expireExpression" />
    <result column="skip_expression" jdbcType="VARCHAR" property="skipExpression" />
    <result column="multi_collection_expression" jdbcType="VARCHAR" property="multiCollectionExpression" />
    <result column="multi_collection_assignees" jdbcType="VARCHAR" property="multiCollectionAssignees" />
    <result column="transparent_transmission" jdbcType="BIT" property="transparentTransmission" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="suspend_expression" jdbcType="VARCHAR" property="suspendExpression" />
    <result column="suspend_option" jdbcType="INTEGER" property="suspendOption" />
    <result column="srv_task_type" jdbcType="VARCHAR" property="srvTaskType" />
    <result column="multi_instance" jdbcType="VARCHAR" property="multiInstance" />
    <result column="node_user_id" jdbcType="VARCHAR" property="nodeUserId" />
    <result column="node_user_groupid" jdbcType="VARCHAR" property="nodeUserGroupid" />
    <result column="node_temp_data" jdbcType="OTHER" property="nodeTempData" />
    <result column="data_interface_status" jdbcType="VARCHAR" property="dataInterfaceStatus" />
    <result column="data_interface_complete_notify" jdbcType="VARCHAR" property="dataInterfaceCompleteNotify" />
    <result column="data_interface_start_notify" jdbcType="VARCHAR" property="dataInterfaceStartNotify" />
    <result column="fixed_identifiers" jdbcType="VARCHAR" property="fixedIdentifiers" />
    <result column="fixed_express" jdbcType="VARCHAR" property="fixedExpress" />
    <result column="complex_express" jdbcType="VARCHAR" property="complexExpress" />
    <result column="fixed_options" jdbcType="VARCHAR" property="fixedOptions" />
    <result column="complex_identifiers" jdbcType="VARCHAR" property="complexIdentifiers" />
    <result column="complete_express" jdbcType="VARCHAR" property="completeExpress" />
    <result column="prev_node_id" jdbcType="VARCHAR" property="prevNodeId" />
    <result column="post_node_id" jdbcType="VARCHAR" property="postNodeId" />
    <result column="dynamic_candidate" jdbcType="BIT" property="dynamicCandidate" />
    <result column="js_config" jdbcType="VARCHAR" property="jsConfig" />
    <result column="node_stage" jdbcType="VARCHAR" property="nodeStage" />
    <result column="css_config" jdbcType="VARCHAR" property="cssConfig" />
    <result column="proc_version" jdbcType="INTEGER" property="procVersion" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="node_code" jdbcType="INTEGER" property="nodeCode" />
    <result column="dynamic_candidate_condition" jdbcType="VARCHAR" property="dynamicCandidateCondition"/>
    <result column="parallel_fork" jdbcType="BIT" property="parallelFork"/>
    <result column="jump_node" jdbcType="VARCHAR" property="jumpNode"/>
    <result column="seq_show_type" jdbcType="VARCHAR" property="seqShowType"/>
  </resultMap>

  <select id="getFlowDefSequenceIdByNodeAndCode" parameterType="String" resultType="String">
  	select  a.seq_id from (
	select trim(split_part(fixed_express,'==',2)) seq_code,proc_version,node_id as seq_id from t_flow_def_info
	where prev_node_id= #{nodeId}  and node_type='sequenceFlow') a
	where a.seq_code= #{code} order  BY a.proc_version desc limit 1
  </select>

  <select id="selectStartNodeOptionByKey" parameterType="String" resultType="map">
  	select node_id,options,proc_def_id  from t_flow_def_info
  		where node_type='startEvent'
	 and proc_def_id = (select id_ from  act_re_procdef where key_= #{procDefKey} order by version_ desc limit 1 )
  </select>

   <select id="getNodeTaskSeqBtns" parameterType="String" resultType="cn.cmcc.flow.pojo.bo.TaskNodeSeqBtns">
		with proc_key as (
			SELECT t.proc_def_key from t_flow_def_info t join act_ru_task a
				on t.proc_def_id = a.proc_def_id_  and a.id_ =  #{taskId} limit 1),
		tab as ( SELECT distinct t.options,t.seq_show_type,t.proc_def_key from t_flow_def_info t,proc_key
			where t.proc_version = ( SELECT max(proc_version) from t_flow_def_info,proc_key
			where  node_type = 'sequenceFlow' and prev_node_id = #{nodeId}
			and proc_key.proc_def_key = t_flow_def_info.proc_def_key )
			and prev_node_id = #{nodeId} and t.proc_def_key = proc_key.proc_def_key)
	    select d.code,d.name,tab.seq_show_type as "seqShowTypeStr",proc_def_key as "procDefKey",
	           (select  count(*) as "permsCount" from sys_menu where sys_menu.perms LIKE '%${nodeId}:apply:flow:%'),
			   case when d.code = '1' then
						(select  count(sys_user_role.*) from sys_user_role
																 left join sys_role_menu on sys_user_role.role_id = sys_role_menu.role_id
																 left join sys_menu on sys_role_menu.menu_id = sys_menu.menu_id
						 where sys_menu.perms LIKE '%${nodeId}:plan:flow:approve%'  and sys_user_role.user_id::text = #{userId}) else
						(select  count(sys_user_role.*) from sys_user_role
																 left join sys_role_menu on sys_user_role.role_id = sys_role_menu.role_id
																 left join sys_menu on sys_role_menu.menu_id = sys_menu.menu_id
						 where sys_menu.perms LIKE '%${nodeId}:plan:flow:reject%'  and sys_user_role.user_id::text = #{userId})
				   end as "roleCounts"
	    from tab left join sys_dict_data d on tab.options::text = d.code
	    and d.dict_type_code = 'FLOW_SEQUENCE_TYPE'
		order by d.sort
	</select>

	<select id="getFlowDefInfoByNodeId" parameterType="String" resultMap="BaseResultMap">
		select *
		 from t_flow_def_info
		 where node_type = 'userTask' and node_id  = #{nodeId}
		 <if test="procDefKey != null and procDefKey!=''">
		 	and proc_def_key = #{procDefKey}
		 </if>
		 order by proc_version desc limit 1
	</select>

	<insert id="insertBatch">
  		insert into t_flow_def_info(
			 id, proc_def_id, node_id, node_type, options, proc_def_key,
			 transparent_transmission, expire_expression, skip_expression,
			 multi_collection_expression, multi_collection_assignees,node_name,
			 suspend_expression,suspend_option,srv_task_type,node_user_id,
			 node_user_groupid,multi_instance,node_temp_data,data_interface_status,
			 data_interface_start_notify,data_interface_complete_notify,
			 fixed_identifiers, fixed_express, complex_express, fixed_options,
			 complex_identifiers, complete_express, prev_node_id, post_node_id,
			 dynamic_candidate,js_config,node_stage,css_config,
			 proc_version,sort,node_code,dynamic_candidate_condition,parallel_fork,
			 automatic_flow,jump_node,seq_show_type)
			 values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.id, jdbcType=VARCHAR}, #{item.procDefId, jdbcType=VARCHAR}, #{item.nodeId, jdbcType=VARCHAR},
			#{item.nodeType, jdbcType=VARCHAR}, #{item.options, jdbcType=BIGINT}, #{item.procDefKey, jdbcType=VARCHAR},
			#{item.transparentTransmission, jdbcType=BOOLEAN}, #{item.expireExpression, jdbcType=VARCHAR},
			#{item.skipExpression, jdbcType=VARCHAR},#{item.multiCollectionExpression, jdbcType=VARCHAR},
			#{item.multiCollectionAssignees, jdbcType=VARCHAR}, #{item.nodeName, jdbcType=VARCHAR},
			#{item.suspendExpression, jdbcType=VARCHAR}, #{item.suspendOption, jdbcType=INTEGER},
			#{item.srvTaskType, jdbcType=VARCHAR},#{item.nodeUserId, jdbcType=VARCHAR},
			#{item.nodeUserGroupid, jdbcType=VARCHAR},
			#{item.multiInstance, jdbcType=VARCHAR},#{item.nodeTempData,javaType=Object, jdbcType=OTHER,typeHandler=cn.cmcc.common.datasource.handler.JsonTypeHandler},
			#{item.dataInterfaceStatus, jdbcType=VARCHAR}, #{item.dataInterfaceStartNotify, jdbcType=VARCHAR},
			#{item.dataInterfaceCompleteNotify, jdbcType=VARCHAR},
			#{item.fixedIdentifiers, jdbcType=VARCHAR}, #{item.fixedExpress, jdbcType=VARCHAR}, #{item.complexExpress, jdbcType=VARCHAR},
			#{item.fixedOptions, jdbcType=VARCHAR}, #{item.complexIdentifiers, jdbcType=VARCHAR}, #{item.completeExpress, jdbcType=VARCHAR},
			#{item.prevNodeId, jdbcType=VARCHAR}, #{item.postNodeId, jdbcType=VARCHAR}, #{item.dynamicCandidate, jdbcType=BOOLEAN},
			#{item.jsConfig, jdbcType=VARCHAR},
			#{item.nodeStage, jdbcType=VARCHAR}, #{item.cssConfig, jdbcType=VARCHAR}, #{item.procVersion, jdbcType=INTEGER},
			#{item.sort, jdbcType=INTEGER},#{item.nodeCode, jdbcType=VARCHAR},#{item.dynamicCandidateCondition, jdbcType=VARCHAR},
			#{item.parallelFork, jdbcType=BOOLEAN}, #{item.automaticFlow, jdbcType=VARCHAR},
			#{item.jumpNode, jdbcType=VARCHAR},#{item.seqShowType, jdbcType=VARCHAR})
		</foreach>
  </insert>

  <select id="getNodeStageByProcKey" parameterType="String" resultType="cn.cmcc.flow.pojo.bo.NodeStageBO">
  	select a.node_id,a.node_name,a.node_stage as stage,b.name as stage_name,a.sort,a.node_code,a.fixed_options,
	  a.multi_instance, a.multi_collection_assignees, a.node_user_groupid
	  from t_flow_def_info  a
		 left join sys_dict_data  b on a.node_stage = b.code and b.dict_type_code = 'STAGE_CODE'
		 where a.node_type = 'userTask'
		 <choose>
		 	<when test="taskId != null and taskId != ''">
		 		and a.proc_def_id = ( SELECT proc_def_id_ from  act_ru_task where id_ = #{taskId} )
		 	</when>
		 	<otherwise>
		 		and a.proc_def_id = (select id_ from  act_re_procdef where key_= #{procDefKey} order by version_ desc limit 1 )
		 	</otherwise>
		 </choose>
		 order by a.sort asc
  </select>

  <select id="getStageByProcInstId" parameterType="String" resultType="String">
  	select DISTINCT a.node_stage
		 from t_flow_def_info  a
		 join act_ru_task art on a.proc_def_id = art.proc_def_id_ and a.node_id = art.task_def_key_
		 and a.node_type = 'userTask' and art.proc_inst_id_ = #{procInstId} limit 1
  </select>

  <update id="upgradeOrderToNewerFlow" parameterType="String" >
  	<choose>
            <when test="type == 'ru_task'">
            	with tab  as (
				  select proc_def_id,proc_def_key from t_flow_def_info where proc_def_key= #{procDefKey} order by proc_version desc limit 1
				)
				update act_ru_task set proc_def_id_=t.proc_def_id
				from tab t where t.proc_def_key=  trim(split_part(proc_def_id_,':',1))
            </when>
            <when test="type == 'ru_execution'">
            	with tab  as (
				  select proc_def_id,proc_def_key from t_flow_def_info where proc_def_key= #{procDefKey} order by proc_version desc limit 1
				)
				update act_ru_execution set proc_def_id_=t.proc_def_id
				from tab t where t.proc_def_key=  trim(split_part(proc_def_id_,':',1))

            </when>
            <when test="type == 'hi_taskinst'">
            	with tab  as (
				  select proc_def_id,proc_def_key from t_flow_def_info where proc_def_key= #{procDefKey} order by proc_version desc limit 1
				)
				update act_hi_taskinst set proc_def_id_=t.proc_def_id
				from tab t where t.proc_def_key=  trim(split_part(proc_def_id_,':',1))
            </when>
    </choose>
  </update>

	<select id="selectNodeList" resultMap="BaseResultMap">
		SELECT a.*
		FROM t_flow_def_info a
		WHERE a.proc_def_id = (SELECT id_ FROM act_re_procdef p
		INNER JOIN t_flow_model m ON p.key_ = m."key" AND m.id = #{modelId}
		ORDER BY p.version_ DESC LIMIT 1)
		ORDER BY a.node_type DESC, a.sort
	</select>

	<select id="getProcDefIdKey" resultType="map">
		SELECT proc_def_id,proc_def_key from t_flow_def_info where
		proc_def_key in (SELECT key from t_flow_model where id = #{modelId} ) order by proc_version desc limit 1
	</select>

	<select id="qryAllFlowWholeNotify" resultType="FlowWholeNotifyBO">
  		SELECT key,whole_task_start_notify,whole_task_completed_notify
  		from t_flow_model
  		where (whole_task_start_notify is not null and whole_task_start_notify != '')
  		or (whole_task_completed_notify is not null and whole_task_completed_notify != '' )
  </select>

</mapper>
