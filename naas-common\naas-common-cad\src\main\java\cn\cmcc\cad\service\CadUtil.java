package cn.cmcc.cad.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import cn.cmcc.cad.entity.XhxkCadLine;
import cn.cmcc.cad.entity.XhxkCadLwPolyline;
import cn.cmcc.cad.entity.XhxkCadPoint;
import cn.hutool.core.lang.UUID;

public class CadUtil {
	
	public static double distince(XhxkCadPoint point1, XhxkCadPoint point2) {
		return Math.sqrt(Math.pow(point1.getX() - point2.getX(), 2) + Math.pow(point1.getY() - point2.getY(), 2));
	}
	
	public static List<XhxkCadLine> getCadLines(XhxkCadLwPolyline polyline){
		List<XhxkCadPoint> polylinePoints = polyline.getPoints();
		List<XhxkCadLine> lines = new ArrayList<XhxkCadLine>();
		for(int i=1;i<polylinePoints.size();i++) {
			String id = UUID.randomUUID().toString(true);
			XhxkCadPoint firstPoint = polylinePoints.get(i-1);
			XhxkCadPoint secondPoint = polylinePoints.get(i);
			XhxkCadLine indoorEntity = new XhxkCadLine(id, polyline.getClass().getSimpleName(), null,polyline.getLayer(), firstPoint, secondPoint);
			
			String wkt = "";
			List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
			points.add(firstPoint);
			points.add(secondPoint);
			wkt = String.format("LINESTRING(%f %f,%f %f)", firstPoint.getX(), firstPoint.getY(), secondPoint.getX(), secondPoint.getY());
			indoorEntity.setValues(points.get(0), wkt, points);
			lines.add(indoorEntity);
		}
		return lines;
	}

	public static String doubleFormate(double d) {
		BigDecimal bd = BigDecimal.valueOf(d);
		bd = bd.setScale(4, RoundingMode.HALF_UP);
		return bd.toString();
	}

	public static String doubleFormate(double d, int float_) {
		BigDecimal bd = BigDecimal.valueOf(d);
		bd = bd.setScale(float_, RoundingMode.HALF_UP);
		return bd.toString();

	}
}
