/**
 * @Title: TableColumn.java
 * @Package cn.cmcc.common.pojo
 * 
 * <AUTHOR>
 * @date 2022年5月31日
 * @version V1.0
 */
package cn.cmcc.common.datasource.pojo.bo;

import lombok.Data;

@Data
public class TableColumn {

	 /** 列名称 */
    private String columnName;
    
    /** 列类型 */
    private String columnType;
    
    /** 字段长度 */
    private String length;

    /** 列描述 */
    private String columnComment;

    /** 是否自增 */
    private String identity;
    
    /** 默认值 */
    private String columnDefault;
    
    /** 是否必填 */
    private boolean required;
    
    /** 是否主键 */
    private boolean pk;
}
