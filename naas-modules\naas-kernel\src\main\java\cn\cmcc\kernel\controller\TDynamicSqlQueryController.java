package cn.cmcc.kernel.controller;

import cn.cmcc.common.annotation.CheckPerms;
import cn.cmcc.kernel.annotation.CheckQueryCodePerms;
import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.enums.AuthorizeType;
import cn.cmcc.common.interactive.apiencrypt.annotation.ApiEncrypt;
import cn.cmcc.common.interactive.utils.ThirdInterTokenUtils;
import cn.cmcc.common.log.annotation.Log;
import cn.cmcc.common.log.enums.SysOperType;
import cn.cmcc.common.web.controller.BaseController;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.cmcc.kernel.domain.TCommonQuery;
import cn.cmcc.kernel.domain.TCommonQueryField;
import cn.cmcc.kernel.service.ITCommonQueryFieldService;
import cn.cmcc.kernel.service.ITCommonQueryService;
import cn.cmcc.kernel.service.ITDynamicSqlQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通用查询-业务控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/kernel/dynamic")
public class TDynamicSqlQueryController extends BaseController{

	@Autowired
    private ITCommonQueryService tCommonQueryService;
	@Autowired
    private ITCommonQueryFieldService tCommonQueryFieldService;
	@Autowired
	private ITDynamicSqlQueryService itDynamicSqlQueryService;


	/**
	 * 通用编码查询数据
	 * 该方法用于根据提供的编码和参数映射执行动态SQL查询
	 * 主要用于实现灵活的、基于编码的查询操作，可以适应不同的查询需求
	 *
	 * @param code     编码，用于标识特定的查询类型或策略
	 * @param paramMap 参数映射，包含了查询所需的参数，可能为null表示没有参数
	 * @return 返回查询结果，具体类型取决于查询内容
	 */
	@ApiEncrypt
	@GetMapping("/query/{code}")
	@CheckPerms(value = "BASE_PRI_ROLE",authorizeType = AuthorizeType.ROLE)
    @CheckQueryCodePerms(message = "无权限访问该查询编码")
	@Log(title = "调用通用查询", sysOperType = SysOperType.COMMON_QUERY)
	public Object dynamicSqlQuery(@PathVariable String code, @RequestParam(required = false) Map<String, Object> paramMap) {
		return itDynamicSqlQueryService.dynamicSqlQuery(code, paramMap);
	}

	/**
	 * 使用POST方法根据代码动态查询SQL结果
	 * 该方法使用了自定义注解@ApiEncrypt和@CheckPerms来分别处理加密和权限检查
	 *
	 * @param code URI中的路径变量，代表查询代码
	 * @param paramMap 请求体中的参数映射，包含查询所需的条件
	 * @return 返回动态查询的结果对象
	 */
	@ApiEncrypt
	@PostMapping("/query/byPost/{code}")
	@CheckPerms(value = "BASE_PRI_ROLE",authorizeType = AuthorizeType.ROLE)
    @CheckQueryCodePerms(message = "无权限访问该查询编码")
	public Object dynamicSqlQueryByPost(@PathVariable String code, @RequestBody Map<String, Object> paramMap) {
		return dynamicSqlQuery(code, paramMap);
	}

	/**
	 * 调用通用查询统计数据量
	 * @param code 通用查询编码
	 * @param paramMap 参数集合
	 * @return  AjaxResult
     */
	@GetMapping("/query/count/{code}")
	@CheckPerms(value = "BASE_PRI_ROLE",authorizeType = AuthorizeType.ROLE)
    @CheckQueryCodePerms(message = "无权限访问该查询编码统计")
    public AjaxResult dynamicSqlQueryCount(@PathVariable String code,@RequestParam(required = false) Map<String, Object> paramMap) {
    	String mastpCountId = Constants.COMMON_QUERY_COUNT_PRX + code;
    	TCommonQuery tCommonQuery = tCommonQueryService.selectTCommonQueryByCode(code);

        List<Map<String, Object>> countList = tCommonQueryService.queryByDynamicDbSql(tCommonQuery.getSystem(),mastpCountId, paramMap,tCommonQuery,tCommonQuery.isCacheData());
        return AjaxResult.success(Long.valueOf(countList.get(0).get("count").toString()));
	}

	/**
	 * 根据编码获取通用查询的字段信息。
	 * 该方法通过提供的编码查询对应的字段配置信息，并返回查询结果。
	 *
	 * @param code 编码，用于标识具体的查询字段配置
	 * @return 如果编码存在，返回字段配置信息的查询结果；如果编码不存在，返回错误提示信息
	 */
    @GetMapping(value = "/field/{code}")
    @CheckPerms(value = "BASE_PRI_ROLE",authorizeType = AuthorizeType.ROLE)
    @CheckQueryCodePerms(message = "无权限访问该查询编码字段配置")
    public AjaxResult getField(@PathVariable("code") String code)
    {
    	TCommonQuery tCommonQuery = tCommonQueryService.selectTCommonQueryByCode(code);
    	if (tCommonQuery == null) {
			return AjaxResult.error("无编码【" + code + "】对应配置！");
		}
    	return AjaxResult.success(tCommonQueryFieldService.selectTCommonQueryFieldList(code));
	}

	/**
	 * 根据提供的编码获取所有相关字段配置信息。
	 * 该方法会查询指定编码对应的配置信息，并获取其完整的字段配置集合。
	 *
	 * @param code 编码，用于标识需要查询的字段配置信息
	 * @return 如果编码存在，返回包含字段配置信息的查询结果；如果编码不存在，返回错误提示信息
	 */
	@ApiEncrypt
    @GetMapping(value = "/field/all/{code}")
    @CheckPerms(value = "BASE_PRI_ROLE",authorizeType = AuthorizeType.ROLE)
    @CheckQueryCodePerms(message = "无权限访问该查询编码完整配置")
    public AjaxResult getFieldAll(@PathVariable("code") String code)
    {
    	TCommonQuery tCommonQuery = tCommonQueryService.selectTCommonQueryByCode(code);
    	if (tCommonQuery == null) {
			return AjaxResult.error("无编码【" + code + "】对应配置！");
		}
    	TCommonQuery tCommonQueryBak = tCommonQuery.clone();
    	tCommonQueryBak.setFields(tCommonQueryFieldService.selectAllFieldList(code));
    	return AjaxResult.success(tCommonQueryBak);
	}

	/**
	 * 根据指定的编码获取字段配置及对应的数据。
	 *
	 * @param code 编码，用于识别特定的配置。
	 * @param paramMap 参数映射，用于动态查询条件，可选。
	 * @param resConfig 是否返回配置信息，若为 true 则返回配置信息，可选。
	 * @return 返回包含字段配置和数据的 AjaxResult 对象。如果编码无对应配置，则返回错误信息。
	 */
	@ApiEncrypt
    @GetMapping(value = "/field/data/{code}")
    @CheckPerms(value = "BASE_PRI_ROLE",authorizeType = AuthorizeType.ROLE)
    @CheckQueryCodePerms(message = "无权限访问该查询编码数据")
    public AjaxResult getFieldAndData(@PathVariable("code") String code,
    		@RequestParam(required = false) Map<String, Object> paramMap,
    		@RequestParam(required = false) boolean resConfig)
    {
    	TCommonQuery tCommonQuery = tCommonQueryService.selectTCommonQueryByCode(code);
    	if (tCommonQuery == null) {
			return AjaxResult.error("无编码【" + code + "】对应配置！");
		}

    	List<TCommonQueryField> fields = tCommonQueryFieldService.selectTCommonQueryFieldList(code);
    	String mastpId = Constants.COMMON_QUERY_PRX + code;

		Map<String, Object> resMap = new HashedMap<String, Object>();
        resMap.put("title", fields);

    	if(resConfig) {
            resMap.put("config", tCommonQuery.clone());
    	}

		//不分页，全部返回
         List<Map<String, Object>> list = tCommonQueryService.queryByDynamicDbSql(tCommonQuery.getSystem(), mastpId, paramMap,tCommonQuery,tCommonQuery.isCacheData());
         resMap.put("dataList", list);

         return AjaxResult.success(resMap);
	}

	/**
	 * 获取第三方接口的TOKEN。
	 * 通过指定的配置编码，调用工具类方法获取对应的TOKEN值。
	 *
	 * @param configCode 配置编码，用于指定获取TOKEN的配置信息
	 * @return 返回包含TOKEN的AjaxResult对象，成功时包含TOKEN数据，失败时返回错误信息
	 */
	@GetMapping("/third/inter/token/{configCode}")
	@Log(title = "调用通用查询-获取三方TOKEN")
    public AjaxResult thirdInterToken(@PathVariable String configCode) {
        return AjaxResult.success("请求成功", ThirdInterTokenUtils.getToken(configCode));
	}
}
