/**
 * @Title: DynamicSqlService.java
 * @Package cn.cmcc.common.datasource.service
 * 
 * <AUTHOR>
 * @date 2022年5月18日
 * @version V1.0
 */
package cn.cmcc.common.datasource.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.cursor.Cursor;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.xmltags.XMLLanguageDriver;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransaction;
import org.mybatis.spring.SqlSessionUtils;
import org.postgresql.copy.CopyManager;
import org.postgresql.copy.CopyOut;
import org.postgresql.jdbc.PgConnection;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.opencsv.CSVParser;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.datasource.annotation.DS;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.function.StreamResult;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class DynamicSqlService {

	/**
	 * 导出csv分隔符
	 */
	private final static char EXPORT_COPY_SEPARATOR = '^';
	
	@Resource(name="dynamicSqlSessionFactory")
    private SqlSessionFactory sqlSessionFactory;

    private XMLLanguageDriver xmlLanguageDriver = new XMLLanguageDriver();
    
    /**
     * load
     * */
    public void loadMappedStatement(String mastpId,String sql) {
    	MybatisConfiguration configuration=(MybatisConfiguration)sqlSessionFactory.getConfiguration();
    	loadMappedStatement(configuration,mastpId,sql);
    }
    
    
    /**
     * load
     * */
    public void loadMappedStatement(MybatisConfiguration configuration,String mastpId,String sql) {
    	if (configuration == null || StringUtils.isEmpty(mastpId) || StringUtils.isEmpty(sql)) {
			return;
		}

    	if(configuration.hasStatement(mastpId)) {
        	this.removeMappedStatement(mastpId);
    	}

		String sqlScript = "<script>"+ sql +"</script>";
		SqlSource sqlSource= xmlLanguageDriver.createSqlSource(configuration, sqlScript.toString().trim(), Map.class);

		List<ResultMap> resultMapList = new ArrayList<ResultMap>();
		resultMapList.add(new ResultMap.Builder(configuration, "defaultResultMap", LinkedHashMap.class, new ArrayList<>(0))
                .build());
		MappedStatement ms = new MappedStatement.Builder(configuration, mastpId, sqlSource, SqlCommandType.SELECT)
                 .resultMaps(resultMapList).build();
        // 缓存
        configuration.addMappedStatement(ms);

    }
    

    /**
     * 删除MappedStatement
     * @throws SecurityException
     * @throws NoSuchFieldException
     * */
    public void removeMappedStatement(String mastpId) {
    	MybatisConfiguration configuration=(MybatisConfiguration)sqlSessionFactory.getConfiguration();

    	Field field;
		try {
			field = configuration.getClass().getDeclaredField("mappedStatements");
			field.setAccessible(true);
	        @SuppressWarnings("unchecked")
			Map<String, MappedStatement> mappedStatements = (Map<String, MappedStatement>) field.get(configuration);
	        mappedStatements.remove(mastpId);
		} catch (Exception e) {
			log.error("removeMappedStatement error:",e);
		}
    }
    
    /**
     * 
     * @Title: getBoundSql   
     * @Description: 获取可执行sql
     * @param: @param mastpId
     * @param: @param param
     * @param: @return      
     * @return: String      
     * @throws
     */
	public String getBoundSql(String mastpId, Map<String, Object> param) {
    	if(StringUtils.isEmpty(mastpId)){
    		throw new NullPointerException("参数mastpId不能为空！");
		}
    	/**
    	 * 1、getBoundSql对动态标签的解析，获取完全可执行的sql。对#{}字符解析，将其替换成?
    	 * 2、我们需要的是在数据库工具里面完全可执行的sql，所以需要将?也替换成参数
    	 * 3、对于${}的动态参数，将完全替换，所以在SQL配置时，可用'${}'的写法
    	 */
//    	BoundSql boundSql = sqlSessionFactory.getConfiguration().getMappedStatement(mastpId).getBoundSql(param);
		org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
		org.apache.ibatis.mapping.MappedStatement mappedStatement = configuration.getMappedStatement(mastpId);
		org.apache.ibatis.mapping.BoundSql boundSql = mappedStatement.getBoundSql(param);
    	List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
    	String sql = boundSql.getSql();
    	
    	if(CollectionUtils.isNotEmpty(parameterMappings) ) {
    		for(int index = 0; index < parameterMappings.size() ; ++ index) {
    			ParameterMapping parameterMapping = parameterMappings.get(index);
    			
    			/**
    			 * 通用查询传入的是Map对象，#{}写法统一按字符串处理
    			 */
    			String value = "'" + param.getOrDefault(parameterMapping.getProperty(),"").toString() + "'";
    			sql = sql.replaceFirst("\\?", value);

    		}
    	}
    	return sql;
	}
	
	/**
	 * 
	 * @Title: query   
	 * @Description: 查询
	 * @param: @param mastpId
	 * @param: @param parameterMap
	 * @param: @return      
	 * @return: List<Map<String,Object>>      
	 * @throws
	 */
	public List<Map<String, Object>> query(String mastpId,Map<String, Object> parameterMap){
    	SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
    	try {
        	return sqlSession.selectList(mastpId,parameterMap);
		}catch (Exception e) {
			log.error("run common query:【" + mastpId + "】error:",e);
			throw e;
		}finally {
			SqlSessionUtils.closeSqlSession(sqlSession, sqlSessionFactory);
		}
    }
	
	/**
     * 查询列表
     *
     * @param sql        SQL
     * @param params     参数
     * @return 列表
     */
    public List<Map<String, Object>> selectList( String sql, Map<String, Object> params)
            throws SQLException {
    	MybatisConfiguration configuration=(MybatisConfiguration)sqlSessionFactory.getConfiguration();
    	
        String mastpId = Constants.COMMON_QUERY_PRX + IdUtils.fastSimpleUUID();
        XMLLanguageDriver driver = new XMLLanguageDriver();
        SqlSource sqlSource = driver.createSqlSource(configuration, String.format("<script>%s</script>", StringUtils.trim(sql)), Map.class);
        MappedStatement.Builder builder = new MappedStatement.Builder(configuration, mastpId, sqlSource, SqlCommandType.SELECT).
                resultMaps(Collections.singletonList(new ResultMap.Builder(configuration, "defaultResultMap", LinkedHashMap.class, new ArrayList<>()).build()));
        MappedStatement statement = builder.build();
        SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
        
        try {
        	Executor executor = configuration.newExecutor(new JdbcTransaction(sqlSession.getConnection()));
            return executor.query(statement, params, new RowBounds(), null);
		} finally {
			SqlSessionUtils.closeSqlSession(sqlSession, sqlSessionFactory);
		}
        
    }
    
	public void queryStream(String sql, Map<String, Object> param, StreamResult result) {
		MybatisConfiguration configuration=(MybatisConfiguration)sqlSessionFactory.getConfiguration();
    	
        String mastpId = Constants.COMMON_QUERY_PRX + IdUtils.fastSimpleUUID();
        XMLLanguageDriver driver = new XMLLanguageDriver();
        SqlSource sqlSource = driver.createSqlSource(configuration, String.format("<script>%s</script>", StringUtils.trim(sql)), Map.class);
        MappedStatement.Builder builder = new MappedStatement.Builder(configuration, mastpId, sqlSource, SqlCommandType.SELECT).
                resultMaps(Collections.singletonList(new ResultMap.Builder(configuration, "defaultResultMap", LinkedHashMap.class, new ArrayList<>()).build()));
        MappedStatement statement = builder.build();
        SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
        
        Cursor<Map<String,Object>> cursor = null;
		try {
			int rowIndex = 1;
			Executor executor = configuration.newExecutor(new JdbcTransaction(sqlSession.getConnection()));
		    cursor = executor.queryCursor(statement, param, RowBounds.DEFAULT);
			Iterator<Map<String,Object>> iterator = cursor.iterator();
		    while (iterator.hasNext()) {
		    	result.processResult(iterator.next(),rowIndex ++ );
		    }
		}catch (Exception e) {
		      throw new CustomException("Error querying database.  Cause: " + e, e);
	    } finally {
			if(cursor != null) {
				try {
					cursor.close();
				} catch (IOException e) {
					log.error("cursor.close error:",e);
				}
			}	
			SqlSessionUtils.closeSqlSession(sqlSession, sqlSessionFactory);
		}
	}
	
	/**
     * @Transactional注解来维持数据库连接，
     * 否则当查询结束后数据库连接就会断开，Cursor就取不到数据了
     */
	@DS(useParamDs=true)
	public void queryStream(String dataSource,String mastpId, Map<String, Object> param, StreamResult result) {
		SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
		Cursor<Map<String,Object>> cursor = null;
		try {
			int rowIndex = 1;
			cursor= sqlSession.selectCursor(mastpId,param);
			Iterator<Map<String,Object>> iterator = cursor.iterator();
		    while (iterator.hasNext()) {
		    	result.processResult(iterator.next(),rowIndex ++ );
		    }
		} finally {
			if(cursor != null) {
				try {
					cursor.close();
				} catch (IOException e) {
					log.error("cursor.close error:",e);
				}
			}	
			SqlSessionUtils.closeSqlSession(sqlSession, sqlSessionFactory);
		}
	}

	@DS(useParamDs=true)
	public void copyStream(String dataSource,String mastpId, Map<String, Object> param, StreamResult result) throws Exception{
    	SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
    	CSVParser csvParser = new CSVParserBuilder().withSeparator(EXPORT_COPY_SEPARATOR).build();
        
		CopyOut copyOut=null;
		try {
			int rowIndex = 0;
			String[] rowTitle=null;
			CopyManager copyManager=sqlSession.getConnection().unwrap(PgConnection.class).getCopyAPI();
			String sql = this.getBoundSql(mastpId,param);
			log.info("导出查询SQL【{}】",sql);
			copyOut=copyManager.copyOut(String.format("COPY (%s) TO STDOUT DELIMITER '%s' quote '\"' CSV HEADER",sql,EXPORT_COPY_SEPARATOR));
			while (true){
				byte[] buff=copyOut.readFromCopy(true);
				if(buff==null){
					break;
				}
				CSVReader reader = new CSVReaderBuilder(new InputStreamReader(new ByteArrayInputStream(buff),Charset.forName("UTF-8"))).withCSVParser(csvParser).build();
				List<String[]> csvList = reader.readAll();
				
				if(rowIndex==0){
					rowTitle = csvList.get(0);
					rowIndex++;
				}else {
					Map<String,Object> resMap = new HashMap<>();
					String[] rowData = csvList.get(0);
					for(int i=0;i<rowData.length;i++){
						resMap.put(rowTitle[i],rowData[i]);
					}
					result.processResult(resMap,rowIndex);
					rowIndex++;
				}
				reader.close();
			}
		}finally {
			SqlSessionUtils.closeSqlSession(sqlSession, sqlSessionFactory);
		}
	}
}
