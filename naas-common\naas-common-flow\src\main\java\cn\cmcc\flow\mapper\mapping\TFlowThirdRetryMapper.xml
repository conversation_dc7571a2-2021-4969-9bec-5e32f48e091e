<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.cmcc.flow.mapper.FlowThirdRetryMapper">
  <resultMap id="BaseResultMap" type="cn.cmcc.flow.domain.TFlowThirdRetry">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="exchange" jdbcType="VARCHAR" property="exchange" />
    <result column="queue" jdbcType="VARCHAR" property="queue" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="retry_cnt" jdbcType="INTEGER" property="retryCnt" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="response" jdbcType="VARCHAR" property="response" />
    <result column="param" jdbcType="VARCHAR" property="param" />
    <result column="sub_type" jdbcType="VARCHAR" property="subType" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="server_node" jdbcType="VARCHAR" property="serverNode" />
  </resultMap>
  
</mapper>