package cn.cmcc.common.exception;

import cn.cmcc.common.web.domain.AjaxResult;

public class BusinessException extends RuntimeException {

    public BusinessException() {
    }

    public BusinessException(AjaxResult errorCode) {
        super(errorCode.getMsg());
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public BusinessException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
