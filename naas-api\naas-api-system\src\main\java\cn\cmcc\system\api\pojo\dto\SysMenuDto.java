/**
 *
 *
 * @Title: SysMenuDto.java
 * <AUTHOR>
 * @date 2022年9月19日
 * @version V1.0
 */
package cn.cmcc.system.api.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class SysMenuDto implements Serializable{

    /**
     * @Fields serialVersionUID :
     */
    private static final long serialVersionUID = 1L;

    private Long menuId;

    /**
     * 菜单父编号
     */
    private Long parentId;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * url地址
     */
    private String url;

    /**
     * 菜单排序号
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 菜单状态(字典)
     */
    private String status;

    /**
     * 打开方式（0 ：新窗口， 1：tab页签）
     */
    private String openType;

    /** 类型（M目录 C菜单 F按钮） */
    private String menuType;

    private String statisticUrl;

    private String belongSystem;

    /** 组件路径 */
    private String component;

    /** 权限字符串 */
    private String perms;

    /** 是否缓存*/
    private String cache;
    /**
     * 菜单参数
     */
    private Object menuParams;

    /** 子菜单 */
    private List<SysMenuDto> children = new ArrayList<>();
}
