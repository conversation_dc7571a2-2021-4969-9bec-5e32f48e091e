package cn.cmcc.auth.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import cn.cmcc.auth.config.CasConfig;

/**
 * cas 退出拦截
 *
 * <AUTHOR>
 */
@Component
public class CasLogoutInterceptor implements AsyncHandlerInterceptor
{
	@Autowired
    private CasConfig casConfig;
	
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        if(request.getMethod().equals(HttpMethod.POST.name())) {
            casConfig.logout(request);
            return false;  
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
    }
}
