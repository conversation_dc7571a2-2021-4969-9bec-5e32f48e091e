/**
 * @Title: FileTypeEnums.java
 * @Package cn.cmcc.base.enums
 * 
 * <AUTHOR>
 * @date 2022年5月19日
 * @version V1.0
 */
package cn.cmcc.common.enums;

public enum FileTypeEnums {

	EXCEL("excel","EXCEL",".xlsx"),
    COMPLEX_EXCEL("complex_excel","COMPLEX_EXCEL",".xlsx"),
	CSV("csv","CSV",".csv"),
	SHAPE("shape","shape图层",".zip"),
	MIF("mif","mif图层",".zip");

	private final String code;
    private final String name;
    private final String suffix;

    FileTypeEnums(String code, String name, String suffix)
    {
        this.code = code;
        this.name = name;
        this.suffix = suffix;
    }

    public static FileTypeEnums getByValue(String value) {
        for (FileTypeEnums e : values()) {
            if (e.getCode().equals(value)) {
                return e;
            }
        }
        return null;
    }

    public String getCode()
    {
        return code;
    }

    public String getName()
    {
        return name;
    }

	public String getSuffix() {
		return suffix;
	}

}
