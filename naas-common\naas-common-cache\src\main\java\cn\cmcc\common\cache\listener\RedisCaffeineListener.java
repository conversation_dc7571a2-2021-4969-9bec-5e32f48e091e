/**
 * @Title: RedisCaffeineListener.java
 * @Package cn.cmcc.common.core.cache.listener
 * <AUTHOR>
 * @date 2021年9月2日
 * @version V1.0
 */
package cn.cmcc.common.cache.listener;

import cn.cmcc.common.cache.enums.ChannelTopicEnum;
import cn.cmcc.common.cache.support.RedisCaffeineCache;
import cn.cmcc.common.config.NaasConfig;
import cn.cmcc.common.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@ConditionalOnBean({RedisMessageListenerContainer.class})
@RequiredArgsConstructor
public class RedisCaffeineListener implements MessageListener {


    @Autowired
    private CacheManager cacheManager;

    private final RedisMessageListenerContainer redisMessageListenerContainer;

    @EventListener
    public void init(ApplicationReadyEvent event) {
        redisMessageListenerContainer.addMessageListener(this, ChannelTopicEnum.REDIS_CAFFEINE_DEL_TOPIC.getChannelTopic());
        redisMessageListenerContainer.addMessageListener(this, ChannelTopicEnum.REDIS_CAFFEINE_CLEAR_TOPIC.getChannelTopic());
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        ChannelTopicEnum channelTopic = ChannelTopicEnum.getChannelTopicEnum(new String(message.getChannel()));
        log.info("redis消息订阅者接收到频道【{}】发布的消息【{}】。", channelTopic.getCode(), maskSensitiveInfo(new String(message.getBody())));

        try {
            String body = new String(message.getBody());
            RedisPublishMessage publishMessage = JsonUtils.parseObject(body, RedisPublishMessage.class);
            if (publishMessage == null) {
                log.warn("接收到的消息体为空，无法解析");
                return;
            }

            if (NaasConfig.SERVER_ID.equals(publishMessage.getPublishServer()) || !publishMessage.getSoftVersion().equals(NaasConfig.getVersion())) {
                log.info("当前运用和非对应版本不处理消息");
                return;
            }

            RedisCaffeineMessage cacheMessage = JsonUtils.parseObject(publishMessage.getData(), RedisCaffeineMessage.class);
            Cache cache = cacheManager.getCache(cacheMessage.getName());

            if (cache instanceof RedisCaffeineCache) {
                RedisCaffeineCache redisCaffeineCache = (RedisCaffeineCache) cache;
                handleCacheOperation(redisCaffeineCache, channelTopic, cacheMessage);
            }
        } catch (ClassCastException e) {
            log.error("消息反序列化失败,类型不匹配", e);
        } catch (NullPointerException e) {
            log.error("消息处理过程中出现空指针异常", e);
        } catch (Exception e) {
            log.error("消息处理过程中出现未知异常", e);
        }
    }

    private void handleCacheOperation(RedisCaffeineCache redisCaffeineCache, ChannelTopicEnum channelTopic, RedisCaffeineMessage cacheMessage) {
        switch (channelTopic) {
            case REDIS_CAFFEINE_DEL_TOPIC:
                redisCaffeineCache.clearLocal(cacheMessage.getKey());
                log.info("删除一级缓存{}数据,key:{}", cacheMessage.getName(), cacheMessage.getKey());
                break;

            case REDIS_CAFFEINE_CLEAR_TOPIC:
                redisCaffeineCache.clearLocal(null);
                log.info("清除一级缓存{}数据", cacheMessage.getName());
                break;

            default:
                log.info("接收到没有定义的订阅消息频道数据");
                break;
        }
    }

    private String maskSensitiveInfo(String message) {
        // 对敏感信息进行脱敏处理，例如替换某些字符
        return message.replaceAll("(?i)(password|token)=\\w+", "$1=****");
    }

}
