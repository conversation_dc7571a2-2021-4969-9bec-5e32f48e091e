/**
 *
 *
 * @Title: SysUserDto.java
 * <AUTHOR>
 * @date 2022年9月19日
 * @version V1.0
 */
package cn.cmcc.system.api.pojo.dto;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

@Data
public class SysUserDto implements Serializable{

	/**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	private Long userId;

    private String account;

    private String name;

    private String phone;

    /**
     * 密码最后修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date lastModifyTime;
}
