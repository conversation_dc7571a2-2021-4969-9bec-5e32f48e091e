package cn.cmcc.common.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Treeselect树结构实体类
 * 
 * <AUTHOR>
 */
public class TreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    protected Long id;

    /** 节点名称 */
    protected String label;
    
    /** 节点编码 */
    protected String code;
    
    /** 节点类型 */
    protected String nodeType;
    
    /** 备注 */
    protected String remark;
    
    /** 父节点ID */
    protected Long pid;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected List<TreeSelect> children = new ArrayList<>();

    public TreeSelect()
    {

    }
    
    public TreeSelect(Long id,String label,Long pid)
    {
    	this.id = id;
    	this.label = label;
    	this.pid = pid;
    }
    
    public TreeSelect(Long id,String label,Long pid,String nodeType)
    {
    	this.id = id;
    	this.label = label;
    	this.pid = pid;
    	this.nodeType = nodeType;
    }
    
    public TreeSelect(Long id,String label,Long pid,String code,String remark)
    {
    	this.id = id;
    	this.label = label;
    	this.pid = pid;
    	this.code = code;
    	this.remark = remark;
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<TreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<TreeSelect> children)
    {
        this.children = children;
    }

	public String getNodeType() {
		return nodeType;
	}

	public void setNodeType(String nodeType) {
		this.nodeType = nodeType;
	}

	public Long getPid() {
		return pid;
	}

	public void setPid(Long pid) {
		this.pid = pid;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
