package cn.cmcc.common.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Map工具类
 * @date 2024/9/30 17:58
 */
public class MapUtils {
    public static <K,V> Map<K,V> hashMap(K k1, V v1, K k2, V v2){
        Map<K,V> map = new HashMap<>();
        map.put(k1, v1);
        map.put(k2, v2);
        return map;
    }
    public static Map<String,Object> hashMap(String k1, Object v1, String k2, Object v2,String k3, Object v3){
        Map<String,Object> map = new HashMap<>();
        map.put(k1, v1);
        map.put(k2, v2);
        map.put(k3, v3);
        return map;
    }
    public static Map<String,Object> hashMap(String k1, Object v1, String k2, Object v2,String k3, Object v3, String k4, Object v4){
        Map<String, Object> map = hashMap(k1, v1, k2, v2, k3, v3);
        map.put(k4, v4);
        return map;
    }
    public static Map<String,Object> hashMap(String k1, Object v1, String k2, Object v2,String k3, Object v3, String k4, Object v4,
                                             String k5, Object v5,String k6, Object v6, String k7, Object v7,String k8, Object v8,String k9, Object v9) {
        Map<String, Object> map = hashMap(k1, v1, k2, v2, k3, v3,k4, v4);
        map.put(k5, v5);
        map.put(k6, v6);
        map.put(k7, v7);
        map.put(k8, v8);
        map.put(k9, v9);
        return map;
    }

    /**
     * <AUTHOR>  2025/2/14 11:36
     * @description: 偶数为key，奇数为value
     */
    public static <K> Map<K,K> hashMap(K ... keyAndValue) {
        if(keyAndValue.length % 2 != 0){
            return null;
        }
        Map<K,K> map = new HashMap<>();
        for(int i = 0; i < keyAndValue.length; i= i + 2){
            map.put(keyAndValue[i], keyAndValue[i+1]);
        }
        return map;
    }
}
