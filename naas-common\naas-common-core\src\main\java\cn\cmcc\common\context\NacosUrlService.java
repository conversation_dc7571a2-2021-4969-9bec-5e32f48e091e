package cn.cmcc.common.context;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Component;

import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.exception.CustomException;

/**
 *
 *
 * <AUTHOR>
 * @date 2022年12月23日
 * @version V1.0
 */

@Component
public class NacosUrlService {

	@Autowired
    private NacosServiceManager nacosServiceManager;

	@Autowired
	private DiscoveryClient  discoveryClient;

	/**
	 * 可直接通用RestTemplate  + @LoadBalanced 调用服务，
	 * 加入loadbalance拦截器进行ip:port的替换，也就是将请求的地址中的服务逻辑名转为具体的服务地址
	 */

	/**
	 * 获取服务的ip+端口
	 */
	public String getServerUriByNacos(String serverName) throws Exception {
        NamingService namingService = nacosServiceManager.getNamingService();
        List<Instance> allInstances =  namingService.getAllInstances(serverName);
        if(CollectionUtils.isEmpty(allInstances)) {
    		throw new CustomException("服务【" + serverName + "】不存在");
    	}

        List<Instance> healthyInstances = allInstances.stream().filter(s->s.isHealthy() && s.isEnabled()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(healthyInstances)) {
    		throw new CustomException("服务【" + serverName + "】没有健康可用的实例");
    	}

    	Instance randomInstance = healthyInstances.get(Constants.rand.nextInt(healthyInstances.size()));
    	return "http://" + randomInstance.getIp() + ":" + randomInstance.getPort();
	}

	/**
	 * 获取服务的ip+端口
	 */
	public String getServerUriByCloud(String serverName) {
		List<ServiceInstance> allInstances = discoveryClient.getInstances(serverName);
        if(CollectionUtils.isEmpty(allInstances)) {
    		throw new CustomException("服务【" + serverName + "】不存在");
    	}

        return allInstances.get(Constants.rand.nextInt(allInstances.size())).getUri().toString();
	}
	
	/**
	 * @return All known service IDs.
	 */
	public List<String> getServices(){
		return discoveryClient.getServices();
	}
}
