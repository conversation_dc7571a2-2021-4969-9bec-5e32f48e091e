/**
 * @Title: ThirdApiTypeEnum.java
 * @Package cn.cmcc.common.enums
 * 
 * <AUTHOR>
 * @date 2021年9月22日
 * @version V1.0
 */
package cn.cmcc.common.enums;

import lombok.Getter;

@Getter
public enum TaskApiType {

	FLOW_NOTICE("flow_notice","流程通知","arrangeFlowNotice"),
	THIRD_CALLBACK("third_callback","三方系统回调"),
	ARRANGE_INNER_TASK("arrange_inner_task","编排内置任务","arrangeInnerTask"),
	ARRANGE_EXTERNAL_TASK("external_task","编排外部任务（HTTP）","arrangeExternalTask");
	
	private final String code;
	private final String name;
	private String taskObj;
	
	TaskApiType(String code,String name){
		this.code = code;
		this.name = name;
	}
	
	TaskApiType(String code,String name,String taskObj){
		this.code = code;
		this.name = name;
		this.taskObj = taskObj;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}
	
	public static TaskApiType getByValue(String value) {
        for (TaskApiType e : values()) {
            if (e.getCode().equals(value)) {
                return e;
            }
        }
        return null;
    }
}
