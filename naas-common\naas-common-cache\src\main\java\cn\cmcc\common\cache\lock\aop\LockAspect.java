package cn.cmcc.common.cache.lock.aop;

import cn.cmcc.common.cache.annotation.Lock;
import cn.cmcc.common.cache.lock.LockService;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.utils.SpelUtils;
import cn.cmcc.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 *
 *
 * <AUTHOR>
 * @date 2022年12月27日
 * @version V1.0
 */

@Aspect
@Component
@Slf4j
public class LockAspect {

	@Resource
	private LockService lockService;

	/**
     * 在方法上执行同步锁
     */
    @Around("@annotation(lock)")
    public Object around(ProceedingJoinPoint point,Lock lock) throws Throwable{
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Object target = point.getTarget();

        String lockKey = lock.key();
        Assert.hasText(lockKey, "@Lock key must not be null or empty");

        String key= getLockKey(methodSignature,point,lock);

        if(StringUtils.isEmpty(key)) {
        	return point.proceed();
        }

        String prefix = getKeyPrefix(methodSignature,target,lock);

        boolean lockFlag = false;
        Object proceed;
        try {
        	lockFlag = lockService.tryLock(prefix,key,lock.waitTime(),lock.leaseTime(), lock.timeUnit());
        	if (lockFlag) {
        		proceed = point.proceed();
        	}else {
        		throw new CustomException(lock.msg());
        	}
		}finally {
            if (lockFlag){
            	lockService.unlock(prefix,key);
                log.info("lock {} has been released",key);
            }
        }
        return proceed;

    }

    /**
     * 获取className+methodName
     * @param methodSignature
     * @return
     * @throws NoSuchMethodException
     */
    private String getKeyPrefix(MethodSignature methodSignature,Object target,Lock targetAnnotation) throws NoSuchMethodException{
    	if (StringUtils.isNotBlank(targetAnnotation.prefix())){
        	return targetAnnotation.prefix();
        }else {
        	Method currentMethod = target.getClass().getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
            return target.getClass().getName() + "-" + currentMethod.getName();
        }
    }

    /**
     * 获取拦截到的方法,解析分布式锁key值（如果包含el表达式，则从中解析出内容）
     *
     * @param joinPoint 切点
     * @return redisKey
     * @throws NoSuchMethodException
     */
    private String getLockKey(MethodSignature methodSignature,
                              ProceedingJoinPoint joinPoint,
                              Lock targetAnnotation) throws NoSuchMethodException {
        Object[] arguments = joinPoint.getArgs();
        String key = targetAnnotation.key();
        if(key.contains("#")) {
        	key = SpelUtils.parse(key , methodSignature.getMethod(), arguments);
        }
        return key;
    }
}
