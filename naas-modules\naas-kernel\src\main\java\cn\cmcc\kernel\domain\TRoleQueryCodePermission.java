package cn.cmcc.kernel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色查询编码权限表实体类
 * 对应数据库表：t_role_query_code_permission
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@TableName("t_role_query_code_permission")
public class TRoleQueryCodePermission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色标识
     */
    private String roleKey;

    /**
     * 查询编码
     */
    private String queryCode;

    /**
     * 权限类型：read-查询，write-修改
     */
    private String permissionType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 构造函数
     */
    public TRoleQueryCodePermission() {
        super();
    }

    /**
     * 构造函数
     *
     * @param roleId 角色ID
     * @param roleKey 角色标识
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     */
    public TRoleQueryCodePermission(String roleId, String roleKey, String queryCode, String permissionType) {
        this.roleId = roleId;
        this.roleKey = roleKey;
        this.queryCode = queryCode;
        this.permissionType = permissionType;
    }

    /**
     * 权限类型常量
     */
    public static class PermissionType {
        public static final String READ = "read";
        public static final String WRITE = "write";
    }
}
