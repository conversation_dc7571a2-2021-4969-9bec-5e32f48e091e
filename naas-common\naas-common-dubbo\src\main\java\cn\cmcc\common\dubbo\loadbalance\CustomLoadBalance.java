package cn.cmcc.common.dubbo.loadbalance;

import java.util.List;

import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.cluster.loadbalance.RandomLoadBalance;

import cn.cmcc.common.utils.StringUtils;

/**  
 * 
 * <AUTHOR>
 * @date 2025-04-25 11:29:59
*/
public class CustomLoadBalance extends RandomLoadBalance{

	@Override
	protected <T> Invoker<T> doSelect(List<Invoker<T>> invokers, URL url, Invocation invocation) {
		String clientIp = RpcContext.getClientAttachment().getAttachment("clientIp");
		if(StringUtils.isNotEmpty(clientIp)) {
			Invoker<T> curr = invokers.stream().filter(invoker ->{
				return invoker.getUrl().getIp().equals(clientIp.trim());
			}).findFirst().orElse(null);
			
	        if (curr != null) {
	            return curr;
	        }
		}
        return super.doSelect(invokers, url, invocation);
	}

}
