/**
 *
 *
 * @Title: SysConfigBO.java
 * <AUTHOR>
 * @date 2022年9月19日
 * @version V1.0
 */
package cn.cmcc.common.pojo;

import java.io.Serializable;

import lombok.Data;

@Data
public class SysConfigBO implements Serializable{

	/**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	private Long id;

	private String name;

	private String code;

	private String value;

	private String remark;
	//是否是定时任务调起,默认为true
	private boolean hasScheduled = true;
	private LoginUser loginUser;
}
