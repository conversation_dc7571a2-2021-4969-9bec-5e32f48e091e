<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.cmcc.kernel.mapper.TQueryCodePermissionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.cmcc.kernel.domain.TQueryCodePermission">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="query_code" property="queryCode" jdbcType="VARCHAR"/>
        <result column="permission_type" property="permissionType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, username, query_code, permission_type, create_time, create_by, update_time, update_by, remark
    </sql>

    <!-- 根据用户名查询用户的所有权限 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_query_code_permission
        WHERE username = #{username}
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户名和查询编码查询特定权限 -->
    <select id="selectByUsernameAndCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_query_code_permission
        WHERE username = #{username} AND query_code = #{queryCode}
        LIMIT 1
    </select>

    <!-- 根据用户名和查询编码查询特定权限（指定权限类型） -->
    <select id="selectByUsernameAndCodeAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_query_code_permission
        WHERE username = #{username} 
          AND query_code = #{queryCode} 
          AND permission_type = #{permissionType}
        LIMIT 1
    </select>

    <!-- 根据用户名和查询编码删除特定权限 -->
    <delete id="deleteByUsernameAndCode">
        DELETE FROM t_query_code_permission
        WHERE username = #{username} AND query_code = #{queryCode}
    </delete>

    <!-- 根据用户名和查询编码删除特定权限（指定权限类型） -->
    <delete id="deleteByUsernameAndCodeAndType">
        DELETE FROM t_query_code_permission
        WHERE username = #{username} 
          AND query_code = #{queryCode} 
          AND permission_type = #{permissionType}
    </delete>

    <!-- 根据查询编码查询所有有权限的用户 -->
    <select id="selectByQueryCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_query_code_permission
        WHERE query_code = #{queryCode}
        ORDER BY create_time DESC
    </select>

    <!-- 获取用户可访问的所有查询编码 -->
    <select id="selectAccessibleCodesByUsername" resultType="java.lang.String">
        SELECT DISTINCT query_code
        FROM t_query_code_permission
        WHERE username = #{username} AND permission_type = #{permissionType}
        ORDER BY query_code
    </select>

    <!-- 批量插入用户权限 -->
    <insert id="batchInsert">
        INSERT INTO t_query_code_permission (id, user_id, username, query_code, permission_type, create_time, create_by)
        VALUES
        <foreach collection="permissions" item="item" separator=",">
            (#{item.id}, #{item.userId}, #{item.username}, #{item.queryCode}, #{item.permissionType}, NOW(), #{item.createBy})
        </foreach>
    </insert>

    <!-- 批量删除用户权限 -->
    <delete id="batchDeleteByUsernameAndCodes">
        DELETE FROM t_query_code_permission
        WHERE username = #{username}
        AND query_code IN
        <foreach collection="queryCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>

</mapper>
