package cn.cmcc.kernel.mapper;

import cn.cmcc.kernel.domain.TRoleQueryCodePermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色查询编码权限表 数据层
 *
 * <AUTHOR> 4.0 sonnet
 */
@Mapper
public interface TRoleQueryCodePermissionMapper extends BaseMapper<TRoleQueryCodePermission> {

    /**
     * 根据角色标识查询角色的所有权限
     *
     * @param roleKey 角色标识
     * @return 权限列表
     */
    List<TRoleQueryCodePermission> selectByRoleKey(@Param("roleKey") String roleKey);

    /**
     * 根据角色标识列表查询权限
     *
     * @param roleKeys 角色标识列表
     * @return 权限列表
     */
    List<TRoleQueryCodePermission> selectByRoleKeys(@Param("roleKeys") List<String> roleKeys);

    /**
     * 根据查询编码查询所有有权限的角色
     *
     * @param queryCode 查询编码
     * @return 权限列表
     */
    List<TRoleQueryCodePermission> selectByQueryCode(@Param("queryCode") String queryCode);

    /**
     * 根据角色标识和查询编码查询特定权限
     *
     * @param roleKey 角色标识
     * @param queryCode 查询编码
     * @return 权限信息
     */
    TRoleQueryCodePermission selectByRoleKeyAndCode(@Param("roleKey") String roleKey,
                                                    @Param("queryCode") String queryCode);

    /**
     * 根据角色标识和查询编码查询特定权限（指定权限类型）
     *
     * @param roleKey 角色标识
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     * @return 权限信息
     */
    TRoleQueryCodePermission selectByRoleKeyAndCodeAndType(@Param("roleKey") String roleKey,
                                                           @Param("queryCode") String queryCode,
                                                           @Param("permissionType") String permissionType);

    /**
     * 根据角色标识和查询编码删除特定权限
     *
     * @param roleKey 角色标识
     * @param queryCode 查询编码
     * @return 影响行数
     */
    int deleteByRoleKeyAndCode(@Param("roleKey") String roleKey,
                               @Param("queryCode") String queryCode);

    /**
     * 根据角色标识和查询编码删除特定权限（指定权限类型）
     *
     * @param roleKey 角色标识
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     * @return 影响行数
     */
    int deleteByRoleKeyAndCodeAndType(@Param("roleKey") String roleKey,
                                      @Param("queryCode") String queryCode,
                                      @Param("permissionType") String permissionType);

    /**
     * 根据角色标识列表和查询编码查询权限
     *
     * @param roleKeys 角色标识列表
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     * @return 权限列表
     */
    List<TRoleQueryCodePermission> selectByRoleKeysAndCodeAndType(@Param("roleKeys") List<String> roleKeys,
                                                                  @Param("queryCode") String queryCode,
                                                                  @Param("permissionType") String permissionType);

    /**
     * 获取角色可访问的所有查询编码
     *
     * @param roleKey 角色标识
     * @param permissionType 权限类型
     * @return 查询编码列表
     */
    List<String> selectAccessibleCodesByRoleKey(@Param("roleKey") String roleKey,
                                                @Param("permissionType") String permissionType);

    /**
     * 批量插入角色权限
     *
     * @param permissions 权限列表
     * @return 影响行数
     */
    int batchInsert(@Param("permissions") List<TRoleQueryCodePermission> permissions);

    /**
     * 批量删除角色权限
     *
     * @param roleKey 角色标识
     * @param queryCodes 查询编码列表
     * @return 影响行数
     */
    int batchDeleteByRoleKeyAndCodes(@Param("roleKey") String roleKey,
                                     @Param("queryCodes") List<String> queryCodes);
}
