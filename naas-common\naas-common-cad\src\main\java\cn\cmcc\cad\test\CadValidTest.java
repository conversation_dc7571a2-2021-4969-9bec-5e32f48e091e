package cn.cmcc.cad.test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aspose.cad.fileformats.cad.CadAcdsList;
import com.aspose.cad.fileformats.cad.CadBlockDictionary;
import com.aspose.cad.fileformats.cad.CadImage;
import com.aspose.cad.fileformats.cad.CadLayersList;
import com.aspose.cad.fileformats.cad.cadobjects.Cad2DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.Cad3DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.CadBaseEntity;
import com.aspose.cad.fileformats.cad.cadobjects.CadBlockEntity;
import com.aspose.cad.fileformats.cad.cadobjects.CadInsertObject;
import com.aspose.cad.fileformats.cad.cadobjects.CadLwPolyline;
import com.aspose.cad.fileformats.cad.cadobjects.CadMText;
import com.aspose.cad.fileformats.cad.cadtables.CadLayerTable;
import com.aspose.cad.internal.aF.iF;
import com.aspose.cad.system.collections.Generic.Dictionary.KeyCollection;

import lombok.Data;

@Data
public class CadValidTest {
	public static String path = "E:\\2024\\indoor\\dwg/";

	public static void main(String[] args) {
		CadImage image = null;
		try {
			String file = path + "1/AH_13618.dwg";
			//file = path + "AH_10860_2.dwg";
			file = path + "jy.dwg";
			image = (CadImage) CadImage.load(file);
			
		//	CadBaseObject object = image.
			
			CadAcdsList  objects = image.getCadAcds();
			
			
			Map<String, Integer> objectClassMap = new HashMap<String, Integer>();
			CadBlockDictionary block = image.getBlockEntities();
			  for (CadBlockEntity blockEntity : block.getValues()) {
				  System.out.println(block);
			  }
			KeyCollection<String, CadBlockEntity> keys = block.getKeys();
			for (String blockKey : keys) {
				CadBlockEntity entity = block.get_Item(blockKey);
				CadBaseEntity[] entities = entity.getEntities();
				for (CadBaseEntity baseEntity : entities) {
				
				}
				System.out.println(blockKey+"\t"+entity.getClass().getName()+"\t"+entities.length);
				if(entities.length==2) {
				System.out.println(blockKey+"\t"+entities.length);
				}
			}

			
			objectClassMap = MapUtil.sortMapByValue(objectClassMap);
			for(String name:objectClassMap.keySet()) {
				System.out.println(objectClassMap.get(name)+"\t"+name);
			}

			CadBaseEntity[]  entities =  image.getEntities();
			Map<String, Integer> layerEntityMap = new HashMap<String, Integer>();
			Map<String, Integer> entityClassMap = new HashMap<String, Integer>();
			for(CadBaseEntity entity : entities) {
				String layer = entity.getLayerName();
				String cl = entity.getClass().getSimpleName();
				
				if(!layerEntityMap.containsKey(layer)){
					layerEntityMap.put(layer, 0);
				}
				if(!entityClassMap.containsKey(cl)){
					entityClassMap.put(cl, 0);
				}
				layerEntityMap.put(layer, layerEntityMap.get(layer)+1);
				entityClassMap.put(cl, entityClassMap.get(cl)+1);
				
				
				//45CEBD
				String entityHandle = entity.getObjectHandle();
				
				 
				if(entityHandle.equals("45CEBD")) {
					//System.err.println(entity);
				}
				if(entity instanceof CadMText) {
					CadMText cadMText = (CadMText)entity;
					Cad3DPoint  point =  cadMText.getInsertionPoint();
					String text =  cadMText.getText();
					if(text.equals("36m")&& (point.getX()>7675) && (point.getX()<7676)){
						//System.out.println();
					}
					//System.err.println(text);
				}else if(entity instanceof CadLwPolyline) {
					CadLwPolyline cadPolyline = (CadLwPolyline)entity;
					Short colorId= cadPolyline.getColorId();
					String colorName= cadPolyline.getColorName();
					Integer colorValue= cadPolyline.getColorValue();
					
					if(layer.equals("连接线")) {
						List<Cad2DPoint> points= cadPolyline.getCoordinates();
						//System.err.println("连接线: "+points.size());
						if(points.size()==2) {
							Cad2DPoint point = points.get(0);
							Cad2DPoint point2 = points.get(1);
							if((point.getY()+"").startsWith("-2551")  && (point.getX()>7645) && (point.getX()<7706) && (point2.getX()>7645) && (point2.getX()<7706)){
							//	System.out.println (Math.abs(point.getX()-point2.getX()));
							}
						}
					}
				}else if( entity instanceof CadInsertObject) {
					CadInsertObject cadInsertObject = (CadInsertObject)entity;
					String blockKey = cadInsertObject.getOriginalBlockName();
					Cad3DPoint point =  cadInsertObject.getInsertionPoint();
					if(blockKey.equals("M0_2")) {
						System.out.println(point.getX()+"\t"+point.getY() +"\t"+image.getMinPoint().getX()+"\t"+image.getMinPoint().getY()+"\t"+image.getMaxPoint().getX()+"\t"+image.getMaxPoint().getY());
						point.getX();
					}
				}

				
				//System.out.println(entity.getLayerName());
			}
			CadLayersList cadLayersList = image.getLayers();
			 // 遍历图层
	        for (int i = 0; i < cadLayersList.size(); i++) {
	        	CadLayerTable layer = (CadLayerTable)cadLayersList.get_Item(i);
	        	
	            System.out.println(layer.getName()+"\t"+layer.getFlags());
	        }
			System.out.println(cadLayersList.size());
			List<String> list =  cadLayersList.getLayersNames();
			
			
			layerEntityMap = MapUtil.sortMapByValue(layerEntityMap);
			for(String name:layerEntityMap.keySet()) {
				System.out.println(layerEntityMap.get(name)+"\t"+name);
			}
			System.out.println(list.size());
			
			entityClassMap = MapUtil.sortMapByValue(entityClassMap);
			for(String name:entityClassMap.keySet()) {
				System.out.println(entityClassMap.get(name)+"\t"+name);
			}
		} catch (Exception e) {
			throw e;
		} finally {
			try {
				image.close();
				image = null;
			} catch (Exception e) {
				System.out.println(e.getMessage());
			}
		}
	}

}
