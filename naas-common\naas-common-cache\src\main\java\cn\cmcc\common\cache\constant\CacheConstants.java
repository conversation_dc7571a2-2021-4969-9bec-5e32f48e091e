/**
 * Copyright 2018-2020 cmcc (cmcc)
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.cmcc.common.cache.constant;

/**
 * 缓存标识前缀集合,常用在ConstantFactory类中
 *
 * <AUTHOR>
 * @date 2021-03-21 9:37
 */
public interface CacheConstants {

	/**
	 * 缓存有效期
	 */
	long EXPIRE_AFTER_WRITE = 86400;

	/**
	 * 缓存默认容量
	 */
	int INITIAL_CAPACTITY = 10;

	/**
     * 缓存刷新时间，默认120（分钟）
     */
	long REFRESH_TIME = 120;

	/**
	 *最大容量
	 */
	long MAX_MUM_SIZE = 10000;

	int FIVE_MIN = 300;

	int TEN_SECONDES = 10;

	interface CacheName {
		/**
	     * 常量缓存
	     */
	    String CACHE_DEFAULT = "CACHE_DEFAULT";
	    String CACHE_REPORT = "CACHE_REPORT";
	    String CACHE_LOGIN = "CACHE_LOGIN";
	    String CACHE_DICT= "CACHE_DICT";
	    String CACHE_REPEAT= "CACHE_REPEAT";
	    String CACHE_ORDER_LOCK = "CACHE_ORDER_LOCK";
		String QUERY_CACHE = "QUERY_CACHE";
	}


    /**
     * 缓存 key
     * */
	interface CacheKey {
    	// 业务阶段字段配置信息
    	String STAGE_FIELD_CONFIG = "stage_feild_config";
    	String STAGE_FIELD_CONFIG_REQUIRED = "stage_feild_config_required";
    	// 业务阶段字段数据字典信息
    	String STAGE_FIELD_DATA_DICT = "stage_feild_data_dict";
    	// 通用查询
    	String COMMON_QUERY = "common_query";
    	// 通用查找字段
    	String COMMON_QUERY_FIELD = "common_query_field";
    	//工单处理锁
    	String ORDER_DEAL_LOCK = "order_deal_lock";
    	//工单信息监听
    	String ORDER_INFO_WATCH = "order_data_watch";
    	//工单保存key
    	String ORDER_SAVE_RECORD = "order_save_record";
    	//节点可编辑表
    	String NODE_EDIT_TABLE = "node_edit_table";
    	//节点可编辑表
    	String ORDER_GENERATE_LOCK = "order_generate_lock";
    	//db
    	String DB_OPER_AUTH_KEY = "db_oper_auth_key";
    	//流程节点信息
    	String FLOW_NODE_INFO = "flow_node_info";
    	//流程节点流转限制信息
    	String FLOW_NODE_VERIFICATION_INFO = "flow_verification_info";
    	/**
         * 验证码 redis key
         */
        String CAPTCHA_CODE_KEY = "captcha_codes:";

        /**
         * 缓存用户key
         * */
        String LOGIN_TOKEN_KEY = "login_user_cache:";

        /**
         * 防重提交 redis key
         */
        String REPEAT_SUBMIT_KEY = "repeat_submit:";
        /**
         * 参数管理 cache key
         */
        String SYS_CONFIG_KEY = "sys_config:";

        /**
         * 字典管理 cache key
         */
        String SYS_DICT_KEY = "sys_dict:";

        /**
         * powerjob信息
         */
        String POWERJOB_APP_INFO = "powerjob_app_info";

		/**
         * 模块版本
         */
	    String CACHE_MODULE_VERSION = "cache_module_version:";
    }

}
