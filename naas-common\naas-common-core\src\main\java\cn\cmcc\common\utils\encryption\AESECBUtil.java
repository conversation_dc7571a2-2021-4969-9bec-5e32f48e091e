package cn.cmcc.common.utils.encryption;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

import cn.cmcc.common.constant.Constants;
import cn.hutool.crypto.KeyUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import lombok.extern.slf4j.Slf4j;

/**  
 * 
 * sonar 漏洞改用 hutool里面的
 * 
 * <AUTHOR>
 * @date 2024-06-26 09:32:48
*/

@Slf4j
public class AESECBUtil {
    
	public static String encrypt(String content, String key) {
		AES aesPkc = new AES(KeyUtil.generateKey(SymmetricAlgorithm.AES.getValue(),128,getSecureRandom(key)));
		return aesPkc.encryptBase64(content);
	}
	
	public static String decrypt(String content, String key) {
		AES aesPkc = new AES(KeyUtil.generateKey(SymmetricAlgorithm.AES.getValue(),128,getSecureRandom(key)));
		return aesPkc.decryptStr(content);
	}
	
	 private static SecureRandom getSecureRandom(String key) {
	    	SecureRandom random;
			try {
				random = SecureRandom.getInstance("SHA1PRNG");
				random.setSeed(Constants.AES_KEY.getBytes());
		  	   	return random;
			} catch (NoSuchAlgorithmException e) {
				log.info(e.getMessage(),e);
			}
	  	   	return null;
	    }

	public static void main(String args[]) {
		AES aesPkc = new AES(KeyUtil.generateKey(SymmetricAlgorithm.AES.getValue(),128,getSecureRandom(Constants.AES_KEY)));
	    System.out.println(aesPkc.encryptBase64("dS#PPGHgK8"));
	    System.out.println(aesPkc.decryptStr("IB35VEEhLQhBhTW4pTOPtw=="));
	}
}
