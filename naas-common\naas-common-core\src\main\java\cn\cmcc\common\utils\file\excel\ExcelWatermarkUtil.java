package cn.cmcc.common.utils.file.excel;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.opc.PackageRelationship;
import org.apache.poi.openxml4j.opc.TargetMode;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFRelation;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Excel水印工具类
 *
 * <p>支持为Excel文件添加文本水印，仅支持XLSX格式。支持单行和多行水印。</p>
 *
 * <p>使用示例：</p>
 * <pre>
 * // 简单使用
 * ExcelWatermarkUtil.addWatermark(workbook, "机密文件");
 *
 * // 多行水印
 * String[] lines = {"机密文件", "仅供内部使用", "请勿外传"};
 * ExcelWatermarkUtil.addMultiLineWatermark(workbook, lines);
 *
 * // 多行水印（自定义行间距）
 * ExcelWatermarkUtil.addMultiLineWatermark(workbook, lines, 10);
 *
 * // 自定义配置
 * WatermarkConfig config = WatermarkConfig.builder()
 *     .text("内部资料\n仅供参考")
 *     .fontSize(24)
 *     .fontColor(new Color(255, 0, 0, 80))
 *     .rotation(-45f)
 *     .lineSpacing(8)
 *     .build();
 * ExcelWatermarkUtil.addWatermark(workbook, config);
 *
 * // 使用预设
 * WatermarkConfig config = ExcelWatermarkUtil.createPresetConfig("机密文件", WatermarkPreset.PROMINENT);
 * ExcelWatermarkUtil.addWatermark(workbook, config);
 *
 * // 创建多行水印配置
 * WatermarkConfig config = ExcelWatermarkUtil.createMultiLineConfig("机密文件\n仅供内部使用\n请勿外传");
 * ExcelWatermarkUtil.addWatermark(workbook, config);
 * </pre>
 *
 * <AUTHOR> Assistant
 * @date 2025/05/30
 * @version 1.3
 */
@Slf4j
public class ExcelWatermarkUtil {

    /**
     * 水印图片缓存，避免重复创建相同的水印图片
     */
    private static final ConcurrentMap<String, byte[]> WATERMARK_CACHE = new ConcurrentHashMap<>();

    /**
     * 缓存大小限制
     */
    private static final int MAX_CACHE_SIZE = 100;

    /**
     * 为Excel工作簿添加水印
     *
     * @param workbook      Excel工作簿，不能为null
     * @param watermarkText 水印文本，不能为null或空字符串
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public static void addWatermark(Workbook workbook, String watermarkText) {
        validateParameters(workbook, watermarkText);

        WatermarkConfig config = new WatermarkConfig();
        config.setText(watermarkText);
        addWatermark(workbook, config);
    }

    /**
     * 为Excel工作簿添加多行水印
     *
     * @param workbook       Excel工作簿，不能为null
     * @param watermarkLines 水印文本行数组，不能为null或空
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public static void addMultiLineWatermark(Workbook workbook, String[] watermarkLines) {
        Objects.requireNonNull(workbook, "工作簿不能为null");
        Objects.requireNonNull(watermarkLines, "水印文本行数组不能为null");
        if (watermarkLines.length == 0) {
            throw new IllegalArgumentException("水印文本行数组不能为空");
        }

        String multiLineText = String.join("\n", watermarkLines);
        WatermarkConfig config = createMultiLineConfig(multiLineText);
        addWatermark(workbook, config);
    }

    /**
     * 为Excel工作簿添加多行水印（带自定义行间距）
     *
     * @param workbook       Excel工作簿，不能为null
     * @param watermarkLines 水印文本行数组，不能为null或空
     * @param lineSpacing    行间距，必须大于等于0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public static void addMultiLineWatermark(Workbook workbook, String[] watermarkLines, int lineSpacing) {
        Objects.requireNonNull(workbook, "工作簿不能为null");
        Objects.requireNonNull(watermarkLines, "水印文本行数组不能为null");
        if (watermarkLines.length == 0) {
            throw new IllegalArgumentException("水印文本行数组不能为空");
        }
        if (lineSpacing < 0) {
            throw new IllegalArgumentException("行间距不能为负数");
        }

        String multiLineText = String.join("\n", watermarkLines);
        WatermarkConfig config = createMultiLineConfig(multiLineText);
        config.setLineSpacing(lineSpacing);
        addWatermark(workbook, config);
    }

    /**
     * 为Excel工作簿添加水印
     *
     * @param workbook Excel工作簿，不能为null
     * @param config   水印配置，不能为null
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public static void addWatermark(Workbook workbook, WatermarkConfig config) {
        validateParameters(workbook, config);

        try {
            config.validate(); // 验证配置参数
            byte[] watermarkData = getOrCreateWatermarkImage(config);

            if (workbook instanceof XSSFWorkbook) {
                XSSFWorkbook xssfWorkbook = (XSSFWorkbook) workbook;
                // 为每个工作表添加水印
                int sheetCount = xssfWorkbook.getNumberOfSheets();
                for (int i = 0; i < sheetCount; i++) {
                    XSSFSheet sheet = xssfWorkbook.getSheetAt(i);
                    if (sheet != null) {
                        addWatermarkToSheet(sheet, watermarkData);
                    }
                }
                log.debug("成功为{}个工作表添加水印", sheetCount);
            } else if (workbook instanceof SXSSFWorkbook) {
                SXSSFWorkbook sxssfWorkbook = (SXSSFWorkbook) workbook;
                // 获取内部XSSFWorkbook
                XSSFWorkbook xssfWorkbook = sxssfWorkbook.getXSSFWorkbook();
                // 为每个工作表添加水印
                int sheetCount = xssfWorkbook.getNumberOfSheets();
                for (int i = 0; i < sheetCount; i++) {
                    XSSFSheet sheet = xssfWorkbook.getSheetAt(i);
                    if (sheet != null) {
                        addWatermarkToSheet(sheet, watermarkData);
                    }
                }
                log.debug("成功为SXSSFWorkbook的{}个工作表添加水印", sheetCount);
            } else {
                log.warn("水印功能仅支持XLSX格式的Excel文件(XSSFWorkbook或SXSSFWorkbook)");
                return;
            }
        } catch (IOException e) {
            log.error("创建水印图片失败", e);
            throw new RuntimeException("创建水印图片失败", e);
        } catch (Exception e) {
            log.error("添加Excel水印失败", e);
            throw new RuntimeException("添加Excel水印失败", e);
        }
    }

    /**
     * 参数验证 - 工作簿和文本
     */
    private static void validateParameters(Workbook workbook, String watermarkText) {
        Objects.requireNonNull(workbook, "工作簿不能为null");
        Objects.requireNonNull(watermarkText, "水印文本不能为null");
        if (watermarkText.trim().isEmpty()) {
            throw new IllegalArgumentException("水印文本不能为空");
        }
    }

    /**
     * 参数验证 - 工作簿和配置
     */
    private static void validateParameters(Workbook workbook, WatermarkConfig config) {
        Objects.requireNonNull(workbook, "工作簿不能为null");
        Objects.requireNonNull(config, "水印配置不能为null");
    }

    /**
     * 获取或创建水印图片（带缓存）
     *
     * @param config 水印配置
     * @return 水印图片字节数组
     * @throws IOException 创建图片失败时抛出
     */
    private static byte[] getOrCreateWatermarkImage(WatermarkConfig config) throws IOException {
        String cacheKey = config.getCacheKey();

        // 先从缓存中获取
        byte[] cachedData = WATERMARK_CACHE.get(cacheKey);
        if (cachedData != null) {
            log.debug("从缓存中获取水印图片");
            return cachedData;
        }

        // 缓存未命中，创建新的水印图片
        byte[] watermarkData = createWatermarkImage(config);

        // 添加到缓存（控制缓存大小）
        if (WATERMARK_CACHE.size() < MAX_CACHE_SIZE) {
            WATERMARK_CACHE.put(cacheKey, watermarkData);
            log.debug("水印图片已添加到缓存");
        } else {
            log.debug("缓存已满，跳过缓存");
        }

        return watermarkData;
    }

    /**
     * 创建水印图片
     *
     * @param config 水印配置
     * @return 水印图片字节数组
     * @throws IOException 创建图片失败时抛出
     */
    private static byte[] createWatermarkImage(WatermarkConfig config) throws IOException {
        // 创建临时图片计算文本尺寸
        BufferedImage tempImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D tempG2d = tempImage.createGraphics();
        Font font = new Font(config.getFontName(), Font.BOLD, config.getFontSize());
        tempG2d.setFont(font);

        // 分割文本为多行
        String[] lines = config.getText().split("\\n");

        // 计算多行文本尺寸
        FontRenderContext frc = tempG2d.getFontRenderContext();
        int maxTextWidth = 0;
        int totalTextHeight = 0;

        for (String line : lines) {
            Rectangle2D bounds = font.getStringBounds(line, frc);
            int lineWidth = (int) Math.ceil(bounds.getWidth());
            int lineHeight = (int) Math.ceil(bounds.getHeight());

            maxTextWidth = Math.max(maxTextWidth, lineWidth);
            totalTextHeight += lineHeight;
        }

        // 添加行间距
        if (lines.length > 1) {
            totalTextHeight += (lines.length - 1) * config.getLineSpacing();
        }

        tempG2d.dispose();

        // 计算旋转后的文本尺寸
        double radians = Math.toRadians(config.getRotation());
        double sinTheta = Math.abs(Math.sin(radians));
        double cosTheta = Math.abs(Math.cos(radians));
        int rotatedTextWidth = (int) Math.ceil(maxTextWidth * cosTheta + totalTextHeight * sinTheta);
        int rotatedTextHeight = (int) Math.ceil(maxTextWidth * sinTheta + totalTextHeight * cosTheta);

        // 确保图片尺寸足够大，至少能容纳一个完整的旋转后的文本
        int minWidth = rotatedTextWidth + 50;
        int minHeight = rotatedTextHeight + 50;

        if (config.getWidth() < minWidth) {
            config.setWidth(minWidth);
        }
        if (config.getHeight() < minHeight) {
            config.setHeight(minHeight);
        }

        // 确保间隔足够大，避免文本重叠
        if (config.getXInterval() < rotatedTextWidth + 20) {
            config.setXInterval(rotatedTextWidth + 20);
        }
        if (config.getYInterval() < rotatedTextHeight + 20) {
            config.setYInterval(rotatedTextHeight + 20);
        }

        // 创建最终图片
        BufferedImage image = new BufferedImage(config.getWidth(), config.getHeight(), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = null;
        ByteArrayOutputStream baos = null;

        try {
            g2d = image.createGraphics();

            // 设置高质量渲染
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);

            // 设置字体
            g2d.setFont(font);
            g2d.setColor(config.getFontColor());

            // 绘制水印文本（重复绘制以填充整个图片）
            drawRepeatedText(g2d, config, rotatedTextWidth, rotatedTextHeight);

            // 转换为字节数组
            baos = new ByteArrayOutputStream();
            if (!ImageIO.write(image, "PNG", baos)) {
                throw new IOException("无法写入PNG格式图片");
            }
            return baos.toByteArray();

        } finally {
            // 确保资源被正确释放
            if (g2d != null) {
                g2d.dispose();
            }
            if (baos != null) {
                try {
                    baos.close();
                } catch (IOException e) {
                    log.warn("关闭ByteArrayOutputStream失败", e);
                }
            }
        }
    }

    /**
     * 绘制重复的水印文本
     */
    private static void drawRepeatedText(Graphics2D g2d, WatermarkConfig config, int rotatedTextWidth, int rotatedTextHeight) {
        // 保存原始变换
        AffineTransform originalTransform = g2d.getTransform();

        // 计算网格
        int cols = (int) Math.ceil((double) config.getWidth() / config.getXInterval()) + 1;
        int rows = (int) Math.ceil((double) config.getHeight() / config.getYInterval()) + 1;

        // 计算起始偏移，使水印在图片中居中
        int startX = (config.getWidth() - (cols - 1) * config.getXInterval()) / 2;
        int startY = (config.getHeight() - (rows - 1) * config.getYInterval()) / 2;

        // 分割文本为多行
        String[] lines = config.getText().split("\\n");

        // 绘制水印文本网格
        for (int row = 0; row < rows; row++) {
            for (int col = 0; col < cols; col++) {
                int x = startX + col * config.getXInterval();
                int y = startY + row * config.getYInterval();

                // 重置变换
                g2d.setTransform(originalTransform);

                // 在每个网格点应用旋转
                g2d.rotate(Math.toRadians(config.getRotation()), x, y);

                // 绘制多行文本
                drawMultiLineText(g2d, lines, x, y, config.getLineSpacing());
            }
        }

        // 恢复原始变换
        g2d.setTransform(originalTransform);
    }

    /**
     * 绘制多行文本
     *
     * @param g2d         图形上下文
     * @param lines       文本行数组
     * @param centerX     中心X坐标
     * @param centerY     中心Y坐标
     * @param lineSpacing 行间距
     */
    private static void drawMultiLineText(Graphics2D g2d, String[] lines, int centerX, int centerY, int lineSpacing) {
        FontMetrics fm = g2d.getFontMetrics();
        int lineHeight = fm.getHeight();

        // 计算总高度
        int totalHeight = lines.length * lineHeight + (lines.length - 1) * lineSpacing;

        // 计算起始Y坐标，使多行文本整体居中
        int startY = centerY - totalHeight / 2 + fm.getAscent();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            int textWidth = fm.stringWidth(line);
            int textX = centerX - textWidth / 2; // 水平居中
            int textY = startY + i * (lineHeight + lineSpacing);

            g2d.drawString(line, textX, textY);
        }
    }

    /**
     * 为工作表添加水印
     *
     * @param sheet         工作表，不能为null
     * @param watermarkData 水印图片数据，不能为null
     * @throws RuntimeException 添加水印失败时抛出
     */
    private static void addWatermarkToSheet(XSSFSheet sheet, byte[] watermarkData) {
        Objects.requireNonNull(sheet, "工作表不能为null");
        Objects.requireNonNull(watermarkData, "水印图片数据不能为null");

        try {
            // 添加图片到工作簿
            int pictureIdx = sheet.getWorkbook().addPicture(watermarkData, Workbook.PICTURE_TYPE_PNG);

            // 获取图片数据
            XSSFPictureData pictureData = (XSSFPictureData) sheet.getWorkbook().getAllPictures().get(pictureIdx);

            // 创建关系
            PackageRelationship pr = sheet.getPackagePart().addRelationship(
                    pictureData.getPackagePart().getPartName(),
                    TargetMode.INTERNAL,
                    XSSFRelation.IMAGES.getRelation());
            if (pr == null) {
                log.warn("创建图片关系失败，工作表：{}", sheet.getSheetName());
                return;
            }

            // 设置背景图片
            sheet.getCTWorksheet().addNewPicture().setId(pr.getId());
            log.debug("成功为工作表 [{}] 添加水印", sheet.getSheetName());

        } catch (Exception e) {
            log.error("为工作表 [{}] 添加水印失败", sheet.getSheetName(), e);
            throw new RuntimeException("为工作表添加水印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清空水印缓存
     */
    public static void clearCache() {
        WATERMARK_CACHE.clear();
        log.info("水印缓存已清空");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存大小
     */
    public static int getCacheSize() {
        return WATERMARK_CACHE.size();
    }

    /**
     * 创建默认水印配置
     *
     * @param watermarkText 水印文本，不能为null或空
     * @return 水印配置
     * @throws IllegalArgumentException 当水印文本无效时抛出
     */
    public static WatermarkConfig createDefaultConfig(String watermarkText) {
        return createAutoConfig(watermarkText);
    }

    /**
     * 创建自定义水印配置
     *
     * @param watermarkText 水印文本，不能为null或空
     * @param fontSize      字体大小，必须大于0
     * @param fontColor     字体颜色，不能为null
     * @param rotation      旋转角度
     * @return 水印配置
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public static WatermarkConfig createCustomConfig(String watermarkText, int fontSize, Color fontColor, float rotation) {
        return WatermarkConfig.builder()
                .text(watermarkText)
                .fontSize(fontSize)
                .fontColor(fontColor)
                .rotation(rotation)
                .build();
    }

    /**
     * 使用预设创建水印配置
     *
     * @param watermarkText 水印文本
     * @param preset        预设类型
     * @return 水印配置
     */
    public static WatermarkConfig createPresetConfig(String watermarkText, WatermarkPreset preset) {
        Objects.requireNonNull(watermarkText, "水印文本不能为null");
        Objects.requireNonNull(preset, "预设类型不能为null");

        return preset.createConfig(watermarkText);
    }

    /**
     * 根据文本长度自动选择合适的预设
     *
     * @param watermarkText 水印文本
     * @return 水印配置
     */
    public static WatermarkConfig createAutoConfig(String watermarkText) {
        Objects.requireNonNull(watermarkText, "水印文本不能为null");

        WatermarkPreset preset;
        if (watermarkText.length() <= 4) {
            preset = WatermarkPreset.SHORT_TEXT;
        } else if (watermarkText.length() > 10) {
            preset = WatermarkPreset.LONG_TEXT;
        } else {
            preset = WatermarkPreset.STANDARD;
        }

        return preset.createConfig(watermarkText);
    }

    /**
     * 创建多行水印配置
     *
     * @param multiLineText 多行水印文本（使用\n分隔）
     * @return 水印配置
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public static WatermarkConfig createMultiLineConfig(String multiLineText) {
        Objects.requireNonNull(multiLineText, "多行水印文本不能为null");

        String[] lines = multiLineText.split("\\n");

        // 根据行数选择合适的预设
        WatermarkPreset preset = WatermarkPreset.PROMINENT;
//        if (lines.length <= 2) {
//            preset = WatermarkPreset.STANDARD;
//        } else if (lines.length <= 4) {
//            preset = WatermarkPreset.LONG_TEXT;
//        } else {
//            preset = WatermarkPreset.DENSE;
//        }

        WatermarkConfig config = preset.createConfig(multiLineText);

        // 针对多行文本调整配置
        config.setLineSpacing(8); // 设置合适的行间距
        config.setMaxLinesPerWatermark(Math.max(10, lines.length)); // 确保能容纳所有行

        // 根据行数调整图片尺寸
        if (lines.length > 3) {
            config.setHeight(config.getHeight() + (lines.length - 3) * 30);
            config.setYInterval(config.getYInterval() + (lines.length - 3) * 20);
        }

        return config;
    }

    /**
     * 水印预设类型枚举
     */
    public enum WatermarkPreset {
        /**
         * 标准水印 - 适用于一般场景
         */
        STANDARD("标准水印", 36, new Color(200, 200, 200, 50), -30f, 1000, 800, 800, 600),

        /**
         * 短文本水印 - 适用于短文本，如"机密"、"内部资料"等
         */
        SHORT_TEXT("短文本水印", 48, new Color(200, 200, 200, 60), -30f, 800, 600, 700, 500),

        /**
         * 长文本水印 - 适用于较长文本，如"仅供内部使用，请勿外传"等
         */
        LONG_TEXT("长文本水印", 36, new Color(200, 200, 200, 45), -30f, 1200, 800, 1100, 700),

        /**
         * 醒目水印 - 使用红色，更加醒目，密度较高
         */
        PROMINENT("醒目水印", 24, new Color(255, 0, 0, 35), -30f, 800, 600, 600, 450),

        /**
         * 密集水印 - 水印间隔较小，密度较大
         */
        DENSE("密集水印", 32, new Color(200, 200, 200, 35), -30f, 800, 600, 500, 400);

        private final String name;
        private final int fontSize;
        private final Color fontColor;
        private final float rotation;
        private final int width;
        private final int height;
        private final int xInterval;
        private final int yInterval;

        WatermarkPreset(String name, int fontSize, Color fontColor, float rotation,
                        int width, int height, int xInterval, int yInterval) {
            this.name = name;
            this.fontSize = fontSize;
            this.fontColor = fontColor;
            this.rotation = rotation;
            this.width = width;
            this.height = height;
            this.xInterval = xInterval;
            this.yInterval = yInterval;
        }

        /**
         * 创建基于预设的水印配置
         *
         * @param text 水印文本
         * @return 水印配置
         */
        public WatermarkConfig createConfig(String text) {
            return WatermarkConfig.builder()
                    .text(text)
                    .fontSize(this.fontSize)
                    .fontColor(this.fontColor)
                    .rotation(this.rotation)
                    .width(this.width)
                    .height(this.height)
                    .xInterval(this.xInterval)
                    .yInterval(this.yInterval)
                    .build();
        }
    }

    /**
     * 水印配置类
     *
     * <p>支持Builder模式创建配置对象，提供参数验证功能。</p>
     *
     * <p>使用示例：</p>
     * <pre>
     * WatermarkConfig config = WatermarkConfig.builder()
     *     .text("机密文件")
     *     .fontSize(24)
     *     .fontColor(new Color(255, 0, 0, 80))
     *     .rotation(-45f)
     *     .build();
     * </pre>
     */
    @Setter
    @Getter
    public static class WatermarkConfig {
        // Getters and Setters
        private String text = "机密文件";
        private String fontName = "微软雅黑";
        private int fontSize = 20;
        private Color fontColor = new Color(200, 200, 200, 60); // 半透明灰色
        private float rotation = -30f; // 旋转角度
        private int width = 300;
        private int height = 200;
        private int xInterval = 200; // 水印间隔
        private int yInterval = 150;
        private int lineSpacing = 5; // 行间距，默认5像素
        private int maxLinesPerWatermark = 10; // 每个水印最大行数，默认10行

        /**
         * 创建Builder实例
         */
        public static Builder builder() {
            return new Builder();
        }

        /**
         * 验证配置参数的有效性
         *
         * @throws IllegalArgumentException 当参数无效时抛出
         */
        public void validate() {
            if (text == null || text.trim().isEmpty()) {
                throw new IllegalArgumentException("水印文本不能为空");
            }
            if (fontName == null || fontName.trim().isEmpty()) {
                throw new IllegalArgumentException("字体名称不能为空");
            }
            if (fontSize <= 0 || fontSize > 200) {
                throw new IllegalArgumentException("字体大小必须在1-200之间");
            }
            if (fontColor == null) {
                throw new IllegalArgumentException("字体颜色不能为null");
            }
            if (width <= 0 || width > 2000) {
                throw new IllegalArgumentException("图片宽度必须在1-2000之间");
            }
            if (height <= 0 || height > 2000) {
                throw new IllegalArgumentException("图片高度必须在1-2000之间");
            }
            if (xInterval <= 0) {
                throw new IllegalArgumentException("X轴间隔必须大于0");
            }
            if (yInterval <= 0) {
                throw new IllegalArgumentException("Y轴间隔必须大于0");
            }
            if (lineSpacing < 0) {
                throw new IllegalArgumentException("行间距不能为负数");
            }
            if (maxLinesPerWatermark <= 0 || maxLinesPerWatermark > 50) {
                throw new IllegalArgumentException("每个水印最大行数必须在1-50之间");
            }

            // 验证文本行数不超过限制
            String[] lines = text.split("\\n");
            if (lines.length > maxLinesPerWatermark) {
                throw new IllegalArgumentException("文本行数(" + lines.length + ")超过了最大限制(" + maxLinesPerWatermark + ")");
            }
        }

        /**
         * 生成缓存键
         */
        public String getCacheKey() {
            return String.format("%s_%s_%d_%d_%f_%d_%d_%d_%d_%d_%d",
                text, fontName, fontSize, fontColor.getRGB(), rotation,
                    width, height, xInterval, yInterval, lineSpacing, maxLinesPerWatermark);
        }

        /**
         * Builder模式构建器
         */
        public static class Builder {
            private final WatermarkConfig config = new WatermarkConfig();

            public Builder text(String text) {
                config.text = text;
                return this;
            }

            public Builder fontName(String fontName) {
                config.fontName = fontName;
                return this;
            }

            public Builder fontSize(int fontSize) {
                config.fontSize = fontSize;
                return this;
            }

            public Builder fontColor(Color fontColor) {
                config.fontColor = fontColor;
                return this;
            }

            public Builder rotation(float rotation) {
                config.rotation = rotation;
                return this;
            }

            public Builder width(int width) {
                config.width = width;
                return this;
            }

            public Builder height(int height) {
                config.height = height;
                return this;
            }

            public Builder xInterval(int xInterval) {
                config.xInterval = xInterval;
                return this;
            }

            public Builder yInterval(int yInterval) {
                config.yInterval = yInterval;
                return this;
            }

            public Builder lineSpacing(int lineSpacing) {
                config.lineSpacing = lineSpacing;
                return this;
            }

            public Builder maxLinesPerWatermark(int maxLinesPerWatermark) {
                config.maxLinesPerWatermark = maxLinesPerWatermark;
                return this;
            }

            /**
             * 构建配置对象
             *
             * @return 验证后的配置对象
             * @throws IllegalArgumentException 当参数无效时抛出
             */
            public WatermarkConfig build() {
                config.validate();
                return config;
            }
        }
    }
}
