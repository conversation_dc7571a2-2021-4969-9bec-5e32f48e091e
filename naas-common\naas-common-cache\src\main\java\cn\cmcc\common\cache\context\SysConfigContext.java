/**
 * @Title: SysConfigContext.java
 * @Package cn.cmcc.common.config
 *
 * <AUTHOR>
 * @date 2022年4月13日
 * @version V1.0
 */
package cn.cmcc.common.cache.context;

import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.cache.service.CacheService;
import cn.cmcc.common.pojo.SysConfigBO;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.spring.SpringUtils;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.ObjectUtils;

@Slf4j
public class SysConfigContext {
    private static final String JWT_SECRET_EXPIRE = "JWT_SECRET_EXPIRE";
    public static final String DB_CURRENT_SCHEMA_KEY = "dbCurrentSchema";

	 /**
     * 添加系统常量
     */
    public static void putConstant(String key, Object value) {
        if (StringUtils.isEmpty(key) || null == value) {
            return;
        }
        SysConfigBO configBo = new SysConfigBO();
        try {
			BeanUtils.copyProperties(configBo, value);
	        SpringUtils.getBean(CacheService.class).set(key, configBo, CachesEnums.CACHE_SYS_CONFIG.name());
		} catch (Exception e) {
			log.error("putConstant error:",e);
			SpringUtils.getBean(CacheService.class).set(key, value, CachesEnums.CACHE_SYS_CONFIG.name());
		}
    }

    /**
     * 删除常量
     */
    public static void deleteConstant(String key) {
    	 if (StringUtils.isEmpty(key)) {
             return;
         }
         SpringUtils.getBean(CacheService.class).del(key, CachesEnums.CACHE_SYS_CONFIG.name());
    }

    /**
     * 清空常量
     */
    public static void clearConstant() {
    	 SpringUtils.getBean(CacheService.class).clear(CachesEnums.CACHE_SYS_CONFIG.name());
    }

    /**
     * 获取当前省份编码
     */
    public static String getCurrentProvinceCode() {
    	return getConfigValue("CURRENT_PROVINCE_CODE");
    }

    /**
     * 获取当前省份名称
     */
    public static String getCurrentProvinceName() {
    	return getConfigValue("CURRENT_PROVINCE");
    }

    /**
     * 判断省份
     */
    public static boolean isCurrentProvince(String provinceCode) {
    	return provinceCode.equals(SysConfigContext.getCurrentProvinceCode());
    }

    /**
     * 获取PROJECT_BASE_URL
     */
    public static String getProjectBaseUrl() {
    	return getConfigValue("PROJECT_BASE_URL");
    }

    /**
     *
     * 获取附件绝对访问路径
     *
     * @Title: getAttementUrl
     * @param fileRelativeUrl
     * @return String
     * @throws
     */
    public static String getAttementUrl(String fileRelativeUrl) {
    	if(StringUtils.isEmpty(fileRelativeUrl)) return StringUtils.EMPTY;
    	String projectBaseUrl = getConfigValue("PROJECT_BASE_URL");
		return projectBaseUrl + fileRelativeUrl;
    }

    /**
     * 获取系统地密钥过期时间（单位：秒）
     */
    public static Long getJwtSecretExpireSec() {
        Long defaultSecs = 86400L;
        String jwtSecretExpire = getConfigValue(JWT_SECRET_EXPIRE);
        if (StringUtils.isEmpty(jwtSecretExpire)) {
            log.error("jwt密钥存在空值！常量名称：JWT_SECRET_EXPIRE，采用默认值：1天");
            putConstant(JWT_SECRET_EXPIRE, String.valueOf(defaultSecs));
            return defaultSecs;
        } else {
            try {
                return Long.valueOf(jwtSecretExpire);
            } catch (NumberFormatException e) {
                log.error("jwt密钥过期时间不是数字！常量名称：JWT_SECRET_EXPIRE，采用默认值：1天");
                putConstant(JWT_SECRET_EXPIRE, String.valueOf(defaultSecs));
                return defaultSecs;
            }
        }
    }


    /**
     * 获取系统常量
     */
    public static Map<Object, Object> getConstntsMap() {
        return SpringUtils.getBean(CacheService.class).getMap(CachesEnums.CACHE_SYS_CONFIG.name());
    }

    public static String getConfigValue(String key) {
    	Object value = null;
		try {
			value = SpringUtils.getBean(CacheService.class).get(key,CachesEnums.CACHE_SYS_CONFIG.name());
            if(value == null) return null;
			SysConfigBO sysConfig = (SysConfigBO)value;
			return sysConfig.getValue();
		} catch (Exception e) {
            log.info("获取系统参数失败:{}", key, e);
			return value == null ? StringUtils.EMPTY : value.toString();
		}
	}

    public static SysConfigBO getSysConfig(String key) {
		try {
			return (SysConfigBO)SpringUtils.getBean(CacheService.class).get(key,CachesEnums.CACHE_SYS_CONFIG.name());
		} catch (Exception e) {
			return null;
		}
	}

    public static String getConfigValueOrDefault(String key,String defaultValue) {
		try {
			SysConfigBO sysConfig = (SysConfigBO)SpringUtils.getBean(CacheService.class).get(key,CachesEnums.CACHE_SYS_CONFIG.name());
			return sysConfig.getValue();
		} catch (Exception e) {
			return defaultValue;
		}
	}


	public static boolean getBooleanValueOrDefault(String key, boolean defaultValue) {
        Object valueObject = getConfigValue(key);// NOSONAR
        if (ObjectUtils.isEmpty(valueObject)) {
            return defaultValue;
        }
        return StringUtils.strToBool(valueObject.toString());// NOSONAR
    }

	 public static Integer getIntegerValueOrDefault(String key, Integer defaultValue) {
         Object valueObject = getConfigValue(key);// NOSONAR
	        if (ObjectUtils.isEmpty(valueObject)) {
	            return defaultValue;
	        }
         return Integer.valueOf(valueObject.toString());// NOSONAR
	    }

  /**
   * 获取当前数据库模式
   */
  public static String getCurrentDBSchema() {
    return getConfigValue(DB_CURRENT_SCHEMA_KEY);
  }
}
