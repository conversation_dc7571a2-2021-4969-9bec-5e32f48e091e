/**
 * @Title: DynamicTableContext.java
 * @Package cn.cmcc.plan.enums
 * 
 * <AUTHOR>
 * @date 2022年2月16日
 * @version V1.0
 */
package cn.cmcc.common.datasource.enums;

public enum DynamicTableContext {
	INSTANCE;
    private ThreadLocal<String> suffix = new ThreadLocal<>();

    public String getSuffix() {
        return suffix.get();
    }

    public void setSuffix(String suffix) {
        this.suffix.set(suffix);
    }

    public void remove() {
    	suffix.remove();
    }
}
