package cn.cmcc.common.datasource.utils;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.bean.ObjectUtils;
import cn.cmcc.common.utils.file.FileUtils;
import cn.cmcc.common.utils.spring.SpringUtils;
import cn.cmcc.common.utils.text.CharsetKit;
import cn.hutool.core.io.FileUtil;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionUtils;
import org.postgresql.PGConnection;
import org.postgresql.copy.CopyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
/**
 * @description: pg数据库用SQL语句与CSV文件交互管理
 * <AUTHOR> @date 2025/7/11 16:11
 */
public class PostgreSQLCopyManager {

    private static final Logger logger = LoggerFactory.getLogger(PostgreSQLCopyManager.class);

    /**
     * utf8-bom 前三个字节，微软系列软件是需要有bom头才视作utf8处理的，CSV文件编码是utf8时，用execl打开是乱码，
     * 解决方案：1、导出成GBK，2、导出成utf8时，插入BOM标记位
     */
    private static byte[] UTF8_BOM = {(byte) 0xef, (byte) 0xbb, (byte) 0xbf};

    public static long copyToFile(String filePath, String tableOrQuery) throws SQLException, IOException {
        return copyToFile(filePath, tableOrQuery, false);
    }

    public static long copyToFile(String filePath, String tableOrQuery,boolean header) throws SQLException, IOException {
    	return copyToFile(filePath, tableOrQuery,header,CharsetKit.UTF_8);
    }

    public static long copyToFile(String filePath, String tableOrQuery,boolean header,String charsetName) throws SQLException, IOException {
    	return copyToFile(filePath,tableOrQuery,header,charsetName,false);
    }

    public static long copyToFile(String filePath, String tableOrQuery, boolean header, String charsetName, boolean bom) throws SQLException, IOException {
        return copyToFile(filePath, tableOrQuery, header, charsetName, false, ",", true);
    }

    public static long copyToFile(String filePath, String tableOrQuery, boolean header, String charsetName, boolean bom, String separator, boolean quote) throws SQLException, IOException {
        OutputStreamWriter outWriter = null;
        OutputStream outStream = null;
        SqlSessionFactory sqlSessionFactory = SpringUtils.getBean(SqlSessionFactory.class);
        SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
        String squote = quote ? " quote '\"' " : StringUtils.EMPTY;
        try {
        	CopyManager copyManager = sqlSession.getConnection().unwrap(PGConnection.class).getCopyAPI();
            FileUtil.touch(FileUtils.getFile(filePath));
        	outStream = FileUtils.openOutputStream(FileUtils.getFile(filePath));
        	if(CharsetKit.UTF_8.equals(charsetName) && bom) {
            	outStream.write(UTF8_BOM);
            }
            outWriter = new OutputStreamWriter(outStream, charsetName);

            String sql = String.format("COPY (%s) TO STDOUT  WITH DELIMITER '%s' %s csv ", tableOrQuery, separator, squote);
            if (header) {
                sql += " header";
            }
            logger.info("copy in sql:【{}】", sql);
            return copyManager.copyOut(sql, outWriter);
        } finally {
            if (outWriter != null) {
                try {
                    outWriter.close();
                } catch (IOException e) {
                	logger.error("copyToFile outWriter close error:",e);
                }
            }
            if (outStream != null) {
                try {
                    outStream.close();
                } catch (IOException e) {
                	logger.error("copyToFile outStream close error:",e);
                }
            }
            SqlSessionUtils.closeSqlSession(sqlSession,sqlSessionFactory);
        }
    }

    public static long copyInFromFile(String filePath, String tableName) throws SQLException, IOException {
    	return copyInFromFile(filePath, tableName,false);
    }
    /**
      * <AUTHOR> @date 2025/7/11 15:57
      * @描述 从csv文件复制数据到表-对列名使用引号
      * @param filePath csv文件路径
      * @param tableName 表名(只包含表名，不含列名)
      * @param columnQuote 是否对单列名使用引号。说明：以数字开头的列不加双引号无法识别，列区别大小写时必须加引号。eg: 若传入true,则得到的列为【"site_uuid","3DMIMO"】
     * @param cols 列名集合
      */
    public static long copyInFromFile(String filePath, String tableName,boolean columnQuote,Collection<String> cols) throws Exception {
        String tableNameTmp = tableName;
        if(columnQuote){
            String colStr = StringUtils.join(cols,',',true);
            tableNameTmp = tableNameTmp + '(' + colStr + ')';
        }
        return copyInFromFile(filePath, tableNameTmp,true);
    }
    /**
      * <AUTHOR> @date 2025/7/11 16:31
      * @description csv文件入库，是否包含表头
      * @param filePath 文件路径
     * @param tableName 表名
     * @param header csv文件是否包含表头，是则为true,入库时忽略首行
      */
    public static long copyInFromFile(String filePath, String tableName,boolean header) throws SQLException, IOException {
    	return copyInFromFile(filePath,tableName,",",header);
    }

    public static long copyInFromFile(String filePath, String tableName,String separator,boolean header) throws SQLException, IOException {
    	return copyInFromFile(filePath,tableName,separator,header,CharsetKit.UTF_8,new ArrayList<>());
    }

    /**
     * <AUTHOR>  2025/4/3 09:17
     * @description: 将csv文件内容复制到库（过滤表字段）
     * @param filePath 文件绝对路径
     * @param tableName 表名
     * @param cols 入库列集合
     */
    public static long copyInFromFile(String filePath, String tableName, Collection<String> cols) throws SQLException, IOException {
        return copyInFromFile(filePath,tableName,",",true,CharsetKit.UTF_8,cols);
    }

    public static long copyInFromFile(String filePath, String tableName,String separator,boolean header,String fileEncode) throws Exception {
        return copyInFromFile(filePath,tableName,separator,header,fileEncode,new ArrayList<>());
    }
    /**
     * <AUTHOR>
     * @description: 将CSV文件复制库
     */
    public static long copyInFromFile(String filePath, String tableName, String separator, boolean header, String fileEncode, Collection<String> cols) throws SQLException, IOException {
        FileInputStream fileInputStream = null;
        BOMInputStream bomIn = null;
        boolean originalUtf8 = true;
        SqlSessionFactory sqlSessionFactory = SpringUtils.getBean(SqlSessionFactory.class);
    	SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
        try {
        	String database = sqlSession.getConnection().getMetaData().getURL();
        	CopyManager copyManager = sqlSession.getConnection().unwrap(PGConnection.class).getCopyAPI();
        	File file = FileUtil.file(filePath);
        	if (!fileEncode.toUpperCase().replace("-", "").equals("UTF8")) {
            	FileUtil.convertCharset(file, Charset.forName(fileEncode), Charset.forName(CharsetKit.UTF_8));
            	originalUtf8 = false;
			}
        	fileInputStream = FileUtils.openInputStream(FileUtils.getFile(file.getAbsolutePath()));
        	if(originalUtf8) {
            	bomIn = new BOMInputStream(fileInputStream);
            	if(bomIn.hasBOM()){
            		logger.info("{} is utf8-BOM file",file.getAbsolutePath());
            		bomIn.skip(bomIn.getBOM().length());
            	}
        	}

            String sql = "COPY " + tableName + " FROM STDIN DELIMITER '" + separator + "' quote '\"' csv ";
            if (ObjectUtils.isNotEmpty(cols)) {
                sql = "COPY " + tableName+ "("+ StringUtils.join(cols, ',') +")" + " FROM STDIN DELIMITER '" + separator + "' quote '\"' csv ";
            }
            if(header) {
            	sql+=" header";
            }
            logger.info("copy in database:【{}】,sql:【{}】",database,sql);
            long startTime = System.currentTimeMillis();
            long result = copyManager.copyIn(sql, originalUtf8 ? bomIn : fileInputStream);
            long costTime = System.currentTimeMillis() - startTime;
            logger.info("copy in result:【file:{},lineCount:{},time:{}】", file, result, (costTime / 1000) + "秒");
            return result;
        } finally {
        	if(bomIn != null) {
        		 try {
        			 bomIn.close();
                 } catch (IOException e) {
                	 logger.error("copyInFromFile bomIn close error:",e);
                 }
        	}
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                	logger.error("copyInFromFile fileInputStream close error:",e);
                }
            }
            SqlSessionUtils.closeSqlSession(sqlSession,sqlSessionFactory);
        }
    }

    public static long copyInFromInputStream(InputStream inputStream, String tableName,String separator,boolean header) throws Exception, IOException{
    	SqlSessionFactory sqlSessionFactory = SpringUtils.getBean(SqlSessionFactory.class);
    	SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
        try {
        	String database = sqlSession.getConnection().getMetaData().getURL();
        	CopyManager copyManager = sqlSession.getConnection().unwrap(PGConnection.class).getCopyAPI();
            String sql = "COPY " + tableName + " FROM STDIN DELIMITER '" + separator + "' quote '\"' csv ";
            if(header) {
            	sql+=" header";
            }
            logger.info("copy in database:【{}】,sql:【{}】",database,sql);
            long result = copyManager.copyIn(sql, inputStream);
            logger.info("copy in result:【{}】",result);
            return result;
        } finally {
            SqlSessionUtils.closeSqlSession(sqlSession,sqlSessionFactory);
        }
    }

    public static void copyFromFileBySql(String filePath, String sql) throws SQLException, IOException {
        FileInputStream fileInputStream = null;
        SqlSessionFactory sqlSessionFactory = SpringUtils.getBean(SqlSessionFactory.class);
    	SqlSession sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
        try {
        	CopyManager copyManager = sqlSession.getConnection().unwrap(PGConnection.class).getCopyAPI();
            fileInputStream = FileUtils.openInputStream(FileUtils.getFile(filePath));
            copyManager.copyIn(sql, fileInputStream);

        }catch(Exception e){
        	logger.error("copyFromFileBySql copyIn error:",e);
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                	logger.error("copyFromFileBySql fileInputStream close error:",e);
                }
            }
            SqlSessionUtils.closeSqlSession(sqlSession,sqlSessionFactory);
        }
    }
}
