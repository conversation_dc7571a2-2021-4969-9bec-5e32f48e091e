package cn.cmcc.auth.service.cas;

import static cn.cmcc.auth.config.CasConfig.*;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.common.cache.config.CacheConfig;
import cn.cmcc.common.cache.context.SysConfigContext;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 河北实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Slf4j
public class CasServiceHEImpl extends CacheConfig implements ICasService {

    private final CasConfig config;

    /**
     * 查询用户操作名称
     */
    private final String queryUserOP;

    /**
     * 查询用户XML
     */
    private final String queryUserXML;

    public CasServiceHEImpl(CasConfig config) {
        this.config = config;
        JSONObject otherParameters = JSON.parseObject(config.getOtherParameters());
        this.queryUserOP = otherParameters.getString("queryUserOP");
        this.queryUserXML = new String(Base64.getDecoder().decode(otherParameters.getString("queryUserXML")));
        try {
            config.setCasLoginUrl(String.format("%s%s", config.getCasServerUrl(), config.getCasServerLoginSuffix()));
            log.info("casLoginUrl: {}", config.getCasLoginUrl());
            config.setCasValidateUrl(String.format("%s%s", config.getCasServerUrl(), config.getCasServerValidateSuffix()));
            log.info("casValidateUrl: {}", config.getCasValidateUrl());
            config.setExpire(SysConfigContext.getJwtSecretExpireSec());
            log.info("expire: {}", config.getExpire());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public AjaxResult login(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>(16);
        try {
            // 已经有登录信息
            if (validToken(request)) {
                data.put(REDIRECT, ROOT_SUFFIX);
                return AjaxResult.success(data);
            }

            String ticket = getTicket(request, config.getCasTicketParameter());
            if (StringUtils.isEmpty(ticket)) {
                log.info("Ticket is empty, jumps to the CAS login page.");
                data.put(REDIRECT, config.getCasLoginUrl());
            } else {
                log.info("Ticket: " + ticket);
                String xml = getResponse(config.getCasValidateUrl(), this.queryUserOP, this.queryUserXML.replace("${ticket}", ticket));
                log.info("Response:\n{}", xml);

                // accId, name, employeeNumber, employeeType, workOrg, telephoneNumber, mobile, email
                String returnXML = config.getTextForElement(xml, "qryUserByTicketReturn", false), account;
                if (StringUtils.isNotEmpty(returnXML) &&
                        StringUtils.isNotEmpty(account = config.getTextForElement(returnXML, "accId", false))) {
                    String token = (String) config.getLoginService().ssoLogin(account).get(SecurityConstants.TOKEN);
                    data.put(REDIRECT, ROOT_SUFFIX);
                    data.put(SecurityConstants.TOKEN, token);
                    data.put(USER_NAME, account);

                    // 票据-TOKEN
                    config.getCacheService().set(TICKET_PREFIX + ticket, token, config.getExpire(), TimeUnit.SECONDS, CACHE_TYPE);
                } else {
                    data.put(REDIRECT, config.getCasLoginUrl());
                    data.put("msg", xml);
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);

            data.put(REDIRECT, config.getCasLoginUrl());
            data.put("msg", ex.getMessage());
        }
        return AjaxResult.success(data);
    }

    @Override
    public void logout(String ticket) {
        try {
            String ticketKey = TICKET_PREFIX + ticket;
            Object token;
            if ((token = config.getCacheService().get(ticketKey, CACHE_TYPE)) == null) {
                log.error("Ticket: {}, token: {}.", ticket, null);
                return;
            }

            // 缓存时间和 TOKEN 过期时间一样，如果缓存中存在，就说明没有过期
            config.getCacheService().del(ticketKey, CACHE_TYPE);
            config.getCacheService().set(LOGOUT_PREFIX + token, ticketKey, config.getExpire(), TimeUnit.SECONDS, CACHE_TYPE);

            config.getLoginService().logout((String) token);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public boolean tokenExpired(String token) {
        return config.getCacheService().get(LOGOUT_PREFIX + token, CACHE_TYPE) != null;
    }

    /**
     * 获取响应
     *
     * @param url           URL
     * @param operationName 操作名称
     * @param xml           XML
     * @return 响应
     */
    public String getResponse(String url, String operationName, String xml) throws Exception {
        log.info("URL: {}, operation name: {}.", StringUtils.replace(url, "[\r\n]", ""), operationName);

        CloseableHttpResponse response = null;
        try (CloseableHttpClient client = HttpClientBuilder.create().build()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(new StringEntity(xml));
            response = client.execute(httpPost);
            StatusLine statusLine = response.getStatusLine();
            if (statusLine.getStatusCode() != 200) {
                throw new RuntimeException(String.format("Request failed, code: [%s], reason: [%s]", statusLine.getStatusCode(), statusLine.getReasonPhrase()));
            }
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        } finally {
            if (response != null) response.close();
        }
    }

}
