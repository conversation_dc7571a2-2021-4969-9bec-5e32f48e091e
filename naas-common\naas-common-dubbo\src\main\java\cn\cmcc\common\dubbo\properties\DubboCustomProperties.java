package cn.cmcc.common.dubbo.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import cn.cmcc.common.dubbo.enums.DubboLogEnum;
import lombok.Data;

/**
 * 自定义配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "dubbo.custom")
public class DubboCustomProperties {

    private Boolean requestLog;

    private DubboLogEnum logLevel;

}
