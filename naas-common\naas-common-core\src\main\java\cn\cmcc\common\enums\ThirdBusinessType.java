/**
 * 交互业务类型
 *
 * @Title: ThirdBusinessType.java
 * <AUTHOR>
 * @date 2022年6月20日
 * @version V1.0
 */
package cn.cmcc.common.enums;

import cn.cmcc.common.context.BusinessTransformerContext;
import lombok.Getter;

@Getter
public enum ThirdBusinessType {

	DY("DY","单验"),
	ZTY("ZTY","天线测量"),
	ZTY_ROOM("ZTY_ROOM","机房测量"),
	PM("PM","性能"),
	TA("TA","共站"),
	JAM("JAM","干扰"),
	GJ("GJ","告警"),
	HZSB("HZSB","宏站识别"),
	CS("CS","参数"),
	DD("DD","电调"),
	GD("GD","光调"),
	TRANSFER("TRANSFER","贵州传输"),
	ER("ER","工程预约"),
	IR("IR","综资推送"),
	CER("CER","取消工程预约"),

	CRS_I("CRS_I","单工单批量获取省端资管资源ID"),
    CRS_T("CRS_T", "创建或更新无线资源Get Token"),
	CRS("CRS","创建或更新无线资源"),
	NRMS("NRMS","资管动环报文发送"),

	ZG_SHJH("ZG_SHJH","资管施工交互"),
	CRS_CU("CRS_CU","录入无线资源数据采集更新数据"),
	CRE("CRE","推送EMOS"),
	CRZ("CRZ","传输调度"),
	CRS_CR("CRS_CR","成本、零工项目的工程施工结果数据"),
	CRS_CN("CRS_CN","施工派发"),
	CRS_CCN("CRS_CCN","施工取消通知"),

	/* ******************* PMS ******************* */
	PMS_DAR("PMS_DAR","设计审批"),
	PMS_REPLY("PMS_REPLY","立项阶段-建站需求工单批复" ),
	PMS_IPUSH("PMS_IPUSH","立项阶段-项目立项信息推送"),
	PMS_DESIGN_INFO_PARSE("PMS_DESIGN_INFO", "设计阶段-设计信息推送"),
	PMS_CONSTRUCTION_INFO_PARSE("PMS_CONSTRUCTION_INFO", "施工阶段-施工信息推送"),
//	PMS_REPLY_INFO("PMS_REPLY_INFO","批复阶段-批复信息推送"),
//	PMS_CANCEL_NOTICE("PMS_CANCEL_NOTICE","施工阶段-施工取消通知"),
//	PMS_CONSTRUCTION_CANCEL_NOTICE("PMS_CONSTRUCTION_CANCEL_NOTICE","施工阶段-施工取消通知"),

	/* ******************* NAAS平台微服务能力接口 ******************* */
	PLATE_SITE_RANGE("PLATE_SITE_RANGE","基于经纬度的详细信息查询"),

	/* ******************* 中国移动网络数据共享平台-用户行为分析统计数据接口 ******************* */
	UBR_CALL("UBR_CALL","网络数据共享平台系统访问调用统计数据上报"),
	UBR_LOGIN("UBR_LOGIN","网络数据共享平台用户登录详情数据上报"),

	/* ******************* 其他 ******************* */
	KPI("KPI","告警/性能/参数"),
	ZDKZ("ZDKZ","自动开站"),
	PSZTJZ("PSZTJZ","价格评估"),
	ZCGL("ZCGL","资产关联"),
	T_CREATE_ORDER("T_CREATE_ORDER","三方平台创建工单"),
	AI_CHECK("AI_CHECK","AI验收(工艺验收)"),
	TYCTI("TYCTI","通用调用三方接口"),
	HD_ICOS("HD_ICOS","黑点ICOS推送"),
	HD_FX_ICOS("HD_FX_ICOS","黑点分析推送ICOS"),
	HD_PG_ICOS("HD_PG_ICOS","黑点评估推送ICOS"),
	ICOS_HD_ICOS("ICOS_HD_ICOS","接收ICOS推送根因结论到黑点解决"),
	JWJG("JWJG","入网交维通知"),
	UNIFY_TODO("UNIFY_TODO","代办通知"),
	ARAN("ARAN","ARAN"),
	ARAN_SJGH("ARAN_SJGH","ARAN_SJGH"),
	ARAN_SJJZ("ARAN_SJJZ","ARAN_SJJZ"),
	ARAN_DZYZ("ARAN_DZYZ", "ARAN_DZYZ"),
	ARAN_SYNC_STATUS("ARAN_SYNC_STATUS","ARAN同步规划工单状态"),
	ARAN_SYNC_DATA("ARAN_SYNC_DATA","ARAN同步规划工单数据"),
	DTN("DTN","DTN仿真"),
//	DTN_SRE("siteRationalityEvaluation","DTN规划站合理性评估"),
	BBU_IDENTIFY("BBU_IDENTIFY","BBUai识别"),
	LXPF("LXPF", "立项批复"),
	KCBG("KCBG", "勘察报告"),
	MESSAGE_GZ("MESSAGE_GZ", "贵州短信平台接口"),
    VERIFY_DATA_SC("VERIFY_DATA_SC", "四川集中规划平台校验接口"),
    DAIWEI("DAIWEI", "代维"),
    TZSB("TZSB", "图纸识别"),
    OCR_RES("JLAttachmentOCR", "OCR识别结果"),
	PRIORITY_RATE("priorityRate","优先级多维评分"),
	SITE_EVERY_COUNT("EveryStageSiteCount","新站入网各阶段站点数量信息查询"),
	TEMP_TRANSFER_BACKOFF("tempStopTransferStationBackOff","基站临停归档后转基站退网"),
	NM_DTN_SRE("NM_DTN_SREvaluation","内蒙DTN规划站可行性评估"),
	PMS_STATION_INFO("PMS_STATION_INFO", "PMS项目立项信息"),
	PMS_4_1("PMS4-1","设计完成后发送设计信息给无线工作台"),
	PMS_7_1("PMS7-1","施工完成后发送施工方案信息给无线工作台"),
	PMS_5_1("PMS5-1","向总部PMS发送无线审核结果"),
	PMS_DESIGN_APPROVAL("PMS_DESIGN_APPROVAL","PMS设计批复结果" ),
	JT_DATA_ASYNC_PROVINCE("JTDataAsyncProvince","集团数据同步省端"),
	BJ_SMS_NOTICE("BJ_SMS_NOTICE","北京短信平台接口"),
	AUDIT_PERIOD("AUDIT_PERIOD", "集团工期审核"),
	UNKNOWE("UNKNOWE", "未知");


	private final String code;
	private final String name;

	ThirdBusinessType(String code,String name){
		this.code = code;
		this.name = name;
	}

	public String getCode() {
        return BusinessTransformerContext.LnChangeInterfaces.getBusinessCodeWithContext(code);
	}
}
