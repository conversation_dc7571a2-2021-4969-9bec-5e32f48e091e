package cn.cmcc.common.utils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import cn.cmcc.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * spel 工具类
 *
 * <AUTHOR>
 * @date 2022年12月27日
 * @version V1.0
 */

@Slf4j
public class SpelUtils {

	/**
     * 获取被拦截方法参数名列表(使用Spring支持类库)
     */
    private static LocalVariableTableParameterNameDiscoverer pnd = new LocalVariableTableParameterNameDiscoverer();

    
    /**
     * 使用SPEL进行key的解析
     */
    private static ExpressionParser parser = new SpelExpressionParser();
    
    public static Map<String,Expression> expressionMap = new ConcurrentHashMap<>();

    public static <T> T parse(String expression, Class<T> returnType) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setBeanResolver(new BeanFactoryResolver(SpringUtils.getBeanFactory()));
        return expressionMap.computeIfAbsent(expression, parser::parseExpression).getValue(context, returnType);
    }
    
    public static <T> T parse(String expression,Map<String, Object> param, Class<T> returnType) {
    	StandardEvaluationContext context = new StandardEvaluationContext();

        //把方法参数放入SPEL上下文中
    	for(Map.Entry<String, Object> entry: param.entrySet()) {
    		 context.setVariable(entry.getKey(), entry.getValue());
    	}
    	
    	return expressionMap.computeIfAbsent(expression, parser::parseExpression)
    			.getValue(context, returnType);
    }
    
    public static boolean check(String expression,Map<String, Object> param) {
    	return parse(expression,param,Boolean.class);
    }

    /**
     * 解析el表达式,el表达式需要用#{}包裹,#whId+'_'+#sku
     *
     * @param spel
     * @param method
     * @param args
     * @return String
     * @throws
     * @Title: parse
     */
    public static String parse(String spel, Method method, Object[] args) {
        String[] paraNameArr = pnd.getParameterNames(method);

        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();

        //把方法参数放入SPEL上下文中
        if (paraNameArr != null) {
            for (int i = 0; i < paraNameArr.length; i++) {
                context.setVariable(paraNameArr[i], args[i]);
            }
        }
        return expressionMap.computeIfAbsent(spel, parser::parseExpression)
        		.getValue(context, String.class);
    }

    /**
     * 支持 #p0 参数索引的表达式解析
     *
     * @param rootObject 根对象,method 所在的对象
     * @param spel       表达式
     * @param method     ，目标方法
     * @param args       方法入参
     * @return 解析后的字符串
     */
    public static String parse(Object rootObject, String spel, Method method, Object[] args) {
        String[] paraNameArr = pnd.getParameterNames(method);
        //SPEL上下文
        StandardEvaluationContext context = new MethodBasedEvaluationContext(rootObject, method, args, pnd);
        //把方法参数放入SPEL上下文中
        if (paraNameArr != null) {
            for (int i = 0; i < paraNameArr.length; i++) {
                context.setVariable(paraNameArr[i], args[i]);
            }
        }
        return expressionMap.computeIfAbsent(spel, parser::parseExpression).getValue(context, String.class);
    }
    
    public static void main(String[] args) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("cb", "12134");
		param.put("xx", 2);
		try {
			//System.out.println(SpelUtils.check("#xx >= 100 && #cb == '12134'",param ));
			//System.out.println(SpelUtils.check("{1,2,3}.contains(#xx)",param ));
			System.out.println(SpelUtils.parse("#xx == 1 ? 'a' : #xx == 2 ? 'b' : 'c'",param,String.class));
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
	}
}
