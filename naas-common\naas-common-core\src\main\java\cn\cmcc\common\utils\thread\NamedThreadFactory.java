package cn.cmcc.common.utils.thread;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自定义命名线程
 * 
 * @version v1.0
 * <AUTHOR>
 * @date 2023年5月9日上午10:35:58
 */
public class NamedThreadFactory implements ThreadFactory {

	private final AtomicInteger sequence = new AtomicInteger(1);

	private final String prefix;

	public NamedThreadFactory(String prefix) {
		this.prefix = prefix;
	}

	@Override
	public Thread newThread(Runnable r) {
		Thread thread = new Thread(r);
		int seq = sequence.getAndIncrement();
		thread.setName(prefix + (seq > 1 ? "-" + seq : ""));
		thread.setDaemon(true);
		return thread;
	}
}
