/**
 * @Title: RemoteFieldConfigService.java
 * @Package cn.cmcc.system.api.service
 *
 * <AUTHOR>
 * @date 2022年4月14日
 * @version V1.0
 */
package cn.cmcc.system.api.service;

import java.util.List;
import java.util.Map;

import cn.cmcc.common.web.domain.AjaxResult;
import cn.cmcc.system.api.pojo.dto.StageFeildConfigDto;
import org.springframework.beans.PropertyValues;

public interface RemoteFieldConfigService {

	/**
	 *
	 * 查询业务字段配置信息
	 *
	 * @Title: selectFieldConfigList
	 * @param t5gStageFeildConfig
	 * @return List<StageFeildConfigDto>
	 * @throws
	 */
	 public List<StageFeildConfigDto> selectFieldConfigList(StageFeildConfigDto t5gStageFeildConfig);

	 /**
	  *
	  * 按业务类型，节点id，获取字段配置信息
	  *
	  * @Title: getNodeFieldGroupList
	  * @param businessType
	  * @param nodeId
	  * @return Map<String,List<StageFeildConfigDto>>
	  * @throws
	  */
	 public Map<String, List<StageFeildConfigDto>> getNodeFieldGroupList(String businessType,String nodeId);

	 /**
	  *
	  * 按业务类型，节点id，获取必填字段配置信息
	  *
	  * @Title: qryStageFeildConfigRequired
	  * @param businessType
	  * @param nodeId
	  * @return Map<String,List<StageFeildConfigDto>>
	  * @throws
	  */
	 public Map<String, List<StageFeildConfigDto>> qryStageFeildConfigRequired(String businessType,String nodeId);

	public List<StageFeildConfigDto> list(String businessType);

	/**
	 * 刷新字段配置缓存
	 */
	public void refreshCache();
}
