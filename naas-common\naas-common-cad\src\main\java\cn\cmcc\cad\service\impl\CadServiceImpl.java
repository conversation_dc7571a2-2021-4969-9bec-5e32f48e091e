package cn.cmcc.cad.service.impl;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.aspose.cad.fileformats.cad.CadBlockDictionary;
import com.aspose.cad.fileformats.cad.CadImage;
import com.aspose.cad.fileformats.cad.CadLayersList;
import com.aspose.cad.fileformats.cad.cadobjects.Cad2DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.Cad3DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.CadArc;
import com.aspose.cad.fileformats.cad.cadobjects.CadBaseEntity;
import com.aspose.cad.fileformats.cad.cadobjects.CadBlockEntity;
import com.aspose.cad.fileformats.cad.cadobjects.CadCircle;
import com.aspose.cad.fileformats.cad.cadobjects.CadEllipse;
import com.aspose.cad.fileformats.cad.cadobjects.CadInsertObject;
import com.aspose.cad.fileformats.cad.cadobjects.CadLeader;
import com.aspose.cad.fileformats.cad.cadobjects.CadLine;
import com.aspose.cad.fileformats.cad.cadobjects.CadLwPolyline;
import com.aspose.cad.fileformats.cad.cadobjects.CadMText;
import com.aspose.cad.fileformats.cad.cadobjects.CadText;
import com.aspose.cad.fileformats.cad.cadobjects.acadtable.CadTableEntity;
import com.aspose.cad.fileformats.cad.cadobjects.attentities.CadAttDef;
import com.aspose.cad.fileformats.cad.cadobjects.attentities.CadAttrib;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadEdgeBoundaryPath;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadHatch;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadHatchBoundaryPathContainer;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadPolylineBoundaryPath;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.ICadBoundaryPath;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.Point2D;
import com.aspose.cad.fileformats.cad.cadobjects.polylines.CadPolyline;
import com.aspose.cad.fileformats.cad.cadobjects.vertices.Cad2DVertex;
import com.aspose.cad.fileformats.cad.cadtables.CadLayerTable;
import com.aspose.cad.system.collections.Generic.Dictionary.KeyCollection;

import cn.cmcc.cad.constant.CadConstants;
import cn.cmcc.cad.entity.XhxkCadArc;
import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadBlockEntity;
import cn.cmcc.cad.entity.XhxkCadCircle;
import cn.cmcc.cad.entity.XhxkCadEllipse;
import cn.cmcc.cad.entity.XhxkCadImage;
import cn.cmcc.cad.entity.XhxkCadInsertObject;
import cn.cmcc.cad.entity.XhxkCadLine;
import cn.cmcc.cad.entity.XhxkCadLwPolyline;
import cn.cmcc.cad.entity.XhxkCadPoint;
import cn.cmcc.cad.entity.XhxkCadText;
import cn.cmcc.cad.service.ICadService;
import cn.cmcc.common.utils.file.FileUtils;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.SerializeUtil;

@Service
public class CadServiceImpl implements ICadService {
	public static final Logger logger = LoggerFactory.getLogger(CadServiceImpl.class);

	@Override
	public XhxkCadImage loadCadImage(String file) throws Exception {
		return loadCadImage(file, CadConstants.CAD_UNIT_TYPE_MM, CadConstants.CAD_DEFAULT_SCALE);
	}

	@Override
	public XhxkCadImage transformCadImage(CadImage image) {
		long start = System.currentTimeMillis();
		List<XhxkCadBlockEntity> blockEntities = transformCadBlockEntity(image);
		long end = System.currentTimeMillis();
		logger.info("transformCadBlockEntity - time: " + (end - start) + " ms size:" + blockEntities.size());
		start = System.currentTimeMillis();
		List<XhxkCadBaseEntity> entities = transformCadBaseEntity(image, blockEntities);
		end = System.currentTimeMillis();
		logger.info("transformCadBaseEntity - time: " + (end - start) + " ms size:" + entities.size());
		start = System.currentTimeMillis();
		XhxkCadImage cadImage = new XhxkCadImage(image.k, entities, blockEntities, image.getWidth(), image.getHeight(),
				transform3dPoint(image.getMinPoint()), transform3dPoint(image.getMaxPoint()));
		end = System.currentTimeMillis();
		logger.info("cadImage - time: " + (end - start) + " ms");
		start = System.currentTimeMillis();
		return cadImage;
	}

	public XhxkCadImage loadCadImage(String file, String unitType1, int scale1) throws Exception {
		long start = System.currentTimeMillis();
		XhxkCadImage cadImage = null;
		if (file.endsWith(CadConstants.CAD_SUFFIX)) {
			cadImage = loadXhxkToCadImage(file);
		} else {
			CadImage image = null;
			try {
				image = (CadImage) CadImage.load(file);
				List<XhxkCadBlockEntity> blockEntities = transformCadBlockEntity(image);
				List<XhxkCadBaseEntity> entities = transformCadBaseEntity(image, blockEntities);
				cadImage = new XhxkCadImage(file, entities, blockEntities, image.getWidth(), image.getHeight(),
						transform3dPoint(image.getMinPoint()), transform3dPoint(image.getMaxPoint()));
			} catch (Exception e) {
				throw e;
			} finally {
				try {
					if(image != null) {
						image.close();
						image = null;
					}
				} catch (Exception e) {
					logger.error(e.getMessage(),e);
				}
			}
		}
		long end = System.currentTimeMillis();
		logger.info("loadCadImage_ time:" + (end - start) + "ms file:" + file);
		return cadImage;
	}

	public boolean saveCadImageToXhxk(XhxkCadImage cadImage, String xhxkFile) {
		long start = System.currentTimeMillis();
		boolean status = false;

		File file = FileUtils.getFile(xhxkFile.substring(0, xhxkFile.lastIndexOf("/")));
		if (!file.exists()) {
			file.mkdirs();
		}
		try(ByteArrayOutputStream baos = new ByteArrayOutputStream();
				ObjectOutputStream oos = new ObjectOutputStream(baos);){
			oos.writeObject(cadImage);
			byte[] data = baos.toByteArray();

			// new一个文件对象用来保存图片，默认保存当前工程根目录
			File imageFile = FileUtils.getFile(xhxkFile);
			// 创建输出流
			try(FileOutputStream fileOutputStream = new FileOutputStream(imageFile)){
				// 写入数据
				fileOutputStream.write(data);
				// 关闭输出流
				status = true;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		long end = System.currentTimeMillis();
		logger.info("save CadImage to xhxk time:" + (end - start) + "ms");
		return status;
	}

	public XhxkCadImage loadXhxkToCadImage(String xhxkFile) {
		long start = System.currentTimeMillis();
		try(FileInputStream fis = new FileInputStream(FileUtils.getFile(xhxkFile));
				BufferedInputStream bis = new BufferedInputStream(fis);
				ByteArrayOutputStream baos = new ByteArrayOutputStream()){
			int c = bis.read();// 读取bis流中的下一个字节
			while (c != -1) {
				baos.write(c);
				c = bis.read();
			}
			byte[] data = baos.toByteArray();

			return SerializeUtil.deserialize(data, XhxkCadImage.class);

			/*
			try(ByteArrayInputStream bais = new ByteArrayInputStream(data);
					ObjectInputStream ois = new ObjectInputStream(bais)){
				XhxkCadImage image = (XhxkCadImage) ois.readObject();
				long end = System.currentTimeMillis();
				logger.info("read xhxk to CadImage time:" + (end - start) + "ms");
				return image;
			}*/

		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return null;
		}
	}

	public XhxkCadImage deepCopy(XhxkCadImage cadImage) {
		long start = System.currentTimeMillis();
		XhxkCadImage image = cadImage.deepCopy();
		long end = System.currentTimeMillis();
		logger.info("deepCopy CadImage time:" + (end - start) + "ms");
		return image;
	}

	public XhxkCadImage copy(XhxkCadImage cadImage) {
		return deepCopy(cadImage);
	}

	public List<XhxkCadBaseEntity> transformCadBaseEntity(CadImage image, List<XhxkCadBlockEntity> blockEntities) {
		CadBaseEntity[] entities = image.getEntities();
		int poolSize = entities.length;
		if (poolSize > 10) {
			poolSize = 10;
		}
		Map<String, Boolean> hideLayers = new HashMap<String, Boolean>();
		CadLayersList cadLayersList = image.getLayers();
	    for (int i = 0; i < cadLayersList.size(); i++) {
	    	CadLayerTable layer = (CadLayerTable)cadLayersList.get_Item(i);
	    	if(layer.getName().equals("xref-07学生宿舍_t3$0$DOTE")){
	    		layer.getName();
	    		System.out.println(layer.getFlags());
	    	}
	    	if(layer.getFlags()==1){
	    		hideLayers.put(layer.getName(), true);
	    	}
	    }
		ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
		List<Future<XhxkCadBaseEntity>> futureList = new ArrayList<Future<XhxkCadBaseEntity>>();
		Cad3DPoint minPoint = image.getMinPoint();
		Cad3DPoint maxPoint = image.getMaxPoint();
		List<XhxkCadBaseEntity> list = new LinkedList<XhxkCadBaseEntity>();
		for (CadBaseEntity entity : entities) {
			String layer = entity.getLayerName();
			if(hideLayers.containsKey(layer)) {
				continue;
			}
			Future<XhxkCadBaseEntity> future = executorService.submit(new Callable<XhxkCadBaseEntity>() {
				@Override
				public XhxkCadBaseEntity call() {
					XhxkCadBaseEntity ent = transformCadBaseEntity(entity, blockEntities, minPoint,maxPoint);
					if(ent==null){
						return null;
					}
					try {
						XhxkCadPoint insertionPoint = ent.getPosition();
						if(insertionPoint==null) {
							return null;
						}
						if (!( (insertionPoint.getX()>=minPoint.getX())&& (insertionPoint.getX()<=maxPoint.getX())
						  && (insertionPoint.getY()>=minPoint.getY())&& (insertionPoint.getY()<=maxPoint.getY()))) {
							return null;
						}
					} catch (Exception e) {
						logger.error(e.getMessage(),e);
					}
					return ent;
				}
			});
			futureList.add(future);
		}
		for (Future<XhxkCadBaseEntity> f : futureList) {
			try {
				XhxkCadBaseEntity ent = f.get();
				if ((ent != null) && (ent.getPosition() != null)) {
					list.add(ent);
				}
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				logger.error(e.getMessage(),e);
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
		}
		executorService.shutdown();
		return list;
	}

	public List<XhxkCadBlockEntity> transformCadBlockEntity(CadImage image) {
		CadBlockDictionary block = image.getBlockEntities();
		Cad3DPoint minPoint = image.getMinPoint();
		Cad3DPoint maxPoint = image.getMaxPoint();
		List<XhxkCadBlockEntity> blockEntities = new LinkedList<XhxkCadBlockEntity>();
		KeyCollection<String, CadBlockEntity> keys = block.getKeys();
		int poolSize = keys.size();
		if (poolSize > 10) {
			poolSize = 10;
		}
		ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
		List<Future<XhxkCadBlockEntity>> futureList = new ArrayList<Future<XhxkCadBlockEntity>>();
		for (String blockKey : keys) {
			Future<XhxkCadBlockEntity> future = executorService.submit(new Callable<XhxkCadBlockEntity>() {
				@Override
				public XhxkCadBlockEntity call() {
					long start = System.currentTimeMillis();
					List<XhxkCadBaseEntity> indoorCadEntities = new LinkedList<XhxkCadBaseEntity>();
					CadBlockEntity entity = block.get_Item(blockKey);

					CadBaseEntity[] entities = entity.getEntities();
					for (CadBaseEntity baseEntity : entities) {
						if (!(baseEntity instanceof CadInsertObject)) {
							XhxkCadBaseEntity ent = transformCadBaseEntity(baseEntity, null, minPoint,maxPoint);
							if ((ent != null) && (ent.getPosition() != null)) {
								ent.setPosition(new XhxkCadPoint(ent.getPosition().getX(), ent.getPosition().getY()));
								List<XhxkCadPoint> points = ent.getPoints();
								List<XhxkCadPoint> resultPoints = new LinkedList<XhxkCadPoint>();
								for (XhxkCadPoint point : points) {
									resultPoints.add(new XhxkCadPoint(point.getX(), point.getY()));
								}
								ent.setPoints(resultPoints);
								indoorCadEntities.add(ent);
							}
						}
						if (baseEntity instanceof CadTableEntity) {
							// System.out.println(1);
						}
					}
					XhxkCadBlockEntity blockEntity = new XhxkCadBlockEntity(blockKey, indoorCadEntities);
					long end = System.currentTimeMillis();
					// logger.info(" time:"+(end-start)+"ms size:"+indoorCadEntities.size()+"
					// "+blockKey);
					return blockEntity;
				}
			});
			futureList.add(future);

		}

		for (Future<XhxkCadBlockEntity> f : futureList) {
			try {
				blockEntities.add(f.get());
				;
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				logger.error(e.getMessage(),e);
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
		}
		executorService.shutdown();
		return blockEntities;
	}

	public XhxkCadBaseEntity transformCadBaseEntity(CadBaseEntity baseEntity, List<XhxkCadBlockEntity> blockEntities,
			Cad3DPoint minPoint,
			Cad3DPoint maxPoint) {
		String id = UUID.randomUUID().toString(true);
		String type = baseEntity.getClass().getSimpleName();
		String layer = baseEntity.getLayerName();
		if (baseEntity instanceof CadAttrib) {// 动态属性
			CadAttrib cadAttrib = (CadAttrib) baseEntity;
			String key = cadAttrib.getDefinitionTagString();
			String value = cadAttrib.getDefaultText();
			XhxkCadText indoorEntity = new XhxkCadText(id, type, null, layer, key, value);
			indoorEntity = initIndoorCadTextEntity(baseEntity, indoorEntity);
			return indoorEntity;
		} else if (baseEntity instanceof CadAttDef) {
			CadAttDef cadAttDef = (CadAttDef) baseEntity;
			String key = cadAttDef.getId();
			String text = cadAttDef.getDefaultString();
			XhxkCadText indoorEntity = new XhxkCadText(id, type, null, layer, key, text);
			indoorEntity = initIndoorCadTextEntity(baseEntity, indoorEntity);
			return indoorEntity;
		} else if (baseEntity instanceof CadMText) {
			CadMText cadMText = (CadMText) baseEntity;
			String text = cadMText.getFullText();
			text = cadMText.getFullClearText();
			XhxkCadText indoorEntity = new XhxkCadText(id, type, null, layer, null, text,
					cadMText.getReferenceRectangleWidth());
			indoorEntity = initIndoorCadTextEntity(baseEntity, indoorEntity);
			indoorEntity.setColorId(cadMText.getColorId());
			return indoorEntity;
		} else if (baseEntity instanceof CadText) {
			CadText cadText = (CadText) baseEntity;
			String text = cadText.getDefaultValue();
			XhxkCadText indoorEntity = new XhxkCadText(id, type, null, layer, null, text);
			indoorEntity = initIndoorCadTextEntity(baseEntity, indoorEntity);
			indoorEntity.setColorId(cadText.getColorId());
			return indoorEntity;
		} else if (baseEntity instanceof CadCircle && baseEntity.getClass().getSimpleName().equals("CadCircle")) {
			CadCircle circle = (CadCircle) baseEntity;
			XhxkCadCircle indoorEntity = new XhxkCadCircle(id, type, null, layer,
					transform3dPoint(circle.getCenterPoint()), circle.getRadius());
			indoorEntity = initIndoorCadCircleEntity(baseEntity, indoorEntity);
			indoorEntity.setColorId(circle.getColorId());
			return indoorEntity;
		} else if (baseEntity instanceof CadArc && baseEntity.getClass().getSimpleName().equals("CadArc")) {
			CadArc arc = (CadArc) baseEntity;
			XhxkCadArc indoorEntity = new XhxkCadArc(id, type, null, layer,
					transform3dPoint(new Cad3DPoint(arc.getCenterPoint().getX(), arc.getCenterPoint().getY())),
					arc.getRadius(), arc.getStartAngle(), arc.getEndAngle());
			indoorEntity.setColorId(arc.getColorId());
			initIndoorCadArcEntity(baseEntity, indoorEntity);
			return indoorEntity;
		} else if (baseEntity instanceof CadEllipse && baseEntity.getClass().getSimpleName().equals("CadEllipse")) {
			CadEllipse ellipse = (CadEllipse) baseEntity;
			XhxkCadPoint majorEndPoint = transform3dPoint(ellipse.getMajorEndPoint());
			XhxkCadPoint minorEndPoint = transform3dPoint(ellipse.getMinorEndPoint());
			XhxkCadPoint extrusion = transform3dPoint(ellipse.getExtrusionDirection());
			XhxkCadEllipse indoorEntity = new XhxkCadEllipse(id, type, null, layer,
					transform3dPoint(ellipse.getCenterPoint()), ellipse.getRadius(), ellipse.getStartAngle(),
					ellipse.getEndAngle(), majorEndPoint, minorEndPoint, extrusion);

			indoorEntity.setColorId(ellipse.getColorId());
			initIndoorCadEllipseEntity(baseEntity, indoorEntity);
			return indoorEntity;
		} else if (baseEntity instanceof CadLine) {
			CadLine cadLine = (CadLine) baseEntity;
			XhxkCadLine indoorEntity = transformLine(cadLine);
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			indoorEntity.setColorId(cadLine.getColorId());
			indoorEntity = initIndoorCadLineEntity(baseEntity, indoorEntity);
			indoorEntity.setDeviceCode(baseEntity.getVisible()+"");
			XhxkCadPoint point = indoorEntity.getFirstPoint();
			// System.out.println(baseEntity.getColorName()+"\t"+baseEntity.getColorId()+"\t"+baseEntity.getColorValue());
			return indoorEntity;
		} else if (baseEntity instanceof CadLwPolyline) {
			CadLwPolyline cadLwPolyline = (CadLwPolyline) baseEntity;
			XhxkCadLwPolyline indoorEntity = transformLwPolyline(cadLwPolyline);
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			indoorEntity.setColorId(cadLwPolyline.getColorId());
			indoorEntity = initIndoorCadLwPolylineEntity(baseEntity, indoorEntity);
			return indoorEntity;
		} else if (baseEntity instanceof CadPolyline) {
			CadPolyline cadPolyline = (CadPolyline) baseEntity;
			XhxkCadLwPolyline indoorEntity = transformPolyline(cadPolyline);
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			indoorEntity.setColorId(cadPolyline.getColorId());
			indoorEntity = initIndoorCadPolylineEntity(baseEntity, indoorEntity);
			return indoorEntity;
		} else if (baseEntity instanceof CadInsertObject) {

			CadInsertObject cadInsertObject = (CadInsertObject) baseEntity;

			String blockKey = cadInsertObject.getOriginalBlockName();
			Cad3DPoint insertionPoint = cadInsertObject.getInsertionPoint();
			Cad3DPoint extrusionDirection = cadInsertObject.getExtrusionDirection();
			if ((minPoint!=null)&&(maxPoint!=null)&& !( (insertionPoint.getX()>=minPoint.getX())&& (insertionPoint.getX()<=maxPoint.getX())
			  && (insertionPoint.getY()>=minPoint.getY())&& (insertionPoint.getY()<=maxPoint.getY()))) {
				return null;
			}
			double extr = extrusionDirection.getZ();
			if ((extr != 0) && (extr != 1)) {
				insertionPoint.setX(insertionPoint.getX() * extr);
			}

			List<XhxkCadBlockEntity> blockList = blockEntities.stream().filter(b -> b.getBlockName().equals(blockKey))
					.collect(Collectors.toList());

			List<XhxkCadBaseEntity> modelEntities = blockList.get(0).getBlockEntities();
			List<XhxkCadBaseEntity> childObjects = new LinkedList<XhxkCadBaseEntity>();
			List<CadBaseEntity> list = cadInsertObject.getChildObjects();
			for (CadBaseEntity entity : list) {
				XhxkCadBaseEntity indoorCadBaseEntity = transformCadBaseEntity(entity, blockEntities, minPoint,maxPoint);
				if (indoorCadBaseEntity != null) {
					childObjects.add(indoorCadBaseEntity);
				}
			}

			// System.out.println("==============="+blockKey+"\t"+cadInsertObject.getName());
			XhxkCadInsertObject indoorEntity = new XhxkCadInsertObject(id, type, blockKey, layer,
					transform3dPoint(insertionPoint), childObjects, modelEntities, cadInsertObject.getScaleX() * extr,
					cadInsertObject.getScaleY(), cadInsertObject.getRotationAngle(), cadInsertObject.getName());

			indoorEntity = initIndoorCadInsertObjectEntity(baseEntity, indoorEntity, blockList);
			return indoorEntity;
		} else if (baseEntity instanceof CadLeader) {
			CadLeader leader = (CadLeader) baseEntity;
			XhxkCadLine indoorEntity = transformCadLeader(leader);
			if (indoorEntity == null)
				return null;
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			indoorEntity.setColorId(leader.getColorId());
			indoorEntity = initIndoorCadLeaderEntity(baseEntity, indoorEntity);
			return indoorEntity;
		} else if (baseEntity instanceof CadTableEntity) {
			if (blockEntities == null)
				return null;
			CadTableEntity tableEntity = (CadTableEntity) baseEntity;
			Cad3DPoint insertionPoint = tableEntity.getInsertionPoint();
			String blockKey = tableEntity.getBlockName();
			List<XhxkCadBlockEntity> blockList = blockEntities.stream().filter(b -> b.getBlockName().equals(blockKey))
					.collect(Collectors.toList());
			List<XhxkCadBaseEntity> modelEntities = blockList.get(0).getBlockEntities();

			XhxkCadInsertObject indoorEntity = new XhxkCadInsertObject(id, type, blockKey, layer,
					transform3dPoint(insertionPoint), null, modelEntities, 1, 1, 0, blockKey);
			indoorEntity = initIndoorCadTableEntity(baseEntity, indoorEntity, blockList);
			return indoorEntity;
		}  else if (baseEntity instanceof CadHatch) {
			CadHatch hatch = (CadHatch) baseEntity;
			XhxkCadLwPolyline indoorEntity = transformHatch(hatch);
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			indoorEntity.setColorId(hatch.getColorId());
			indoorEntity = initIndoorCadPolylineEntity(baseEntity, indoorEntity);
			return indoorEntity;
		}
		else {
			 System.err.println(baseEntity);
		}
		return null;
	}

	public XhxkCadLwPolyline transformHatch(CadHatch hatch) {
		List<CadHatchBoundaryPathContainer> list = hatch.getBoundaryPaths();
		if((list!=null)&&!list.isEmpty()) {
			List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
			for(CadHatchBoundaryPathContainer hatchBoundaryPathContainer : list) {
				List<ICadBoundaryPath> paths=  hatchBoundaryPathContainer.getBoundaryPath();
				if((paths!=null)&&!paths.isEmpty()) {
					for(ICadBoundaryPath path : paths) {
						if(path instanceof CadPolylineBoundaryPath ) {
							points = new LinkedList<XhxkCadPoint>();
							CadPolylineBoundaryPath boundaryPath = (CadPolylineBoundaryPath)path;
							List<Point2D> point2ds =  boundaryPath.getVertices();
							for(Point2D point:point2ds) {
								points.add(new XhxkCadPoint(point.getX(), point.getY()));
							}
						}else if(path instanceof CadEdgeBoundaryPath) {
							//遇到再细化
							CadEdgeBoundaryPath boundaryPath = (CadEdgeBoundaryPath)path;

						}
					}
				}
			}
			return new XhxkCadLwPolyline(null, null, null, hatch.getLayerName(),0, points);
		}

		return null;
	}

	public XhxkCadLwPolyline transformPolyline(CadPolyline polyline) {
		List<CadBaseEntity> list = polyline.getChildObjects();
		if ((list != null) && !list.isEmpty()) {
			List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
			for (CadBaseEntity entity : list) {
				if (entity instanceof Cad2DVertex) {
					Cad2DVertex vertex = (Cad2DVertex) entity;
					Cad3DPoint point = vertex.getLocationPoint();
					points.add(new XhxkCadPoint(point.getX(), point.getY()));
				}
			}
			return new XhxkCadLwPolyline(null, null, null, polyline.getLayerName(), polyline.getFlag(), points);
		}
		return null;
	}

	public XhxkCadLwPolyline transformLwPolyline(CadLwPolyline polyline) {
		return new XhxkCadLwPolyline(null, null, null, polyline.getLayerName(), polyline.getFlag(),
				transformPoint(polyline.getCoordinates()));
	}

	public XhxkCadLwPolyline transformLwPolyline(CadPolyline polyline) {
		return new XhxkCadLwPolyline(null, null, null, polyline.getLayerName(), polyline.getFlag(), null);// transformPoint(polyline.getChildObjects())
	}

	public XhxkCadLine transformLine(CadLine line) {
		return new XhxkCadLine(null, null, null, line.getLayerName(), transform3dPoint(line.getFirstPoint()),
				transform3dPoint(line.getSecondPoint()));
	}

	public XhxkCadLine transformCadLeader(CadLeader leader) {
		List<Cad3DPoint> points = leader.getCoordinates();
		if ((points != null) && !points.isEmpty() && (points.size() == 2)) {
			return new XhxkCadLine(null, null, null, leader.getLayerName(), transform3dPoint(points.get(0)),
					transform3dPoint(points.get(1)));
		}
		return null;
	}

	/**
	 * 将aspose坐标转换为IndoorCadPoint
	 *
	 * @param point
	 * @return
	 */
	public XhxkCadPoint transform3dPoint(Cad3DPoint point) {
		return new XhxkCadPoint(point.getX(), point.getY(), point.getZ());
	}

	public XhxkCadPoint transformPoint(Cad2DPoint point) {
		return new XhxkCadPoint(point.getX(), point.getY());
	}

	public List<XhxkCadPoint> transform3dPoint(List<Cad3DPoint> points) {
		List<XhxkCadPoint> list = new LinkedList<XhxkCadPoint>();
		for (Cad3DPoint point : points) {
			list.add(new XhxkCadPoint(point.getX(), point.getY(), point.getZ()));
		}
		return list;
	}

	public List<XhxkCadPoint> transformPoint(List<Cad2DPoint> points) {
		List<XhxkCadPoint> list = new LinkedList<XhxkCadPoint>();
		for (Cad2DPoint point : points) {
			list.add(new XhxkCadPoint(point.getX(), point.getY()));
		}
		return list;
	}

	public String cadSuffix(String file) {
		return file.substring(file.lastIndexOf("."));
	}

	public Map<String, List<XhxkCadBaseEntity>> getBlockModel(CadBlockDictionary block) {
		Map<String, List<XhxkCadBaseEntity>> resultMap = new HashMap<String, List<XhxkCadBaseEntity>>();
		KeyCollection<String, CadBlockEntity> keys = block.getKeys();
		for (String blockKey : keys) {
			List<XhxkCadBaseEntity> indoorCadEntities = new LinkedList<XhxkCadBaseEntity>();
			CadBlockEntity entity = block.get_Item(blockKey);
			CadBaseEntity[] entities = entity.getEntities();
			for (CadBaseEntity baseEntity : entities) {
				XhxkCadBaseEntity indoorCadEntity = transformCadBaseEntity(baseEntity, null, null,null);
				if (indoorCadEntity != null) {
					indoorCadEntities.add(indoorCadEntity);
				}
				resultMap.put(blockKey, indoorCadEntities);
			}
		}
		return resultMap;
	}

	public XhxkCadArc initIndoorCadArcEntity(CadBaseEntity entity, XhxkCadArc indoorEntity) {
		int angleStep = indoorEntity.getAngleStep();
		CadArc arc = (CadArc) entity;
		double startAngle = arc.getStartAngle();
		double endAngle = arc.getEndAngle();
		double radius = arc.getRadius();
		XhxkCadPoint centerPoint = new XhxkCadPoint(arc.getCenterPoint().getX(), arc.getCenterPoint().getY());
		if (startAngle > endAngle) {
			endAngle = 360 + endAngle;
		}

		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		int startIndex = (int) (startAngle / angleStep) - 1;
		int endIndex = (int) (endAngle / angleStep) + 1;
		String wkt = "LINESTRING(";
		for (int i = startIndex; i <= endIndex; i = i + 1) {
			double angle = (double)i * angleStep;
			if (angle > endAngle) {
				angle = endAngle;
			}
			if (angle < startAngle) {
				angle = startAngle;
			}
			XhxkCadPoint point = new XhxkCadPoint((centerPoint.getX() + Math.cos(Math.toRadians(angle)) * radius),
					(centerPoint.getY() + Math.sin(Math.toRadians(angle)) * radius));
			wkt += String.format("%f %f,", point.getX(), point.getY());
			points.add(point);
			wkt += String.format("%f %f,", point.getX(), point.getY());
		}
		if (wkt.endsWith(",")) {
			wkt = wkt.substring(0, wkt.length() - 1);
		}
		wkt += ")";
		indoorEntity.setValues(centerPoint, wkt, points);
		return indoorEntity;
	}

	public XhxkCadCircle initIndoorCadCircleEntity(CadBaseEntity entity, XhxkCadCircle indoorEntity) {
		CadCircle circle = (CadCircle) entity;
		int angleStep = indoorEntity.getAngleStep();
		double radius = circle.getRadius();
		XhxkCadPoint centerPoint = new XhxkCadPoint(circle.getCenterPoint().getX(), circle.getCenterPoint().getY());
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		String wkt = "LINESTRING(";
		for (int i = 0; i < 360; i = i + angleStep) {
			XhxkCadPoint point = new XhxkCadPoint((centerPoint.getX() + Math.cos(Math.toRadians(i)) * radius),
					(centerPoint.getY() + Math.sin(Math.toRadians(i)) * radius));
			wkt += String.format("%f %f,", point.getX(), point.getY());
			points.add(point);
		}
		XhxkCadPoint point = points.get(0);
		points.add(point);
		wkt += String.format("%f %f,", point.getX(), point.getY());
		if (wkt.endsWith(",")) {
			wkt = wkt.substring(0, wkt.length() - 1);
		}
		wkt += ")";

		indoorEntity.setValues(centerPoint, wkt, points);
		return indoorEntity;
	}

	public XhxkCadEllipse initIndoorCadEllipseEntity(CadBaseEntity entity, XhxkCadEllipse indoorEntity) {
		CadEllipse ellipse = (CadEllipse) entity;
		int angleStep = indoorEntity.getAngleStep();
		XhxkCadPoint centerPoint = new XhxkCadPoint(ellipse.getCenterPoint().getX(), ellipse.getCenterPoint().getY(),
				ellipse.getCenterPoint().getZ());
		XhxkCadPoint majorEndPoint = new XhxkCadPoint(ellipse.getMajorEndPoint().getX(),
				ellipse.getMajorEndPoint().getY(), ellipse.getMajorEndPoint().getZ());
		XhxkCadPoint minorEndPoint = new XhxkCadPoint(ellipse.getMinorEndPoint().getX(),
				ellipse.getMinorEndPoint().getY(), ellipse.getMinorEndPoint().getZ());
		XhxkCadPoint extrusion = new XhxkCadPoint(ellipse.getExtrusionDirection().getX(),
				ellipse.getExtrusionDirection().getY(), ellipse.getExtrusionDirection().getZ());
		boolean counterclockwiseFlag = extrusion.getZ() > 0;// true为正序
		double a = Math.sqrt(Math.pow(majorEndPoint.getX(), 2) + Math.pow(majorEndPoint.getY(), 2));
		double b = Math.sqrt(Math.pow(minorEndPoint.getX(), 2) + Math.pow(minorEndPoint.getY(), 2));

		double startAngle = Math.toDegrees(ellipse.getStartAngle());
		double endAngle = Math.toDegrees(ellipse.getEndAngle());
		double defaultAngle = 90 - Math.toDegrees(Math.asin(majorEndPoint.getX() / (a)));
		double o = Math.toRadians(defaultAngle);
		double k = Math.pow(Math.cos(o), 2) + Math.pow(Math.sin(o), 2);
		double e = Math.pow(b, 2) * Math.pow(Math.cos(o), 2) + Math.pow(a, 2) * Math.pow(Math.sin(o), 2);
		double f = Math.pow(b, 2) * Math.pow(Math.sin(o), 2) + Math.pow(a, 2) * Math.pow(Math.cos(o), 2);
		double g = 2 * Math.sin(o) * Math.cos(o) * (Math.pow(a, 2) - Math.pow(b, 2));
		double h = Math.pow(a, 2) * Math.pow(b, 2) * Math.pow(k, 2);

		int majorQuadrant = quadrant(majorEndPoint);
		// int minorQuadrant = quadrant(minorEndPoint);
		double majorEX = majorEndPoint.getX();
		double majorEy = majorEndPoint.getY();
		// double minorEX=minorEndPoint.getX();
		// double minorEy=minorEndPoint.getY();
		double lastStartAngle = Math.toDegrees(Math.atan(Math.abs(majorEy) / Math.abs(majorEX)));
		if (majorQuadrant == 2) {// 0度在二象限
			lastStartAngle = 90 + Math.toDegrees(Math.atan(Math.abs(majorEX) / Math.abs(majorEy)));
		} else if (majorQuadrant == 3) {// 0度在三象限
			lastStartAngle = 180 + Math.toDegrees(Math.atan(Math.abs(majorEy) / Math.abs(majorEX)));
		} else if (majorQuadrant == 4) {// 0度在四象限
			lastStartAngle = Math.toDegrees(Math.atan(Math.abs(majorEX) / Math.abs(majorEy))) - 90;
		}

		// 判断正向还是反向
		if (counterclockwiseFlag) {// 正向

		} else {
			double tmp = startAngle;
			startAngle = 360 - endAngle;
			endAngle = 360 - tmp;
		}

		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		int startIndex = (int) (startAngle / angleStep);
		int endIndex = (int) (endAngle / angleStep) + 1;
		String wkt = "LINESTRING(";
		for (int i = startIndex; i <= endIndex; i = i + 1) {
			double angle = (double)i * angleStep;
			if (angle > endAngle) {
				angle = endAngle;
			}
			if (angle < startAngle) {
				angle = startAngle;
			}
			angle = angle + lastStartAngle;
			double x = Math.sqrt(
					h / (e + f * Math.pow(Math.tan(Math.toRadians(angle)), 2) + g * Math.tan(Math.toRadians(angle))));// Math.sqrt(1/(m+n*Math.pow(Math.tan(Math.toRadians(angle)),2)));
			double y = Math.tan(Math.toRadians(angle)) * x;
			double pi = 90;
			if (angle > pi && angle < 3 * pi) {
				y = 0 - y;
				x = 0 - x;
			}
			x = x + centerPoint.getX();
			y = y + centerPoint.getY();
			XhxkCadPoint point = new XhxkCadPoint(x, y);
			wkt += String.format("%f %f,", point.getX(), point.getY());
			points.add(point);
		}
		if (wkt.endsWith(",")) {
			wkt = wkt.substring(0, wkt.length() - 1);
		}
		wkt += ")";

		centerPoint.setX(centerPoint.getX());
		centerPoint.setY(centerPoint.getY());
		indoorEntity.setValues(centerPoint, wkt, points);
		return indoorEntity;
	}

	public XhxkCadLine initIndoorCadLineEntity(CadBaseEntity entity, XhxkCadLine indoorEntity) {
		String wkt = "";
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		if (entity instanceof CadLine) {
			CadLine cad = (CadLine) entity;
			XhxkCadPoint firstPoint = new XhxkCadPoint((cad.getFirstPoint().getX()), (cad.getFirstPoint().getY()));
			XhxkCadPoint secondPoint = new XhxkCadPoint((cad.getSecondPoint().getX()), (cad.getSecondPoint().getY()));
			points.add(firstPoint);
			points.add(secondPoint);
			wkt = String.format("LINESTRING(%f %f,%f %f)", firstPoint.getX(), firstPoint.getY(), secondPoint.getX(),
					secondPoint.getY());
		}
		indoorEntity.setValues(points.get(0), wkt, points);
		return indoorEntity;
	}

	public XhxkCadLine initIndoorCadLeaderEntity(CadBaseEntity entity, XhxkCadLine indoorEntity) {
		String wkt = "";
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		if (entity instanceof CadLeader) {
			CadLeader cad = (CadLeader) entity;
			List<Cad3DPoint> list = cad.getCoordinates();
			wkt = "LINESTRING(";
			for (Cad3DPoint c : list) {
				XhxkCadPoint point = new XhxkCadPoint((c.getX()), (c.getY()));
				points.add(point);
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
		}
		indoorEntity.setValues(points.get(0), wkt, points);
		return indoorEntity;
	}

	public XhxkCadLwPolyline initIndoorCadLwPolylineEntity(CadBaseEntity entity, XhxkCadLwPolyline indoorEntity) {
		String wkt = "";
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		if (entity instanceof CadLwPolyline) {
			CadLwPolyline cad = (CadLwPolyline) entity;
			List<Cad2DPoint> list = cad.getCoordinates();
			wkt = "LINESTRING(";
			for (Cad2DPoint c : list) {
				XhxkCadPoint point = new XhxkCadPoint((c.getX()), (c.getY()));
				points.add(point);
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (cad.getFlag() == 1) {// 是否关闭
				XhxkCadPoint point = points.get(0);
				points.add(point);
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
		}
		indoorEntity.setValues(points.get(0), wkt, points);
		return indoorEntity;
	}

	public XhxkCadLwPolyline initIndoorCadPolylineEntity(CadBaseEntity entity, XhxkCadLwPolyline indoorEntity) {
		String wkt = "";

		List<XhxkCadPoint> coordinates = indoorEntity.getCoordinates();
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		if ((coordinates != null) && !coordinates.isEmpty()) {

			wkt = "LINESTRING(";
			for (XhxkCadPoint point : coordinates) {
				points.add(point);
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (indoorEntity.getFlag() == 1) {// 是否关闭
				XhxkCadPoint point = points.get(0);
				points.add(point);
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
			indoorEntity.setValues(points.get(0), wkt, points);
		}
		return indoorEntity;
	}

	public XhxkCadLwPolyline initIndoorCadLwPolylineEntity2(CadBaseEntity entity, XhxkCadLwPolyline indoorEntity) {
		String wkt = "";
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		if (entity instanceof CadLwPolyline) {
			CadLwPolyline cad = (CadLwPolyline) entity;
			List<Cad2DPoint> list = cad.getCoordinates();
			wkt = "LINESTRING(";
			for (Cad2DPoint c : list) {
				XhxkCadPoint point = new XhxkCadPoint((c.getX()), (c.getY()));
				points.add(point);
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (cad.getFlag() == 1) {// 是否关闭
				XhxkCadPoint point = points.get(0);
				points.add(point);
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
		}
		indoorEntity.setValues(points.get(0), wkt, points);
		return indoorEntity;
	}

	public XhxkCadText initIndoorCadTextEntity(CadBaseEntity entity, XhxkCadText indoorEntity) {
		XhxkCadPoint point = null;
		String allText = "";
		List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
		Map<String, String> content = new HashMap<String, String>();
		if (entity instanceof CadAttrib) {// 动态属性
			CadAttrib cadAttrib = (CadAttrib) entity;
			point = new XhxkCadPoint(cadAttrib.getAlignmentPoint().getX(), cadAttrib.getAlignmentPoint().getY());
			String key = cadAttrib.getDefinitionTagString();
			String value = cadAttrib.getDefaultText();
			if (StringUtils.isNoneBlank(value)) {
				content.put(key, value);
				allText += value;
			}
		} else if (entity instanceof CadAttDef) {
			CadAttDef cadAttDef = (CadAttDef) entity;
			point = new XhxkCadPoint(cadAttDef.getFirstAlignment().getX(), cadAttDef.getFirstAlignment().getY());
			String key = cadAttDef.getId();
			String text = cadAttDef.getDefaultString();
			// if (StringUtils.isNotBlank(text)) {
			content.put(key, text);
			allText += text;
			// }
		} else if (entity instanceof CadMText) {
			CadMText cadMText = (CadMText) entity;
			point = new XhxkCadPoint(cadMText.getInsertionPoint().getX(), cadMText.getInsertionPoint().getY());
			String text = cadMText.getFullClearText();
			if (StringUtils.isNotBlank(text)) {
				content.put("CadMText", text);
				allText += text;
			}
		} else if (entity instanceof CadText) {
			CadText cadText = (CadText) entity;
			point = new XhxkCadPoint(cadText.getFirstAlignment().getX(), cadText.getFirstAlignment().getY());
			String text = cadText.getDefaultValue();
			if (StringUtils.isNotBlank(text)) {
				content.put("CadMText", text);
				allText += text;
			}
		}

		if(allText.contains("XQJ-C-300")) {
			System.out.println(entity.getVisible());
		}else if(allText.contains("A03-B1")) {
			System.out.println(entity.getVisible());
		}

		content.put("allText", allText);
		if(point != null) {
			points.add(point);
			String wkt = String.format("POINT(%f %f)", point.getX(), point.getY());
			indoorEntity.setValues(point, wkt, points, allText, content);
		}
		return indoorEntity;
	}

	public XhxkCadInsertObject initIndoorCadInsertObjectEntity(CadBaseEntity cadBaseEntity,
			XhxkCadInsertObject indoorEntity, List<XhxkCadBlockEntity> blockEntities) {
		CadInsertObject cadInsertObject = (CadInsertObject) cadBaseEntity;
		XhxkCadPoint insertionPoint = indoorEntity.getInsertionPoint();

		String block = indoorEntity.getBlock();
		List<XhxkCadBaseEntity> indoorCadEntities = new LinkedList<XhxkCadBaseEntity>();
		List<CadBaseEntity> list = cadInsertObject.getChildObjects();
		XhxkCadText textEntity = null;
		for (CadBaseEntity baseEntity : list) {// 文字类处理
			if (baseEntity instanceof CadAttrib || baseEntity instanceof CadAttDef || baseEntity instanceof CadMText
					|| baseEntity instanceof CadText) {
				XhxkCadText indoorCadEntity = (XhxkCadText) transformCadBaseEntity(baseEntity, blockEntities, null,null);
				indoorCadEntity = initIndoorCadTextEntity(baseEntity, indoorCadEntity);
				indoorCadEntity.setPosition(insertionPoint);
				if (textEntity == null) {
					textEntity = indoorCadEntity;
				} else {
					Map<String, String> textMap = textEntity.getTextMap();
					Map<String, String> map = indoorCadEntity.getTextMap();
					for (String key : map.keySet()) {
						if (StringUtils.isNotBlank(map.get(key))) {
							textMap.put(key, map.get(key));
						}
					}
					textEntity.setTextMap(textMap);
				}
			}
		}
		if (textEntity != null) {
			textEntity.setBlock(block);
			textEntity.setPoints(null);
			textEntity.setPosition(
					new XhxkCadPoint(insertionPoint.getX(), insertionPoint.getY()));
			textEntity.setWkt();
			//textEntity.setDeviceCode(textEntity.getPosition().getX()+"/"+textEntity.getPosition().getY());
			indoorCadEntities.add(textEntity);
		}
		List<XhxkCadBaseEntity> lastModelEntity = indoorEntity.scaleAndRotate(this);// this需要验证功能
		indoorCadEntities.addAll(lastModelEntity);

		/*
		 * for (XhxkCadBaseEntity entity : lastModelEntity) { entity =
		 * moveCadBaseEntity(entity,insertionPoint); if(entity!=null)
		 * indoorCadEntities.add(entity); }
		 */
		if (textEntity != null) {
			indoorEntity.setTextMap(textEntity.getTextMap());
		}
		indoorEntity.setInsertionPoint(insertionPoint);
		indoorEntity.setPosition(insertionPoint);
		indoorEntity.setIndoorCadEntities(indoorCadEntities);
		indoorEntity.initialize();
		return indoorEntity;
	}

	public XhxkCadInsertObject initIndoorCadTableEntity(CadBaseEntity cadBaseEntity, XhxkCadInsertObject indoorEntity,
			List<XhxkCadBlockEntity> blockEntities) {
		XhxkCadPoint insertionPoint = indoorEntity.getInsertionPoint();
		List<XhxkCadBaseEntity> indoorCadEntities = new LinkedList<XhxkCadBaseEntity>();
		if (blockEntities == null || blockEntities.isEmpty()) {
			return null;
		}
		XhxkCadBlockEntity blockEntity = blockEntities.get(0);
		for (XhxkCadBaseEntity baseEntity : blockEntity.getBlockEntities()) {
			if (baseEntity instanceof XhxkCadText) {
				XhxkCadText cadText = (XhxkCadText) baseEntity;
				cadText.setPosition(new XhxkCadPoint(cadText.getPosition().getX() + insertionPoint.getX(),
						cadText.getPosition().getY() + insertionPoint.getY()));
				XhxkCadText newCadText = new XhxkCadText(cadText.getId(), cadText.getType(), cadText.getBlock(),
						cadText.getLayer(), null, cadText.getText());
				XhxkCadPoint point = cadText.getPosition();
				List<XhxkCadPoint> points = new ArrayList<XhxkCadPoint>();
				points.add(point);
				String wkt = String.format("POINT(%f %f)", point.getX(), point.getY());
				newCadText.setValues(point, wkt, points, cadText.getText(), cadText.getTextMap());
				indoorCadEntities.add(newCadText);

			} else if (baseEntity instanceof XhxkCadLine) {
				XhxkCadLine line = (XhxkCadLine) baseEntity;
				XhxkCadPoint firstPoint = line.getFirstPoint();
				firstPoint.setX(firstPoint.getX() + insertionPoint.getX());
				firstPoint.setY(firstPoint.getY() + insertionPoint.getY());
				XhxkCadPoint secondPoint = line.getSecondPoint();
				secondPoint.setX(secondPoint.getX() + insertionPoint.getX());
				secondPoint.setY(secondPoint.getY() + insertionPoint.getY());

				XhxkCadLine cadLine = new XhxkCadLine(UUID.randomUUID().toString(true) + "------------", null,
						indoorEntity.getBlock(), indoorEntity.getLayer(), firstPoint, secondPoint);
				System.err.println(cadLine.getId() + "\t" + cadLine.getMinx() + "\t" + cadLine.getMaxx() + "\t"
						+ cadLine.getMiny() + "\t" + cadLine.getMaxy());
				indoorCadEntities.add(cadLine);
			}
		}
		indoorEntity.setInsertionPoint(insertionPoint);
		indoorEntity.setPosition(insertionPoint);
		indoorEntity.setIndoorCadEntities(indoorCadEntities);
		indoorEntity.initialize();
		return indoorEntity;
	}

	public XhxkCadPoint deviationPoint(XhxkCadPoint point, double x, double y) {
		return new XhxkCadPoint(point.getX() - x, point.getY() - y);
	}

	public XhxkCadPoint pointScale(XhxkCadPoint point, double scale) {
		return new XhxkCadPoint(point.getX() * scale, point.getY() * scale);
	}

	public int quadrant(XhxkCadPoint point) {
		double x = point.getX();
		double y = point.getY();
		int result = 1;
		if ((x <= 0) && (y >= 0)) {// 0度在二象限
			result = 2;
		} else if ((x <= 0) && (y <= 0)) {// 0度在三象限
			result = 3;
		} else if ((x >= 0) && (y <= 0)) {// 0度在四象限
			result = 4;
		}
		return result;
	}

	public XhxkCadPoint transfer(XhxkCadPoint point, Double scale, XhxkCadPoint minPoint) {
		if (scale != null) {
			point = pointScale(point, scale);
		}
		if (minPoint != null) {
			point = deviationPoint(point, minPoint.getX(), minPoint.getY());
		}
		return point;
	}

	public List<XhxkCadPoint> transfer(List<XhxkCadPoint> points, Double scale, XhxkCadPoint minPoint) {
		if (points == null || points.isEmpty())
			return points;
		List<XhxkCadPoint> result = new LinkedList<XhxkCadPoint>();
		for (XhxkCadPoint point : points) {
			point = transfer(point, scale, minPoint);
			result.add(point);
		}
		return result;
	}

	/**
	 * 针对组合体，需要整体移动所有元素
	 *
	 * @param baseEntity
	 * @param point
	 * @return
	 */
	private XhxkCadBaseEntity moveCadBaseEntity(XhxkCadBaseEntity baseEntity, XhxkCadPoint insertionPoint) {
		String id = UUID.randomUUID().toString(true);
		String type = baseEntity.getClass().getSimpleName();
		if (baseEntity instanceof XhxkCadCircle ) {
			XhxkCadCircle circle = (XhxkCadCircle) baseEntity;
			XhxkCadPoint centerPoint = new XhxkCadPoint(circle.getCenterPoint().getX() + insertionPoint.getX(),
					circle.getCenterPoint().getY() + insertionPoint.getY());
			XhxkCadCircle indoorEntity = new XhxkCadCircle(id, type, null, circle.getLayer(), centerPoint,
					circle.getRadius());
			indoorEntity.setId(id);
			indoorEntity.setType(type);

			List<XhxkCadPoint> points = circle.getPoints();
			String wkt = "LINESTRING(";
			for (XhxkCadPoint point : points) {
				point.setX(point.getX() + insertionPoint.getX());
				point.setY(point.getY() + insertionPoint.getY());
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
			indoorEntity.setValues(centerPoint, wkt, points);
			return indoorEntity;
		} else if (baseEntity instanceof XhxkCadArc) {
			XhxkCadArc arc = (XhxkCadArc) baseEntity;
			XhxkCadArc indoorEntity = new XhxkCadArc(id, type, null, arc.getLayer(),
					new XhxkCadPoint(arc.getCenterPoint().getX() + insertionPoint.getX(),
							arc.getCenterPoint().getY() + insertionPoint.getY()),
					arc.getRadius(), arc.getStartAngle(), arc.getEndAngle());
			indoorEntity.setId(id);
			indoorEntity.setType(type);
			return indoorEntity;
		} else if (baseEntity instanceof XhxkCadEllipse) {
			XhxkCadEllipse ellipse = (XhxkCadEllipse) baseEntity;
			XhxkCadPoint majorEndPoint = ellipse.getMajorEndPoint();
			XhxkCadPoint minorEndPoint = ellipse.getMinorEndPoint();
			XhxkCadPoint extrusion = ellipse.getExtrusion();
			XhxkCadPoint centerPoint = new XhxkCadPoint(ellipse.getCenterPoint().getX() + insertionPoint.getX(),
					ellipse.getCenterPoint().getY() + insertionPoint.getY());
			XhxkCadEllipse indoorEntity = new XhxkCadEllipse(id, type, null, ellipse.getLayer(), centerPoint,
					ellipse.getRadius(), ellipse.getStartAngle(), ellipse.getEndAngle(), majorEndPoint, minorEndPoint,
					extrusion);

			indoorEntity.setId(id);
			indoorEntity.setType(type);

			List<XhxkCadPoint> points = ellipse.getPoints();
			String wkt = "LINESTRING(";
			for (XhxkCadPoint point : points) {
				point.setX(point.getX() + insertionPoint.getX());
				point.setY(point.getY() + insertionPoint.getY());
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
			indoorEntity.setValues(centerPoint, wkt, points);
			return indoorEntity;
		} else if (baseEntity instanceof XhxkCadLine) {
			XhxkCadLine cadLine = (XhxkCadLine) baseEntity;
			XhxkCadPoint firstPoint = new XhxkCadPoint(cadLine.getFirstPoint().getX() + insertionPoint.getX(),
					cadLine.getFirstPoint().getY() + insertionPoint.getY());
			XhxkCadPoint secondPoint = new XhxkCadPoint(cadLine.getSecondPoint().getX() + insertionPoint.getX(),
					cadLine.getSecondPoint().getY() + insertionPoint.getY());
			XhxkCadLine indoorEntity = new XhxkCadLine(id, type, null, cadLine.getLayer(), firstPoint, secondPoint);
			indoorEntity.setId(id);
			indoorEntity.setType(type);

			String wkt = "";
			List<XhxkCadPoint> points = new LinkedList<XhxkCadPoint>();
			points.add(firstPoint);
			points.add(secondPoint);
			wkt = String.format("LINESTRING(%f %f,%f %f)", firstPoint.getX(), firstPoint.getY(), secondPoint.getX(),
					secondPoint.getY());
			indoorEntity.setValues(points.get(0), wkt, points);

			return indoorEntity;
		} else if (baseEntity instanceof XhxkCadLwPolyline) {
			XhxkCadLwPolyline cadLwPolyline = (XhxkCadLwPolyline) baseEntity;
			List<XhxkCadPoint> coordinates = cadLwPolyline.getCoordinates();
			String wkt = "LINESTRING(";
			for (XhxkCadPoint point : coordinates) {
				point.setX(point.getX() + insertionPoint.getX());
				point.setY(point.getY() + insertionPoint.getY());
				wkt += String.format("%f %f,", point.getX(), point.getY());
			}
			XhxkCadPoint point = coordinates.get(0);
			point.setX(point.getX());
			point.setY(point.getY());
			coordinates.add(point);
			wkt += String.format("%f %f,", point.getX(), point.getY());
			if (wkt.endsWith(",")) {
				wkt = wkt.substring(0, wkt.length() - 1);
			}
			wkt += ")";
			XhxkCadLwPolyline indoorEntity = new XhxkCadLwPolyline(id, type, null, cadLwPolyline.getLayer(),
					cadLwPolyline.getFlag(), coordinates);
			indoorEntity.setId(id);
			indoorEntity.setType(type);

			indoorEntity.setValues(coordinates.get(0), wkt, coordinates);
			return indoorEntity;
		}
		return null;
	}

	public void cadLicense() {
		InputStream is2 = CadServiceImpl.class.getClassLoader().getResourceAsStream("license.xml");
		com.aspose.cad.License asposeLic2 = new com.aspose.cad.License();
		try {
			asposeLic2.setLicense(is2);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}

}
