/**
 *
 *
 * @Title: SysDictDataDto.java
 * <AUTHOR>
 * @date 2022年9月19日
 * @version V1.0
 */
package cn.cmcc.system.api.pojo.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class SysDictDataDto implements Serializable{

	/**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	private Long dictId;

	private Long parentId;

    /**
     * 所属字典类型的id
     */
    private String dictTypeCode;

    /**
     * 字典编码
     */
    private String code;

    /**
     * 字典名称
     */
    private String name;

    /**
     * 状态（字典）
     */
    private String status;

    /**
     * 分组
     */
    private String grouping;
}
