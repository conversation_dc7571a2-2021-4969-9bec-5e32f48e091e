/**
 * @Title: GeometryTypeHandler.java
 * @Package cn.cmcc.common.core.db
 * 
 * <AUTHOR>
 * @date 2022年4月14日
 * @version V1.0
 */
package cn.cmcc.common.datasource.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

@MappedTypes(Object.class)
public class GeometryTypeHandler  extends BaseTypeHandler<Object>{

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType)
			throws SQLException {
		PGobject jsonObject = new PGobject();
		jsonObject.setType("geometry");
		jsonObject.setValue(parameter.toString());
		ps.setObject(i, jsonObject);

	}

	@Override
    public Object getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
    	Object obj = rs.getObject(columnName);
        if(obj instanceof PGobject) {
        	return obj.toString();
        }
        return rs.getString(columnName);
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if(obj instanceof PGobject)
        	return obj.toString();
    	return rs.getString(columnIndex);
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
    	Object obj = cs.getObject(columnIndex);
        if(obj instanceof PGobject)
        	return obj.toString();
    	return cs.getString(columnIndex);
    }

}
