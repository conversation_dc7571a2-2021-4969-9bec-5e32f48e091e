/**
 * @Title: CommonExportDto.java
 * @Package cn.cmcc.base.pojo.dto
 *
 * <AUTHOR>
 * @date 2022年6月7日
 * @version V1.0
 */
package cn.cmcc.common.pojo.dto;

import cn.cmcc.common.enums.ExportCodeTypeEnums;
import cn.cmcc.common.enums.FileTypeEnums;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CommonExportDTO implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 编码
	 * 1、type：common 表示通用查询编码
	 * 2、type: config 表示系统参数配置
	 */
	@NotEmpty(message = "编码不能为空")
	private String code;

	/**
	 * code 类型
	 */
	@NotEmpty(message = "编码类型不能为空")
	private String type = ExportCodeTypeEnums.COMMON.getCode();

	/**
	 * 导出文件类型
	 */
	@NotEmpty(message = "文件不能为空")
	private String fileType = FileTypeEnums.EXCEL.getCode();

	/**
	 * 通用查询参数
	 */
	private Map<String, Object> paramMap = new HashMap<>();

	/**
	 * 自定义导出字段(中文)
	 */
	private Map<String, List<String>> customFieldMap;

	/**
	 * 水印文本
	 */
	private String watermarkText;

	/**
	 * 是否启用水印
	 */
	private Boolean enableWatermark = false;
}
