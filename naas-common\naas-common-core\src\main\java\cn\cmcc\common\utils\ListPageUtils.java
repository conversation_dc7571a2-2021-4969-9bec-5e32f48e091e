package cn.cmcc.common.utils;

import cn.cmcc.common.constant.HttpStatus;
import cn.cmcc.common.utils.sql.SqlUtil;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.cmcc.common.web.page.PageDomain;
import cn.cmcc.common.web.page.PageResult;
import cn.cmcc.common.web.page.TableSupport;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/1/10 17:37
 */
public class ListPageUtils {

    public static PageResult getDataTable(List<?> list) {
        PageResult rspData = new PageResult();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setData(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }
    public static void customPagination(Map<String, Object> paramMap) {
        if (paramMap == null) {
            return;
        }

        PageDomain pageDomain = TableSupport.getPageDomain();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            int offset = (pageNum - 1) * pageSize;

            paramMap.put("pageSize", pageSize);
            paramMap.put("offset", offset);
            paramMap.remove("pageNum");
        }
    }
    public static AjaxResult getDataTableNoPage(Object data) {
        AjaxResult rspData = new AjaxResult();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setData(data);
        return rspData;
    }
    public static PageResult getDataTableCustomPagination(List<?> list,Long count) {
        PageResult rspData = new PageResult();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setData(list);
        rspData.setTotal(count);
        return rspData;
    }
    /**
     * 设置请求分页数据
     */
    public static void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageMethod.startPage(pageNum, pageSize, orderBy);
        }
    }
}
