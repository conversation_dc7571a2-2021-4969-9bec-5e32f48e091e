/**
 * @Title: TreeUtils.java
 * @Package cn.cmcc.common.utils
 * 
 * <AUTHOR>
 * @date 2022年3月9日
 * @version V1.0
 */
package cn.cmcc.common.utils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.cmcc.common.pojo.TreeSelect;


public class TreeUtils {
	
	public static List<TreeSelect> buildTree(List<TreeSelect> dataList) {
		List<TreeSelect> returnList = new ArrayList<TreeSelect>();
        List<Long> idList = dataList.stream().map(TreeSelect::getId).collect(Collectors.toList());
        for (Iterator<TreeSelect> iterator = dataList.iterator(); iterator.hasNext();)
        {
        	TreeSelect data = (TreeSelect) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!idList.contains(data.getPid()))
            {
                recursionFn(dataList, data);
                returnList.add(data);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = dataList;
        }
        return returnList;
	}
	
	private static void recursionFn(List<TreeSelect> list, TreeSelect t)
    {
        // 得到子节点列表
        List<TreeSelect> childList = getChildList(list, t);
        t.setChildren(childList);
        for (TreeSelect tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
	
	/**
     * 得到子节点列表
     */
    private static List<TreeSelect> getChildList(List<TreeSelect> list, TreeSelect t)
    {
        List<TreeSelect> tlist = new ArrayList<TreeSelect>();
        Iterator<TreeSelect> it = list.iterator();
        while (it.hasNext())
        {
        	TreeSelect n = (TreeSelect) it.next();
            if (n.getPid() != null && ( n.getPid().longValue() == t.getId().longValue()))
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private static boolean hasChild(List<TreeSelect> list, TreeSelect t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
    
    
    /**
     * 
     * tree构造，map必须包含，id和pid  且根节点id必须为0
     *
     * @Title: buildTree   
     * @param dataList
     * @return List<Map<String,Object>>
     * @throws
     */
    public static List<Map<String, Object>> buildMapTree(List<Map<String, Object>> dataList) {
    	return dataList.stream().filter(s->Objects.equals(s.get("pid").toString(),"0")).map(
    		    (m) -> {
    		        m.put("children",getChildrenList(m, dataList));
    		        return m;
    	}).collect(Collectors.toList());
    }
    
    
    public static List<Map<String, Object>> getChildrenList(Map<String, Object> parent, List<Map<String, Object>> list){
        return list.stream().filter(item -> Objects.equals(item.get("pid").toString(), parent.get("id").toString())).map(
                (item) -> {
                    item.put("children",getChildrenList(item, list));
                    return item;
                }
        ).collect(Collectors.toList());
    }
}
