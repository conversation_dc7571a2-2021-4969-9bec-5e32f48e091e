package cn.cmcc.kernel.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 权限查询响应 DTO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
public class PermissionQueryResponse {

    /**
     * 权限ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色标识
     */
    private String roleKey;

    /**
     * 查询编码
     */
    private String queryCode;

    /**
     * 权限类型
     */
    private String permissionType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 构造函数
     */
    public PermissionQueryResponse() {
    }

    /**
     * 用户权限响应
     */
    @Data
    public static class UserPermissionResponse {
        private String username;
        private List<String> queryCodes;
        private String permissionType;
        private int totalCount;

        public UserPermissionResponse(String username, List<String> queryCodes, String permissionType) {
            this.username = username;
            this.queryCodes = queryCodes;
            this.permissionType = permissionType;
            this.totalCount = queryCodes != null ? queryCodes.size() : 0;
        }
    }

    /**
     * 角色权限响应
     */
    @Data
    public static class RolePermissionResponse {
        private String roleKey;
        private List<String> queryCodes;
        private String permissionType;
        private int totalCount;

        public RolePermissionResponse(String roleKey, List<String> queryCodes, String permissionType) {
            this.roleKey = roleKey;
            this.queryCodes = queryCodes;
            this.permissionType = permissionType;
            this.totalCount = queryCodes != null ? queryCodes.size() : 0;
        }
    }

    /**
     * 查询编码权限统计响应
     */
    @Data
    public static class CodePermissionStatsResponse {
        private String queryCode;
        private List<String> users;
        private List<String> roles;
        private int userCount;
        private int roleCount;

        public CodePermissionStatsResponse(String queryCode, List<String> users, List<String> roles) {
            this.queryCode = queryCode;
            this.users = users;
            this.roles = roles;
            this.userCount = users != null ? users.size() : 0;
            this.roleCount = roles != null ? roles.size() : 0;
        }
    }
}
