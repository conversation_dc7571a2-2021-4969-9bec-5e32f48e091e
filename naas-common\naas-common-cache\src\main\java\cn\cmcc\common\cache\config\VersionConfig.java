package cn.cmcc.common.cache.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.cache.utils.RedisUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**  
 * 
 * 模块版本
 * 
 * <AUTHOR>
 * @date 2024-04-25 02:47:20
*/

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "build", ignoreUnknownFields = false)
@PropertySource(value= "classpath:version.properties",encoding = "utf-8")
@Data
@Component
public class VersionConfig implements CommandLineRunner{
	
	/**
     * 版本
     */
    private String version;
    
    @Value("${spring.application.name}")
	private String appName;

	@Override
	public void run(String... args) throws Exception {
		log.info("{}编译版本日期：{}",version,appName);
    	RedisUtils.setCacheObject(CacheConstants.CacheKey.CACHE_MODULE_VERSION + appName, version);
	}
}
