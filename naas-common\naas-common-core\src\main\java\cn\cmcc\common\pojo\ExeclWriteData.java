/**
 * @Title: ExeclWriteData.java
 * @Package cn.cmcc.common.pojo
 * 
 * <AUTHOR>
 * @date 2022年1月10日
 * @version V1.0
 */
package cn.cmcc.common.pojo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.write.handler.SheetWriteHandler;

import com.alibaba.excel.write.handler.WriteHandler;
import lombok.Data;

@Data
public class ExeclWriteData implements Serializable{

	/**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	private String fileName;//文件名
	private String sheetName; //sheet名

	private List<List<String>> dataList; //数据集合
	private List<List<String>> headList; //表头集合

	private List<String> headMap;//表头数组
	private List<String> dataStrMap;//对应数据字段数组
	private List<Map<String, Object>> dataMapList; //数据集合

    private SheetWriteHandler sheetWriteHandler;

	private List<WriteHandler> sheetWriteHandlers;
}
