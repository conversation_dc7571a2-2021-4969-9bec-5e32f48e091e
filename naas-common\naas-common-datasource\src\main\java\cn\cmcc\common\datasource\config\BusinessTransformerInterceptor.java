package cn.cmcc.common.datasource.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import net.sf.jsqlparser.JSQLParserException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static cn.cmcc.common.context.BusinessTransformerContext.Common.specificTransformerKey;

/**
 * @Description:
 * @author: niuyh
 * @date: 2024/1/15
 */

@SuppressWarnings("rawtypes")
@Component
@Order
@Intercepts(
        {
                @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class}),
                @Signature(type = StatementHandler.class, method = "getBoundSql", args = {}),
                @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        }
)
public class BusinessTransformerInterceptor implements Interceptor {
    public static Map<String, BusinessTransformer> transformers = new LinkedHashMap<>();


    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Collection<BusinessTransformer> collection = transformers.values();
        if (CollectionUtils.isEmpty(collection)) {
            return invocation.proceed();
        }

        for (BusinessTransformer transformer : collection) {
            String key = transformer.getKey();
            if (key.equalsIgnoreCase(BusinessTransformer.KEY_GENERAL) || key.equalsIgnoreCase(specificTransformerKey.get())) {
                transformer.interceptInner(invocation);
            }
        }
        return invocation.proceed();
    }

    /**
     * @Description:
     * @author: niuyh
     * @date: 2024/1/15
     */
    @SuppressWarnings("unused")
    public interface BusinessTransformer {

        String KEY_GENERAL = "general";

        @NonNull
        String getKey();
        /**
         * @param invocation
         * @see MybatisPlusInterceptor#intercept(Invocation)
         */
        default void interceptInner(Invocation invocation) throws SQLException, InvocationTargetException, IllegalAccessException {
            Object target = invocation.getTarget();
            Object[] args = invocation.getArgs();
            if (target instanceof Executor) {
                final Executor executor = (Executor) target;
                Object parameter = args[1];
                boolean isUpdate = args.length == 2;
                MappedStatement ms = (MappedStatement) args[0];
                if (!isUpdate && ms.getSqlCommandType() == SqlCommandType.SELECT) {
                    RowBounds rowBounds = (RowBounds) args[2];
                    ResultHandler resultHandler = (ResultHandler) args[3];
                    BoundSql boundSql;
                    if (args.length == 4) {
                        boundSql = ms.getBoundSql(parameter);
                    } else {
                        // 几乎不可能走进这里面,除非使用Executor的代理对象调用query[args[6]]
                        boundSql = (BoundSql) args[5];
                    }
                    beforeQuery(executor, ms, parameter, rowBounds, resultHandler, boundSql);
                } else if (isUpdate) {
                    beforeUpdate(executor, ms, parameter);
                }
            } else {
                // StatementHandler
                final StatementHandler sh = (StatementHandler) target;
                // 目前只有StatementHandler.getBoundSql方法args才为null
                if (null == args) {
                    beforeGetBoundSql(sh);
                } else {
                    Connection connections = (Connection) args[0];
                    Integer transactionTimeout = (Integer) args[1];
                    beforePrepare(sh, connections, transactionTimeout);
                }
            }
        }

        default void beforePrepare(StatementHandler sh, Connection connections, Integer transactionTimeout) {
        }


        default void beforeGetBoundSql(StatementHandler sh) {
        }


        default void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) {
        }

        default void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        }

        default void register() {
            transformers.put(getClass().getCanonicalName(), this);
        }
    }
}
