-- 创建通用查询编码权限表
CREATE TABLE IF NOT EXISTS t_query_code_permission (
    id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    query_code VARCHAR(200) NOT NULL COMMENT '查询编码',
    permission_type VARCHAR(20) DEFAULT 'READ' COMMENT '权限类型：read-查询，write-修改',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(64) COMMENT '创建人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(64) COMMENT '更新人',
    remark VARCHAR(500) COMMENT '备注'
);

-- 创建索引
CREATE INDEX idx_query_code_permission_user ON t_query_code_permission(username);
CREATE INDEX idx_query_code_permission_code ON t_query_code_permission(query_code);
CREATE UNIQUE INDEX uk_query_code_permission ON t_query_code_permission(username, query_code, permission_type);

-- 创建角色-查询编码权限表
CREATE TABLE IF NOT EXISTS t_role_query_code_permission (
    id VARCHAR(64) PRIMARY KEY,
    role_id VARCHAR(64) NOT NULL COMMENT '角色ID',
    role_key VARCHAR(100) NOT NULL COMMENT '角色标识',
    query_code VARCHAR(200) NOT NULL COMMENT '查询编码',
    permission_type VARCHAR(20) DEFAULT 'read' COMMENT '权限类型',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(64) COMMENT '创建人'
);

-- 创建索引
CREATE INDEX idx_role_query_code_permission_role ON t_role_query_code_permission(role_key);
CREATE INDEX idx_role_query_code_permission_code ON t_role_query_code_permission(query_code);
CREATE UNIQUE INDEX uk_role_query_code_permission ON t_role_query_code_permission(role_key, query_code, permission_type);

-- 初始化基础权限：BASE_PRI_ROLE角色拥有所有现有查询编码的权限
INSERT INTO t_role_query_code_permission (id, role_id, role_key, query_code, permission_type, create_by)
SELECT
    REPLACE(UUID(), '-', '') as id,
    '1' as role_id,
    'BASE_PRI_ROLE' as role_key,
    code as query_code,
    'read' as permission_type,
    'system' as create_by
FROM t_common_query
WHERE code IS NOT NULL AND code != ''
ON DUPLICATE KEY UPDATE update_time = CURRENT_TIMESTAMP;
