package cn.cmcc.common.utils.thread;

import java.util.Random;
import java.util.concurrent.*;

import cn.cmcc.common.utils.uuid.IdUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * Description:符合阿里巴巴规范的线程池
 * 
 * <AUTHOR>
 */
public class ThreadPoolUtil {

	public static ThreadPoolExecutor threadPool;

	public static int getProcessors(int factor) {
		int availProcessors = Runtime.getRuntime().availableProcessors();
		int processors = availProcessors / factor;
		if (processors == 0) {
			processors = 1;
		}
		return processors;
	}

	public static int getProcessorSize(int needSize) {
		// 返回 两者中的 较小的值 返回值含义 processorSize
		return Math.min(needSize, getProcessors(2));
	}

	public static int getProcessorSize(int needSize, int maxSize) {
		return Math.min(maxSize, needSize);
	}

	/**
	 * 无返回值直接执行
	 *
	 * @param runnable
	 *            实现线程的Runnable 接口
	 */
	public static void execute(Runnable runnable) {
		getThreadPool().execute(runnable);
	}

	/**
	 * 有返回值直接执行
	 *
	 * @param runnable
	 *            实现线程的Runnable 接口
	 */
	public static Future<?> submit(Runnable runnable) {
		return getThreadPool().submit(runnable);
	}

	/**
	 * 返回值直接执行
	 *
	 * @param callable
	 *            实现Callable接口然后重写call方法
	 */
	public static <T> Future<T> submit(Callable<T> callable) {
		return getThreadPool().submit(callable);
	}

	/**
	 * dcs获取线程池
	 *
	 * @return 线程池对象
	 */
	public static ThreadPoolExecutor getThreadPool() {
		ThreadFactory threadFactory = new ThreadFactoryBuilder().setDaemon(true).setNameFormat("自定义线程池-"+IdUtils.randomId(3) +"-%d").build();
		if (threadPool != null) {
			return threadPool;
		} else {
			synchronized (ThreadPoolUtil.class) {
				if (threadPool == null) {
					ArrayBlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(2000);
					threadPool = new TraceThreadPoolTaskExecutor(100, 300, 60, TimeUnit.SECONDS, queue, threadFactory,new CustomRejectedExecutionHandler());
					threadPool.allowCoreThreadTimeOut(true);
				}
				return threadPool;
			}
		}
	}
	
	@Slf4j
	 public static class CustomRejectedExecutionHandler implements RejectedExecutionHandler {    
	        @Override    
	        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {    
	            try {  
	                /**
	                 * add(E e)：在容量不足时，抛出异常。
	                 * put(E e)：在容量不足时，阻塞等待。
	                 * offer(E e)：不阻塞，容量不足时返回false，当前新增数据操作放弃
	                 * offer(E e, long timeout, TimeUnit unit）：容量不足时，阻塞times时长（单位为timeunit），如果在阻塞时长内，有容量空闲，新增数据返回true。如果阻塞时长范围内，无容量空闲，放弃新增数据，返回false。
	                 */
	            	
	            	log.info("PUT_PRE==线程池队列大小：{},ActiveCount:{}",executor.getQueue().size(),executor.getActiveCount());
	                executor.getQueue().put(r);  
	            } catch (InterruptedException e) {  
	            	log.error(e.getMessage(),e);
	                Thread.currentThread().interrupt();
	            }  
	        }    
	}
}
