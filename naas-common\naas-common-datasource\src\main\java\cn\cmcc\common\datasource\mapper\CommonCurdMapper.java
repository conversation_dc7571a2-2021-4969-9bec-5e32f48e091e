/**
 * @Title: TCommonCurdMapper.java
 * @Package cn.cmcc.base.mapper
 * <AUTHOR>
 * @date 2022年1月20日
 * @version V1.0
 */
package cn.cmcc.common.datasource.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.tuple.Triple;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectKey;

import cn.cmcc.common.datasource.pojo.bo.FieldTypeValueBO;

@Mapper
public interface CommonCurdMapper {

    int uniqueCheck(String tableName, List<Map<String, Object>> params);

    int save(String tableName, Map<String, Object> param);

    int update(String tableName, Map<String, Object> param, String privimaryKey, Object privimaryKeyValue);

    int updateByMultiField(String tableName, Map<String, Object> param, Map<String, Object> whereParam);

    int delete(String tableName, String privimaryKey, List<Object> list);

    Map<String, Object> detail(String tableName, String privimaryKey, String privimaryKeyValue);

    /**
     *
     * @Title: saveBatch
     * @Description: 批量保存
     * @param: tableName
     * @param: fields
     * @param: dataList
     * @param: @return
     * @return: int
     * @throws
     */
    int saveBatchType(String tableName, Map<String, FieldTypeValueBO> fields, List<Map<String, FieldTypeValueBO>> dataList);

    /**
     *
     * @Title: saveBatch
     * @Description: 批量保存
     * @param: tableName
     * @param: fields
     * @param: dataList
     * @param: @return
     * @return: int
     * @throws
     */
    int insertDynamicFieldsAndValues(String tableName, Map<String, Object> fields, List<Map<String, Object>> dataList);

    /**
     * 保存批量数据
     *
     * @param tableName 表名
     * @param fields    字段
     * @param dataList  数据列表
     * @return 保存行数
     */
    int insertStaticFieldsFromObjects(String tableName, Set<String> fields, List<Map<String, Object>> dataList);

    /**
     *
     * @Title: deleteBatch
     * @Description: 删除数据
     * @param: @param tableName
     * @param: @param dataList
     * @param: @return
     * @return: int
     * @throws
     */
    int deleteBatch(String tableName, Map<String, List<String>> dataList);

    /**
     * 多字段关联删除
     *
     * @param tableName
     * @param fieldValueMap
     * @return
     */
    int deleteByMultiField(String tableName, Map<String, Object> fieldValueMap);

    /**
     *
     * @Title: deleteBatchByField
     * @Description: TODO(描述这个方法的作用)
     * @param: @param tableName
     * @param: @param fieldName
     * @param: @param dataList
     * @param: @return
     * @return: int
     * @throws
     */
    int deleteBatchByField(String tableName, String fieldName, Collection<String> dataList);

    /**
     * 根据临时表删除数据
     *
     * @param tableName
     * @param tmpTable
     * @param uniqueKeys
     * @return
     */
    int deleteBatchByTempTable(String tableName ,String tmpTable,Set<String> uniqueKeys);

    /**
     *
     * @Title: qryRepeatList
     * @Description: 基于唯一查询重复
     * @param: @param tableName
     * @param: @param primaryKey
     * @param: @param orderNo
     * @param: @param dataList
     * @param: @return
     * @return: List<Map<String,Object>>
     * @throws
     */
    List<Map<String, Object>> qryRepeatList(String tableName, String primaryKey, String orderNo, Map<String, List<String>> dataList);

    /**
     *
     * 清空表
     *
     * @Title: clearTable
     * @param tableName void
     * @throws
     */
    void clearTable(String tableName);

    /**
     *
     * 居于原表创建临时表
     *
     * @Title: createTempTable
     * @param table
     * @param tmpTable
     * @param List<String> fieldList
     * @return int
     * @throws
     */
    int createTempTable(String table, String tmpTable, Collection<String> fieldList);

    /**
     *
     * 查询重复
     *
     * @Title: selectRepeatFromTempTable
     * @param table
     * @param tmpTable
     * @param uniqueKey
     * @return List<String>
     * @throws
     */
    List<String> selectRepeatFromTempTable(String table, String tmpTable, Set<String> uniqueKeys);

    /**
     * 从临时表插入到实际表
     *
     * @param table
     * @param tmpTable
     * @return long
     * @throws
     * @Title: insertTableFromTempTable
     */
    long insertTableFromTempTable(String table, String tmpTable, Set<String> uniqueKeys, String fields);

/**
     * 批量处理
     *
     * @param insert             是否插入
     * @param table              表
     * @param key                主键
     * @param fieldAttributeType 字段属性类型
     * @param list               列表
     * @return 更新行数
     */
    int batching(boolean insert, String table, String key, List<Triple<String, String, Integer>> fieldAttributeType, List<?> list);

    /**
     * 根据临时表更新数据
     *
     * @param tableName
     * @param tmpTable
     * @param updateByField
     * @param updateFields
     * @return
     */
    long updateByFieldNameFromTempTable(String tableName, String tmpTable, Set<String> uniqueKeys, Set<Map<String, String>> updateFields);

    List<Map<String, String>> selectColumnsType(String tableScheme, String tableName);

    int removeRequiredIsNull(String tempTableName, Set<String> requiredSet);

    int deleteRepeData(String tempTableName, String originalTableName, List<Map<String, String>> uniKeyColumns);

    @SelectKey(statement = "SELECT ROW_COUNT() AS count", keyProperty = "count", before = false, resultType = Long.class)
    Long insertOriginalTableFromTextTempTable(String tempTableName, String originalTableName, List<Map<String, String>> columns,Boolean quote);

    List<Map<String, Object>> selectInsertFailedDataFromTempTable(String tempTableName, String originalTableName, List<Map<String, String>> columns, List<Map<String, String>> uniKeyColumns);

    int deleteTempTable(String tempTableName);

    /**
     * <AUTHOR>  2024/9/20 上午11:43
     * @description: 根据不等于该列的值删除
     * @param tableName 表名
     * @param fieldName 字段名
     * @param columnValue 该字段对应的值
     * @return 返回删除数据条数
     */
    int deleteByNotEqColumnValue(String tableName,String fieldName,String columnValue);

    List<Map<String, Object>> selectRepetitionFromTempTable(Map<String, Object> params);

    long deleteNotUserCityData(String tableName,String key,Long userId);
	
	//根据主键查询数据是否存在
    Integer findCountByKey(@Param("tableName") String tableName, @Param("idField") String idField, @Param("idValue") String idValue);
}
