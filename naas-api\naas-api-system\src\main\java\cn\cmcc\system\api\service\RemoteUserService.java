package cn.cmcc.system.api.service;

import java.util.List;
import java.util.Map;

import cn.cmcc.common.pojo.LoginUser;
import cn.cmcc.system.api.pojo.dto.SysMenuDto;
import cn.cmcc.system.api.pojo.dto.SysUserDto;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    public LoginUser getUserInfo(String account);
    
    /**
     * oauth2登录，检查用户是否存在，不存在则创建用户
     * 
     * @param sysUserDto 
     * @param resources
     * void
     */
    public void checkInitUser(SysUserDto sysUserDto,List<String> resources);
    
    /**
     * 
     * 多个用户,分割
     *
     * @Title: getDealUser   
     * @param account
     * @return LoginUser
     * @throws
     */
    public LoginUser getDealUser(String account);
    
    /**
     * 
     * 获取用户菜单   
     *
     * @Title: selectMenuTrees   
     * @param userId
     * @param belongSystem
     * @return List<SysMenuDto>
     * @throws
     */
    public List<SysMenuDto> selectMenuTrees(Long userId,String belongSystem);
    
    /**
     * 
     * 根据角色，管理返公司，获取用户列表
     *
     * @Title: selectUserByRoleCompany   
     * @param role
     * @param cityCompany
     * @param areaCompany
     * @return List<SysUserDto>
     * @throws
     */
    public List<SysUserDto> selectUserByRoleCompany(String role,String cityCompany,String areaCompany);
    
    /**
     * 
     * 根据用户id用户用户信息
     *
     * @Title: selectUserListByUserIds   
     * @param userIds
     * @return List<SysUserDto>
     * @throws
     */
    public List<SysUserDto> selectUserListByUserIds(List<Long> userIds);
    
    /**
     * 查询所有用户
     * @return Map<Long,String>
     * @date 2023-10-09 05:15:40
     */
    public Map<Long, String> list();
    
    public Integer changeUserStutus(String account,String status);
    //    根据用户ID查询用户
    LoginUser findByUserId(Long userId);

    /**
     * 根据ID获取用户信息
     * @param userId
     * @return
     */
    public SysUserDto getUserInfo(Long userId);
}
