package cn.cmcc.common.utils.http;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.net.ssl.SSLContext;

import org.apache.commons.collections4.MapUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.file.FileUtils;
import cn.cmcc.common.utils.http.bo.HttpContextHolder;
import cn.cmcc.common.utils.http.bo.HttpContextHolder.RequestConfig;
import cn.cmcc.common.utils.http.bo.HttpReqParam;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.http.HttpConnection;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * HTTP 请求工具类
 *
 * <AUTHOR>
 *
 */

@Slf4j
public class HttpClientUtils {

	private static CustomHttpRequestFactory requestFactory;
	private static RestTemplate client;

	static {
			SSLContext sslContext;
			try {
				sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				    @Override
				    public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
				        return true;
				    }
				}).build();


		        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext,
		                new String[]{"TLSv1","TLSv1.1", "TLSv1.2"},
		                null,
		                NoopHostnameVerifier.INSTANCE);

		        CloseableHttpClient httpClient = HttpClients.custom()
		                .setSSLSocketFactory(csf)
		                .build();

				requestFactory = new CustomHttpRequestFactory();
				requestFactory.setConnectTimeout(120000);
				requestFactory.setReadTimeout(120000);
				requestFactory.setHttpClient(httpClient);
				client = new RestTemplate(requestFactory);
			} catch (KeyManagementException | KeyStoreException e) {
				log.error(e.getMessage(),e);
			} catch (NoSuchAlgorithmException e) {
				log.error(e.getMessage(),e);
			}

    }
	public static AjaxResult sendPost(String url, String params,Integer connectTimeout,Integer readTimeout) {
		return send(HttpReqParam.builder(url, params, HttpMethod.POST,connectTimeout,readTimeout).build());
	}
	public static AjaxResult send(String url, String params, HttpMethod method) {
		return send(HttpReqParam.builder(url, params, method).build());
	}

	public static AjaxResult sendPost(String url, String params) {
		return send(HttpReqParam.builder(url, params, HttpMethod.POST).build());
	}

	public static AjaxResult sendGet(String url) {
		return send(HttpReqParam.builder(url, HttpMethod.GET).build());
	}

	public static AjaxResult send(HttpReqParam httpReqParam) {
		try {
			HttpEntity<String> requestEntity = new HttpEntity<String>(httpReqParam.getParams(),
					httpReqParam.getHeaders());

			if (httpReqParam.getConnectTimeout() != null && httpReqParam.getReadTimeout() != null) {
				HttpContextHolder.set(RequestConfig.builder().connectTimeout(httpReqParam.getConnectTimeout())
						.readTimeout(httpReqParam.getReadTimeout()).build());
			}
			log.info("开始请求第三方服务,请求地址:{},请求方式:{},\n请求报文:{}\n请求头:{},",httpReqParam.getUrl(),httpReqParam.getMethod(),httpReqParam.getParams(),httpReqParam.getHeaders());
			ResponseEntity<String> response = client.exchange(httpReqParam.getUrl(), httpReqParam.getMethod(), requestEntity, String.class, new Object[0]);
			log.info("请求地址:{},请求结果：code:{},data:{}",httpReqParam.getUrl(),response.getStatusCodeValue(), null);
			return new AjaxResult(response.getStatusCodeValue(), "", response.getBody());
		} catch (Exception var8) {
			log.info("请求地址:{},请求异常：{}",httpReqParam.getUrl(), var8.getMessage());
			return AjaxResult.error("请求异常", var8.getMessage());
		} finally {
			HttpContextHolder.remove();
		}
	}

	public static AjaxResult sendFormUrlEncode(String url, Map<String, Object> params,
			Map<String, String> headerParam) {
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

			if (MapUtils.isNotEmpty(headerParam)) {
				headerParam.forEach((k, v) -> {
					if (StringUtils.isNotEmpty(k) && StringUtils.isNotEmpty(v)) {
						headers.add(k, v);
					}
				});
			}

			MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
			params.forEach((k, v) -> {
				if (StringUtils.isNotEmpty(k)) {
					postParameters.add(k, v);
				}
			});

			HttpEntity<MultiValueMap<String, Object>> r = new HttpEntity<>(postParameters, headers);
			log.info("请求地址:{},请求参数：{}", url, postParameters);
			ResponseEntity<String> response = client.exchange(url, HttpMethod.POST, r, String.class, new Object[0]);
			log.info("请求结果,code:{},data:{}", response.getStatusCodeValue(), response.getBody());
			return new AjaxResult(response.getStatusCodeValue(), "", response.getBody());
		} catch (Exception e) {
			log.info("请求异常：{}", e.getMessage());
			return AjaxResult.error("请求异常", e.getMessage());
		}
	}

	/**
	 * 下载文件到本地
	 *
	 * @param httpUrl       被下载的文件地址
	 * @param localFilename 本地文件名
	 * @throws Exception 各种异常
	 */
	public static Boolean download(String httpUrl, String localFilename) {
		try {
			/**
			 * url 中有中文，需要编码
			 */
			List<String> urls = Arrays.asList(httpUrl.split("/"));
			urls = urls.stream().map(s -> {
				if (StringUtils.isNotEmpty(s) && StringUtils.isContainChinese(s)) {
					try {
						return URLEncoder.encode(s, "UTF-8");
					} catch (UnsupportedEncodingException e) {
						log.error(e.getMessage(),e);
					}
				}
				return s;
			}).collect(Collectors.toList());

			httpUrl = StringUtils.join(urls, "/");

			File file = FileUtils.getFile(localFilename);
			File fileParent = file.getParentFile();
			if (!fileParent.exists()) {
				fileParent.mkdirs();
			}

			if (file.exists() && file.delete()) {
			}
			HttpUtil.downloadFile(httpUrl, localFilename);
			return true;
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			return false;
		}
	}

	/**
	 * 获取重定向返回的地址
	 *
	 * @param url
	 * @return
	 * @throws IOException
	 * String
	 */
	public static String getRedirectUrl(String url) throws Exception {
		HttpURLConnection conn = null;
		try {
			conn = HttpConnection.create(url, null).getHttpURLConnection();
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			// 设置跟随重定向
			conn.setInstanceFollowRedirects(false);
			conn.setRequestMethod("GET");
			conn.setConnectTimeout(5000);
			conn.setReadTimeout(5000);

			conn.connect();
			// 检查响应代码是否为HTTP重定向 (301, 302)
			int status = conn.getResponseCode();
			if (status == HttpURLConnection.HTTP_MOVED_PERM || status == HttpURLConnection.HTTP_MOVED_TEMP) {
				// 获取重定向的URL
				return conn.getHeaderField("Location");
			}
			return StringUtils.EMPTY;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return StringUtils.EMPTY;
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
		}
	}

	/**
	 * 自定义 支持设置每个请求的超时时间
	 */
	public static class CustomHttpRequestFactory extends HttpComponentsClientHttpRequestFactory  {

		@Override
		public void setReadTimeout(int timeout) {
			RequestConfig config = HttpContextHolder.get();
			if (config != null && config.getReadTimeout() != null && config.getReadTimeout() >= 0) {
				super.setReadTimeout(config.getReadTimeout());
			}else {
				super.setReadTimeout(timeout);
			}
		}

		@Override
		public void setConnectTimeout(int timeout) {
			RequestConfig config = HttpContextHolder.get();
			if (config != null && config.getConnectTimeout() != null && config.getConnectTimeout() >= 0) {
				super.setConnectTimeout(config.getConnectTimeout());
			}else {
				super.setConnectTimeout(timeout);
			}
		}
	}

	public static void main( String args[]) {
		AjaxResult ajr = HttpClientUtils.send(HttpReqParam.builder("https://wechat.xhxk.cc/wechat_bs/getInfo", HttpMethod.GET)
				.addHeaderParam("Authorization", "Bearer eyJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************************************************.f9ZzIAJyx6hA4MXHccxjOcNjE2HZw1ZFe8pZ2mIxnBvEk266aEYtMQg_Bg8LPWKcM-Zr1Otk7tGTKAid7p-6Jg")
				.build());
		System.out.println(ajr);
	}
}
