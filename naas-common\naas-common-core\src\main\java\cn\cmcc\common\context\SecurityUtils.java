package cn.cmcc.common.context;

import java.util.List;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import org.springframework.http.HttpCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.lang.Nullable;
import org.springframework.util.MultiValueMap;

import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.pojo.LoginUser;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.WebUtils;

/**
 * 权限获取工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils
{
    /**
     * 获取用户ID
     */
    public static Long getUserId() {
        return SecurityUtils.getLoginUser().getUserId();
    }

    /**
     * 获取用户名称
     */
    public static String getUserName() {
        return SecurityUtils.getLoginUser().getUserName();
    }

    /**
     * 获取用户省份
     */
    @Nullable
    public static String getProvinceCode() {
        LoginUser loginUser = getLoginUser();
        if (loginUser == null) {
            loginUser = new LoginUser();
        }
        return loginUser.getProvinceCode();
    }

    /**
     * 获取用户key
     */
    public static String getTokenUuid() {
        return SecurityContextHolder.getTokenUuid();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginUser getLoginUser()
    {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
    }

    /**
     * 设置登录用户信息
     */
    public static void setLoginUser(LoginUser loginUser)
    {
    	SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
    }

    /**
     * clear
     */
    public static void remove() {
    	SecurityContextHolder.remove();
    }

    /**
     * 获取请求token
     */
    public static String getToken()
    {
        return getToken(WebUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request)
    {
        // 从header获取token标识
        String token = request.getHeader(SecurityConstants.AUTHENTICATION);
        if (StringUtils.isEmpty(token)){
        	Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    if (cookie.getName().contains(SecurityConstants.COOKIE_TOKEN_KEY)) {
                    	token = cookie.getValue();
                    	break;
                    }
                }
            }
        }
        return replaceTokenPrefix(token);
    }

    public static String getToken(ServerHttpRequest request) {
    	String token = request.getHeaders().getFirst(SecurityConstants.AUTHENTICATION);
    	if (StringUtils.isEmpty(token)){
    		MultiValueMap<String, HttpCookie> cookieMap = request.getCookies();
            if (cookieMap != null) {
                for (String key : cookieMap.keySet()) {
                	List<HttpCookie> cookies = cookieMap.get(key);
                	for (HttpCookie httpCookie : cookies) {
                		if (httpCookie.getName().contains(SecurityConstants.COOKIE_TOKEN_KEY)) {
                			token = httpCookie.getValue();
                        	break;
                        }
					}
                    
                }
            }
        }
    	return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token)
    {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(SecurityConstants.PREFIX))
        {
            token = token.replaceFirst(SecurityConstants.PREFIX, "");
        }
        return token;
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }
}
