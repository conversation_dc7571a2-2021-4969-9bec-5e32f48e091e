package cn.cmcc.common.exception;

import org.apache.commons.lang3.StringUtils;

/**
 * 自定义异常
 * 
 * <AUTHOR>
 */
public class CustomException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    private String message;
    
    private Integer code;
    
    private Object data;

    public CustomException(String message)
    {
        this.message = message;
    }
    
    public CustomException(Integer code, String message,Object data)
    {
    	this.code = code;
        this.message = message;
        this.data = data;
    }
    
    public CustomException(String[] msgs) {
    	 super(StringUtils.join(msgs, ","));
    }

    public CustomException(String message, Throwable e)
    {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage()
    {
        return message;
    }
    

	public Object getData() {
		return data;
	}

	public Integer getCode() {
		return code;
	}
}
