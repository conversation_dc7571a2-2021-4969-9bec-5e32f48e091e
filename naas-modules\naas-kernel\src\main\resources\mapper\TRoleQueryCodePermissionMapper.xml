<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.cmcc.kernel.mapper.TRoleQueryCodePermissionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.cmcc.kernel.domain.TRoleQueryCodePermission">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="VARCHAR"/>
        <result column="role_key" property="roleKey" jdbcType="VARCHAR"/>
        <result column="query_code" property="queryCode" jdbcType="VARCHAR"/>
        <result column="permission_type" property="permissionType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, role_id, role_key, query_code, permission_type, create_time, create_by
    </sql>

    <!-- 根据角色标识查询角色的所有权限 -->
    <select id="selectByRoleKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role_query_code_permission
        WHERE role_key = #{roleKey}
        ORDER BY create_time DESC
    </select>

    <!-- 根据角色标识列表查询权限 -->
    <select id="selectByRoleKeys" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role_query_code_permission
        WHERE role_key IN
        <foreach collection="roleKeys" item="roleKey" open="(" separator="," close=")">
            #{roleKey}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <!-- 根据查询编码查询所有有权限的角色 -->
    <select id="selectByQueryCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role_query_code_permission
        WHERE query_code = #{queryCode}
        ORDER BY create_time DESC
    </select>

    <!-- 根据角色标识和查询编码查询特定权限 -->
    <select id="selectByRoleKeyAndCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role_query_code_permission
        WHERE role_key = #{roleKey} AND query_code = #{queryCode}
        LIMIT 1
    </select>

    <!-- 根据角色标识和查询编码查询特定权限（指定权限类型） -->
    <select id="selectByRoleKeyAndCodeAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role_query_code_permission
        WHERE role_key = #{roleKey} 
          AND query_code = #{queryCode} 
          AND permission_type = #{permissionType}
        LIMIT 1
    </select>

    <!-- 根据角色标识和查询编码删除特定权限 -->
    <delete id="deleteByRoleKeyAndCode">
        DELETE FROM t_role_query_code_permission
        WHERE role_key = #{roleKey} AND query_code = #{queryCode}
    </delete>

    <!-- 根据角色标识和查询编码删除特定权限（指定权限类型） -->
    <delete id="deleteByRoleKeyAndCodeAndType">
        DELETE FROM t_role_query_code_permission
        WHERE role_key = #{roleKey} 
          AND query_code = #{queryCode} 
          AND permission_type = #{permissionType}
    </delete>

    <!-- 根据角色标识列表和查询编码查询权限 -->
    <select id="selectByRoleKeysAndCodeAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role_query_code_permission
        WHERE role_key IN
        <foreach collection="roleKeys" item="roleKey" open="(" separator="," close=")">
            #{roleKey}
        </foreach>
        AND query_code = #{queryCode}
        AND permission_type = #{permissionType}
    </select>

    <!-- 获取角色可访问的所有查询编码 -->
    <select id="selectAccessibleCodesByRoleKey" resultType="java.lang.String">
        SELECT DISTINCT query_code
        FROM t_role_query_code_permission
        WHERE role_key = #{roleKey} AND permission_type = #{permissionType}
        ORDER BY query_code
    </select>

    <!-- 批量插入角色权限 -->
    <insert id="batchInsert">
        INSERT INTO t_role_query_code_permission (id, role_id, role_key, query_code, permission_type, create_time, create_by)
        VALUES
        <foreach collection="permissions" item="item" separator=",">
            (#{item.id}, #{item.roleId}, #{item.roleKey}, #{item.queryCode}, #{item.permissionType}, NOW(), #{item.createBy})
        </foreach>
    </insert>

    <!-- 批量删除角色权限 -->
    <delete id="batchDeleteByRoleKeyAndCodes">
        DELETE FROM t_role_query_code_permission
        WHERE role_key = #{roleKey}
        AND query_code IN
        <foreach collection="queryCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>

</mapper>
