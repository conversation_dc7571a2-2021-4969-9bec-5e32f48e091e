package cn.cmcc.common.datasource.service;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.google.common.collect.Maps;

import cn.cmcc.common.datasource.mapper.CommonCurdMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.sql.Types;
import java.util.*;

/**
 * <p>
 * Postgres batch service
 * Dependencies: MyBatis Plus
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@SuppressWarnings("unchecked")
@Service
@Slf4j
@RequiredArgsConstructor
public class PGBatchService {
    public static final Integer BATCH_SAVE_SIZE = 50;
    private final NamedParameterJdbcTemplate namedTemplate;
    private final CommonCurdMapper curdMapper;

    /**
     * 获取表字段
     *
     * @param tables 表
     * @return 表名|字段名|字段类型
     */
    public Map<String, Map<String, Integer>> getColumnListFromTables(String... tables) {
        List<String> columnTypeList = namedTemplate.queryForList("SELECT table_name || '|' || LOWER(column_name) || '|' || data_type FROM information_schema.\"columns\" WHERE table_name IN (:tables)",
                Collections.singletonMap("tables", Arrays.asList(tables)), String.class);

        Map<String, Map<String, Integer>> columnTypes = new HashMap<>(tables.length, 1);
        columnTypeList.forEach(it -> {
            String[] tct = it.split("\\|");
            Map<String, Integer> types = columnTypes.computeIfAbsent(tct[0], (k) -> new HashMap<>());
            int type;
            switch (tct[2]) {
                case "bigint":
                    type = Types.BIGINT;
                    break;
                case "boolean":
                    type = Types.BOOLEAN;
                    break;
                case "bytea":
                    type = Types.BINARY;
                    break;
                case "character varying":
                case "character":
                    type = Types.CHAR;
                    break;
                case "date":
                    type = Types.DATE;
                    break;
                case "double precision":
                    type = Types.DOUBLE;
                    break;
                case "integer":
                    type = Types.INTEGER;
                    break;
                case "jsonb":
                    type = Types.CLOB;
                    break;
                case "numeric":
                    type = Types.NUMERIC;
                    break;
                case "real":
                    type = Types.REAL;
                    break;
                case "smallint":
                    type = Types.SMALLINT;
                    break;
                case "timestamp without time zone":
                    type = Types.TIMESTAMP;
                    break;
                case "timestamp with time zone":
                    type = Types.TIME_WITH_TIMEZONE;
                    break;
                default:
                    type = Types.VARCHAR;
                    break;
            }
            types.put(tct[1], type);
        });
        return columnTypes;
    }

    /**
     * 保存批量数据
     *
     * @param columnTypes 列类型，为空则不处理类型转换
     * @param list        列表
     * @param suffix      后缀
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> void saveBatch(Map<String, Map<String, Integer>> columnTypes, List<T> list, String... suffix) throws IllegalAccessException {
        if (CollectionUtils.isEmpty(list)) return;

        Class<T> clazz = (Class<T>) list.get(0).getClass();
        preprocess(columnTypes, list, TableInfoHelper.getTableInfo(clazz), suffix == null || suffix.length == 0 ? StringUtils.EMPTY : suffix[0],
                getPropertyField(clazz), true);
    }

    /**
     * 更新批量数据
     *
     * @param columnTypes 列类型，为空则不处理类型转换，会直接执行更新，此时无法更新日期和时间
     * @param list        列表
     * @param suffix      后缀
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> void updateBatchById(Map<String, Map<String, Integer>> columnTypes, List<T> list, String... suffix)
            throws IllegalAccessException {
        if (CollectionUtils.isEmpty(list)) return;

        Class<T> clazz = (Class<T>) list.get(0).getClass();
        preprocess(columnTypes, list, TableInfoHelper.getTableInfo(clazz), suffix == null || suffix.length == 0 ? StringUtils.EMPTY : suffix[0],
                getPropertyField(clazz), false);
    }

    /**
     * 保存或更新批量数据
     *
     * @param columnTypes 表字段类型，为空则不处理类型转换
     * @param list        列表
     * @param suffix      后缀
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> void saveOrUpdateBatch(Map<String, Map<String, Integer>> columnTypes, List<T> list, String... suffix)
            throws IllegalAccessException {
        if (CollectionUtils.isEmpty(list)) return;

        Class<T> clazz = (Class<T>) list.get(0).getClass();
        List<T> insert = new ArrayList<>(), update = new ArrayList<>();
        TableInfo tableInfo = TableInfoHelper.getTableInfo(clazz);
        Map<String, Field> propertyField = getPropertyField(clazz);
        Field idField = propertyField.get(tableInfo.getKeyProperty());
        Map<Object, T> idMap = new HashMap<>();
        List idList;
        // 自动类型通过主键是否为空判断，其他类型需要通过查询判断
        boolean auto = tableInfo.getIdType() == IdType.AUTO;
        for (T l : list) {
            Object id = idField.get(l);
            if (auto) {
                if (id == null) insert.add(l);
                else update.add(l);
            } else {
                if (id == null) insert.add(l);
                else idMap.put(id, l);
            }
        }

        if (!auto) {
            if (MapUtils.isEmpty(idMap) ||
                    CollectionUtils.isEmpty(idList = namedTemplate.queryForList(String.format("SELECT %s FROM %s WHERE %s IN (:ids)", tableInfo.getKeyColumn(),
                                    tableInfo.getTableName() + (suffix == null || suffix.length == 0 ? StringUtils.EMPTY : suffix[0]), tableInfo.getKeyColumn()),
                            Collections.singletonMap("ids", idMap.keySet()), tableInfo.getKeyType()))) {
                insert = list;
            } else {
                for (Object id : idList) {
                    T t = idMap.remove(id);
                    if (t != null) update.add(t);
                }
                if (MapUtils.isNotEmpty(idMap)) insert.addAll(idMap.values());
            }
        }

        if (!insert.isEmpty()) saveBatch(columnTypes, insert, suffix);
        if (!update.isEmpty()) updateBatchById(columnTypes, update, suffix);
    }

    /**
     * 获取属性-字段
     *
     * @param clazz 类
     * @return 属性-字段
     */
    public <T> Map<String, Field> getPropertyField(Class<T> clazz) {
        Map<String, Field> propertyField = ReflectionKit.getFieldMap(clazz);
        if (!propertyField.isEmpty()) {
            for (Field field : propertyField.values()) {
                if (!field.isAccessible()) field.setAccessible(true);
            }
        }
        return propertyField;
    }

    /**
     * 预处理
     *
     * @param columnTypes   列类型
     * @param list          列表
     * @param tableInfo     表信息
     * @param suffix        后缀
     * @param propertyField 属性字段
     * @param insert        插入
     */
    public <T> void preprocess(Map<String, Map<String, Integer>> columnTypes, List<T> list, TableInfo tableInfo, String suffix,
                               Map<String, Field> propertyField, boolean insert) throws IllegalAccessException {
        Map<String, String> columnProperty = new LinkedHashMap<>();
        columnProperty.put(tableInfo.getKeyColumn(), tableInfo.getKeyProperty());
        tableInfo.getFieldList().forEach(it -> columnProperty.putIfAbsent(it.getColumn(), it.getProperty()));

        int size = list.size(), columnIndex = 0;
        Set<Integer> indexSet = new LinkedHashSet<>(columnProperty.size(), 1);
        Map<String, Integer> types = MapUtils.isEmpty(columnTypes) ? Maps.newHashMap() : columnTypes.get(tableInfo.getTableName() + suffix),
                reserveTypes = new LinkedHashMap<>(columnProperty.size(), 1);
        List<List> valuesList = new ArrayList<>();
        boolean typeHandling = MapUtils.isNotEmpty(types);
        // 统计值非空字段，只保留值非空字段，去除不匹配字段（共用实体类）
        for (Map.Entry<String, String> cp : columnProperty.entrySet()) {
            Integer type = null;
            if (typeHandling && (type = types.get(cp.getKey().replace("\"", "").toLowerCase())) == null) continue;

            Field field = propertyField.get(cp.getValue());
            // 字段加了引号（关键字）
            for (int j = 0; j < size; j++) {
                Object value = field.get(list.get(j));
                if (value != null && indexSet.add(columnIndex)) {
                    reserveTypes.putIfAbsent(cp.getKey(), type);
                }

                List<Object> values;
                if (valuesList.size() > j) {
                    values = valuesList.get(j);
                } else valuesList.add(values = new ArrayList<>());
                values.add(value);
            }
            ++columnIndex;
        }

        // 最大参数数量（32767）
        int max = 32767 / 3, reserve = reserveTypes.size(), batch = Math.min(max / reserve, size), typeIndex = 0, index = 0, total = 0;
        StringBuilder variable = new StringBuilder();
        List<Object> argList = new ArrayList<>();
        int[] argTypes = null, lineTypes = null;
        if (typeHandling) {
            argTypes = new int[batch * reserve];
            lineTypes = new int[reserve];
            for (Integer type : reserveTypes.values()) lineTypes[typeIndex++] = type;
        }
        for (List values : valuesList) {
            variable.append('(');
            for (int j = 0; j < values.size(); j++) {
                if (indexSet.contains(j)) {
                    variable.append("?,");
                    argList.add(values.get(j));
                }
            }
            if (typeHandling) System.arraycopy(lineTypes, 0, argTypes, index * reserve, reserve);
            variable.setCharAt(variable.length() - 1, ')');
            variable.append(", ");

            // 批次或结束
            ++index;
            ++total;
            if (index % batch == 0) {
                variable.setLength(variable.length() - 2);
                update(tableInfo, suffix, insert, reserveTypes, variable, argList, argTypes);

                index = 0;
                variable.setLength(0);
                argList.clear();
            } else if (total == size) {
                variable.setLength(variable.length() - 2);
                update(tableInfo, suffix, insert, reserveTypes, variable, argList,
                        typeHandling ? Arrays.copyOfRange(argTypes, 0, reserve * index) : null);
            }
        }
    }

    /**
     * 执行更新
     *
     * @param tableInfo    表信息
     * @param suffix       后缀
     * @param insert       插入
     * @param reserveTypes 保留类型
     * @param variable     变量
     * @param argList      参数列表
     * @param argTypes     参数类型
     */
    public void update(TableInfo tableInfo, String suffix, boolean insert, Map<String, Integer> reserveTypes, StringBuilder variable,
                       List<Object> argList, int[] argTypes) {
        String table = tableInfo.getTableName() + suffix, sql, c;
        if (insert) {
            sql = String.format("INSERT INTO %s (%s) VALUES %s", table, StringUtils.join(reserveTypes.keySet(), ", "), variable);
        } else {
            sql = variable.toString();
            variable.setLength(0);
            for (Map.Entry<String, Integer> columnType : reserveTypes.entrySet()) {
                if ((c = columnType.getKey()).equals(tableInfo.getKeyColumn())) continue;

                if (argTypes == null) {
                    variable.append(c).append(" = n.").append(c).append(", ");
                } else {
                    if (columnType.getValue() == Types.DATE) {
                        variable.append(c).append(" = COALESCE(to_date(n.").append(c).append(", 'yyyy-mm-dd'), o.").append(c).append("), ");
                    } // TIMESTAMP_WITH_TIMEZONE not tested
                    else if (columnType.getValue() == Types.TIMESTAMP || columnType.getValue() == Types.TIMESTAMP_WITH_TIMEZONE) {
                        variable.append(c).append(" = COALESCE(to_timestamp(n.").append(c).append(", 'yyyy-mm-dd hh24:mi:ss.ms'), o.").append(c).append("), ");
                    } else if (!c.equals(tableInfo.getKeyColumn())) {
                        variable.append(c).append(" = COALESCE(n.").append(c).append(", o.").append(c).append("), ");
                    }
                }
            }
            if (StringUtils.isEmpty(variable)) {
                log.info("Table [%s], variable is null.", table);
                return;
            }
            variable.setLength(variable.length() - 2);
            sql = String.format("UPDATE %s o SET %s FROM (VALUES %s) AS n (%s) WHERE o.%s = n.%s", table, variable,
                    sql, StringUtils.join(reserveTypes.keySet(), ", "), tableInfo.getKeyColumn(), tableInfo.getKeyColumn());
        }

        int rows = argTypes == null ? namedTemplate.getJdbcTemplate().update(sql, argList.toArray(new Object[0])) : namedTemplate.getJdbcTemplate().update(sql, argList.toArray(new Object[0]), argTypes);// NOSONAR
        log.info("{} {} table {} rows.", insert ? "Insert" : "Update", table, rows);
    }

}
