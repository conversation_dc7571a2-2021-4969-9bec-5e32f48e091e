package cn.cmcc.common.pojo.template;

import cn.cmcc.common.enums.HttpMethod;
import cn.cmcc.common.utils.StringUtils;
import lombok.Data;

/**
 * 请求三方token参数
 * 
 * @version v1.0
 * <AUTHOR>
 * @date 2023年3月29日下午3:05:09
 */
@Data
public class RequestThirdTokenParam {
	
	private String requestType = HttpMethod.POST.name();

	private String url;
	
	private String param;
	
	private String valueKey;
	
	private String defaultPrefix = StringUtils.EMPTY;

	private String tokenKey = "authorization";
}
