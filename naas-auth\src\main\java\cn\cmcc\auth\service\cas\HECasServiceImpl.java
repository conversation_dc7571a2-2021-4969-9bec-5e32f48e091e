package cn.cmcc.auth.service.cas;

import java.io.IOException;

import javax.jws.WebService;

import org.springframework.stereotype.Component;
import org.xml.sax.SAXException;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.HECasService;
import cn.cmcc.auth.service.HECasService.Root;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 服务实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@Data
@Slf4j
@Component
@WebService(targetNamespace = "http://services.cas.he", endpointInterface = "cn.cmcc.auth.service.HECasService")
@RequiredArgsConstructor
public class HECasServiceImpl implements HECasService {

    private final CasConfig casConfig;

    @Override
    public boolean logout(Root root) throws IOException, SAXException {
        if (root != null && root.getTicket() != null) {
            int start = root.getTicket().indexOf('<'), end = root.getTicket().lastIndexOf('>');
            if (start < 0 || end < 0) return false;

            String ticket = casConfig.getTextForElement(root.getTicket().substring(start, ++end), "ticket", false);
            casConfig.getCasService().logout(ticket);
            return true;
        }
        return false;
    }

}
