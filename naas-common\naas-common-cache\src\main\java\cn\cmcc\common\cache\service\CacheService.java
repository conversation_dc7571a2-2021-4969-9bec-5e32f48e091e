package cn.cmcc.common.cache.service;

import cn.cmcc.common.cache.enums.ChannelTopicEnum;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * cache 工具类
 *
 * <AUTHOR>
 **/
public interface CacheService {

	void set(final String key, final Object value);

	void set(final String key, final Object value, String cacheType);

	void set(final String key, final Object value, final long timeout, final TimeUnit timeUnit, String cacheType);

	boolean expire(final String key, final long timeout, String cacheType);

	boolean expire(final String key, final long timeout, final TimeUnit unit, String cacheType);

	Object get(final String key);

	Object get(final String key, String cacheType);

	Object get(final String key, String cacheType, boolean prefix);

	boolean del(final String key, String cacheType);

	long del(final Collection collection, String cacheType);

	boolean clear(String cacheType);

	Boolean hasKey(String key, String cacheType);

	Map<Object, Object> getMap(String cacheType);

	Set<Object> keys(final String pattern, String cacheType);

	boolean lock(final String key, String cacheType);

	void redisPublisher(ChannelTopicEnum channelTopicEnum, Object message);
}
