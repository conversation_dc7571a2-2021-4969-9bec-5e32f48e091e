package cn.cmcc.common.runner;

import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import cn.cmcc.common.config.NaasConfig;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.spring.SpringUtils;

/**  
 * 
 * <AUTHOR>
 * @date 2024-03-11 09:43:13
*/

@Component
public class SystemInitRunner implements CommandLineRunner{

	@Override
	public void run(String... args) throws Exception {
		 Environment environment = SpringUtils.getEnvironment();
         String contextPath = environment.getProperty("server.servlet.context-path",StringUtils.EMPTY);
         if (contextPath.equals("/")) contextPath = "";
         NaasConfig.SERVER_PORT_CONTEXT = "http://127.0.0.1:" + environment.getProperty("server.port") + contextPath;
	}

}
