/**
 *
 *
 * @Title: StageFeildConfigDto.java
 * <AUTHOR>
 * @date 2022年9月19日
 * @version V1.0
 */
package cn.cmcc.system.api.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StageFeildConfigDto implements Serializable{

    /**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	/** 标题 */
    private String title;

    /** 字段名称 */
    private String name;

    /** 阶段标识 */
    private String stage;

    /** 表单类型 */
    private String formType;

    /** 是否显示 */
    private Boolean formShow;

    /** 是否编辑 */
    private Boolean formEdit;

    /** 表单数据来源类型，数据字典，http请求 */
    private String dataSourceType;

    /** 表单数据来源 */
    private String dataSource;

    /** 级联父级字段 */
    private String cascadeCode;

    /** 是否必填 */
    private Boolean required;

    /** 是否脱敏 */
    private Boolean dataEncryption;

    /** JS校验规则 */
    private String jsValid;

    /** 后台校验URL */
    private String serverValid;

    /** 字段排序 */
    private Long seq;

    /** 表名 */
    private String tableName;

    /** 阶段标识 */
    private String stageCode;

    /** 表单分组 */
    private String modelType;

    /** 业务类型 */
    private String businessType;

    /** 子分类(同一个表，存储多种业务类型数据) */
    private String tableSubType;

    /** 环节名称(流程类为流程节点，接口类为接口名称) */
    private String node;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 提示信息
     */
    private String tipInfo;

    private String param1;
    private String param2;
    private String param3;
    private String param4;
    private boolean param5;

    /**
     * 初始隐藏
     */
    private boolean initHide;

    /**
     * 条件必填
     */
    private String conditionRequired;

    private List<String> queryTables;

    /**
     * 数据联动
     */
    private String dataLinkage;

    /**
     * 弹窗多选-默认显示字段
     */
    private String popupMultiDisplayField;

    /**
     * 弹窗多选用于去重的唯一标识字段
     */
    private String popupMultiUniqueKey;

    /**
     * 弹窗多选-初始化选中时用于匹配的字段名,如果为空则使用 displayField
     */
    private String popupMultiInitMatchField;
}
