package cn.cmcc.kernel.api.service;

import cn.cmcc.kernel.api.dto.CommonQueryDto;
import cn.cmcc.kernel.api.dto.CommonQueryFieldDto;

import java.util.List;
import java.util.Map;

public interface RemoteDbQueryService {

	/**
	 *
	 * @Title: query
	 * @Description: 通用查询
	 * @param: @param code
	 * @param: @param paramMap
	 * @param: @return
	 * @return: List<Map < String, Object>>
	 * @throws
	 */
	List<Map<String, Object>> query(String code, Map<String, Object> paramMap);

	/**
	 * 获取单条记录
	 * @param code
	 * @param paramMap
	 * @return Map<String ,Object>
	 */
	Map<String ,Object> queryOne(String code,Map<String ,Object> paramMap) ;

	/**
	 *
	 * @Title: selectCommonQueryDtoByCode
	 * @Description: 查询通用查询明细
	 * @param: @param code
	 * @param: @return
	 * @return: CommonQueryDto
	 * @throws
	 */
	CommonQueryDto getByCode(String code);

	/**
	 *
	 * @Title: selectCommonQueryDtoByCode
	 * @Description: 查询通用查询明细
	 * @param: @param code
	 * @param: @return
	 * @return: CommonQueryDto
	 * @throws
	 */
	List<CommonQueryDto> getByCodes(List<String> codes);

	/**
	 *
	 * @Title: selectCommonQueryDtoField
	 * @Description: 查询通用查询明细
	 * @param: @param code
	 * @param: @return
	 * @return: CommonQueryDto
	 * @throws
	 */
	List<CommonQueryFieldDto> getQueryField(String code);

	/**
	 *
	 * @Title: selectCommonQueryDtoField
	 * @Description: 查询通用查询明细
	 * @param: @param code
	 * @param: @return
	 * @return: CommonQueryDto
	 * @throws
	 */
	List<CommonQueryFieldDto> getQueryField(CommonQueryFieldDto commonQueryFieldDto);

	/**
	 * 获取节点下的所有通用SQL
	 * @param code
	 * @return
	 */
	List<CommonQueryDto> queryByNodeIdOrderByCode(String code);

	/**
	 * 获取通用查询SQL
	 * @param mastpId
	 * @param param
	 * @return
	 */
	String getBoundSql(String mastpId, Map<String, Object> param);

	/**
	 * 刷新全局缓存
	 */
	void refresh();

	int runDeleteSql(String code, Map<String, Object> mapParam);

	int runUpdateSql(String code, Map<String, Object> mapParam);

	int runInsertSql(String code, Map<String, Object> mapParam);
}
