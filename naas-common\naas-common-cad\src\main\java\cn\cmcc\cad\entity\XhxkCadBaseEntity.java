package cn.cmcc.cad.entity;

import java.awt.Color;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import cn.cmcc.cad.service.ICadService;
import cn.hutool.core.util.SerializeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class XhxkCadBaseEntity implements Serializable {
	private static final long serialVersionUID = 4442351105043405584L;
	
	private String id;
	private String parentId;
	/**
	 * 对象类型
	 */
	private String type;
	
	private String layer;
	
	/**
	 * 对象位置 ：<br/>
	 * &nbsp;&nbsp;文本类为具体文本所在位置<br/>
	 * &nbsp;&nbsp;曲线类为中心点<br/>
	 * &nbsp;&nbsp;线段类为第一个点
	 */
	private XhxkCadPoint position;

	/**
	 * 分类名，主要用于识别器件
	 */
	private String block;

	/**
	 * ColorId
	 */
	private int colorId;
	
	private Color color;
	
	private String hexColor;

	/**
	 * 坐标集合，主要针对曲线类和线段类
	 */
	private List<XhxkCadPoint> points;
	/**
	 * 文本内容，key-value对象的，存储vale拼接结果
	 */
	private String text;
	/**
	 * 文本内容，key-value对象的值
	 */
	private Map<String, String> textMap;
	/**
	 * wkt对象<br/>
	 * &nbsp;&nbsp;文本类为位置点<br/>
	 * &nbsp;&nbsp;曲线类为所有线段坐标<br/>
	 * &nbsp;&nbsp;线段类为所有线段坐标
	 */
	private String wkt;

	/**
	 * 对象是否为设备
	 */
	private boolean device;
	/**
	 * 设备类型
	 */
	private String deviceName;
	/**
	 * 设备CODE
	 */
	private String deviceCode;

	/**
	 * 用于区分是否是设备，是设备则值为device
	 */
	private String deviceFlag;
	/**
	 * 对象类型，如果是设备值为3，默认为1表示墙体
	 */
	private String lineType;
	

	public XhxkCadBaseEntity(String id, String type, String block,String layer) {
		this.id = id;
		this.type = type;
		this.block = block;
		this.layer = layer;
		this.lineType = "1";
	}

	public void setValues(XhxkCadPoint position, String wkt, List<XhxkCadPoint> points, String text, Map<String, String> textMap) {
		this.position = position;
		this.wkt = wkt;
		this.points = points;
		this.text = text;
		this.textMap = textMap;
	}

	public void setValues(XhxkCadPoint position, String wkt, List<XhxkCadPoint> points) {
		this.setValues(position, wkt, points, null, null);
	}

	public void setWkt() {
		if(points==null||points.isEmpty()) {
			this.setPoineWkt(this.position);
		}else if (points.size() == 1) {
			this.setPoineWkt();
		} else {
			this.setLineWkt();
		}
	}

	public void setLineWkt() {
		String wkt = "";
		for (XhxkCadPoint point : points) {
			wkt += String.format("%f %f,",point.getX(), point.getY());
		}
		if (wkt.endsWith(",")) {
			wkt = wkt.substring(0, wkt.length() - 1);
		}
		wkt = String.format("LINESTRING(%s)",wkt);
		this.wkt = wkt;
	}

	public void setPoineWkt() {
		XhxkCadPoint point = points.get(0);
		this.setPoineWkt(point);
	}
	
	public void setPoineWkt(XhxkCadPoint point) {
		String wkt = String.format("POINT(%f %f)", point.getX() , point.getY() );
		this.wkt = wkt;
	}

	public String getFullText() {
		if (textMap == null || textMap.isEmpty()) {
			return "";
		}
		String text = "";
		for (String key : textMap.keySet()) {
			if (key.equals("allText"))
				continue;
			text += textMap.get(key);
		}
		text = text.replaceAll(",", "");
		text = text.replaceAll(",", "");
		text = text.replaceAll("\"", "");
		return text;
	}

	public String getRegText(String keyValue) {
		if (textMap == null || textMap.isEmpty()) {
			return null;
		}
		String text = "";
		for (String key : textMap.keySet()) {
			text = textMap.get(key);
			if (StringUtils.isNotBlank(text) && text.contains(keyValue)) {
				text = text.replaceAll(",", "");
				text = text.replaceAll(",", "");
				text = text.replaceAll("\"", "");
				return text;
			}
		}
		return null;
	}

	public XhxkCadBaseEntity deepCopy() {
		XhxkCadBaseEntity entity = null;
		try {
			/*
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(baos);
			oos.writeObject(this);

			ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
			ObjectInputStream ois = new ObjectInputStream(bais);
			entity = (XhxkCadBaseEntity) ois.readObject();*/
			entity = SerializeUtil.clone(this);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
		return entity;
	}
	public String toCsvHeader() {
		return "id,type,layer,block,parentId,hexColor,text,device,deviceName,deviceCode,deviceFlag,wkt";
	}
	public String toCsv() {
		this.setWkt();
		String tmp = this.text;
		if(tmp!=null){
			tmp = tmp.replaceAll("\n\r", "").replaceAll("\n", "").replaceAll("\r", "").replaceAll("\"", " ");
		}else {
			tmp="";
		}
		return String.format("%s,%s,%s,%s,%s,%s,\"%s\",%s,%s,%s,%s,\"%s\"", id,type,layer,block,parentId,hexColor,tmp,device,deviceName,deviceCode,deviceFlag,getWkt() );
	}
	
	/**
	 * 先缩放后移动
	 * @param scale - 缩放比例
	 * @param point - 偏移参数
	 */
	public void transfer(ICadService cadService,Double scale,XhxkCadPoint minPoint) {
		position = cadService.transfer(position, scale, minPoint);
		points = cadService.transfer(points, scale, minPoint);
	}

	public void setEntityColor(int colorId) {
		this.colorId = colorId;
		// 获取实际颜色
		//Color color = cadImage.getColors().getByIndex(colorId);

		// 将颜色转换为16进制编码字符串
		//String hexColor = String.format("#%06X", (0xFFFFFF & color.toArgb()));
	}
}
