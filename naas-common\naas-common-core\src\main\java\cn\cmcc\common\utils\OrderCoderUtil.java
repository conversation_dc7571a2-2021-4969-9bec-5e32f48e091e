package cn.cmcc.common.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.cmcc.common.constant.Constants;

/**
 * <AUTHOR>
 * 工单编码码生成器，生成32位数字编码。
 *  2位工单类型+17位时间戳+13位(用户id加密&随机数)
 */

public class OrderCoderUtil {

    /** 随即编码 */
    private static final int[] R = new int[]{7, 9, 6, 2, 8 , 1, 3, 0, 5, 4};

    /** 用户id和随机数总长度 */
    private static final long MAX_LENGTH = 13L;

    /**
     * 生成工单单号编码
     * @param userId  用户ID
     */
    public static String getOrderCode(Long userId){
        return  getCode(userId);
    }

    /**
     * 更具id进行加密+加随机数组成固定长度编码
     */
    private static String toCode(Long id) {
        String idStr = id.toString();
        StringBuilder idsbs = new StringBuilder();
        for (int i = idStr.length() - 1 ; i >= 0; i--) {
            idsbs.append(R[idStr.charAt(i)-'0']);
        }
        return idsbs.append(getRandom(MAX_LENGTH - idStr.length())).toString();
    }

    /**
     * 生成时间戳
     */
    private static String getDateTime(){
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return sdf.format(new Date());
    }

    /**
     * 生成固定长度随机码
     * @param n    长度
     */
    private static long getRandom(long n) {
        long min = 1,max = 9;
        for (int i = 1; i < n; i++) {
            min *= 10;
            max *= 10;
        }
        // rangeLong
        return (((long) (Constants.rand.nextDouble() * (max - min)))) + min;
    }

    /**
     * 生成不带类别标头的编码
     * @param userId  用户ID
     */
    private static synchronized String getCode(Long userId){
        userId = userId == null ? 10000 : userId;
        return getDateTime() + toCode(userId);
    }

    
    public static String genAgentFile() {
    	List<String> manJars = List.of("naas-app.jar","naas-auth.jar","naas-blackspot.jar","naas-cov.jar","naas-gateway.jar","naas-indoor.jar","naas-kernel.jar","naas-outdoor3d.jar","naas-outdoor.jar","naas-plan.jar","naas-survey.jar","naas-system.jar","naas-vs.jar");
    	List<String> commonJars= List.of("naas-common-core","naas-common-datasource","naas-common-dict","naas-common-flow","naas-common-log","naas-common-interactive","naas-common-security","naas-common-storage","naas-common-webservice","naas-common-cache","naas-common-cad");
    	
    	Map<String, List<String>> smap = new HashMap<String, List<String>>();
    	smap.put("naas-plan.jar", List.of("naas-plan-base","naas-plan-hn","naas-plan-js","naas-plan-jx","naas-plan-nm","naas-plan-tj","naas-plan-yn","naas-plan-zj"));
    	smap.put("naas-app.jar", List.of("naas-app-data","naas-app-update"));
    	String version = "3.0.0.jar";
    	StringBuffer res = new StringBuffer();
    	String main = "<file_%s>%s</file_%s>";
    	String common = "<file_%s>%s;BOOT-INF/lib/%s-%s.jar</file_%s>";
    	int total = 1;
    	for(int index = 1 ;index <= manJars.size();index ++) {
    		res.append(String.format(main, total ,manJars.get(index-1),total));
    		res.append("\n");
    		for(int jndex = 1 ;jndex <= commonJars.size();jndex++ ) {
    			res.append(String.format(common,total,manJars.get(index-1),commonJars.get(jndex-1),version,total));
    			res.append("\n");
    			total = total +1;
        	}
    		
    		List<String> sJars = smap.get(manJars.get(index-1));
			if(sJars != null && sJars.size() > 0 ) {
				for(int zndex = 1; zndex <= sJars.size() ; zndex ++ ) {
					res.append(String.format(common,total,manJars.get(index-1),sJars.get(zndex-1),version,total));
	    			res.append("\n");
	    			total = total +1;
				}
			}
    	}
    	return res.toString();
    }

    public static void main(String[] args) {
    	System.out.println(genAgentFile());
    }



}

