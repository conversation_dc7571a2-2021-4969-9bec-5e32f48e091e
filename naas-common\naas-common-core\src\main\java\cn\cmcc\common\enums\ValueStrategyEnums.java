package cn.cmcc.common.enums;

import java.util.Date;

import cn.cmcc.common.context.SecurityUtils;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.uuid.IdUtils;

/**
 * TODO(用一句话描述该文件做什么)
 *
 * <AUTHOR>
 * @date 2023年2月22日
 * @version V1.0
 */
public enum ValueStrategyEnums {

	ORDER_NO("order_no", "工单编号") {
		@Override
		public Object get() {
			return null;
		}
	},
	IMPORT_BATCH("import_batch", "导入批次") {
		@Override
		public Object get() {
			return null;
		}
	},
	DEFAULT_VALUE("default_value", "默认值") {
		@Override
		public Object get() {
			return null;
		}
	},
	CREATE_TIME("create_time", "创建时间",true) {
		@Override
		public Object get() {
			return new Date();
		}
	},
	CREATE_USER("create_user", "创建人(账号)",true) {
		@Override
		public Object get() {
			return SecurityUtils.getLoginUser().getAccount();
		}
	},
	CREATE_USER_ID("create_user_id", "创建人(ID)",true) {
		@Override
		public Object get() {
			return SecurityUtils.getLoginUser().getUserId();
		}
	},
	UPDATE_USER("update_user", "更新人(账号)") {
		@Override
		public Object get() {
			return SecurityUtils.getLoginUser().getAccount();
		}
	},
	UPDATE_USER_ID("update_user_id", "更新人(ID)") {
		@Override
		public Object get() {
			return SecurityUtils.getLoginUser().getUserId();
		}
	},
	UPDATE_TIME("update_time", "更新时间") {
		@Override
		public Object get() {
			return new Date();
		}
	},
	CONDITION_DEFAULT("conDefault", "条件设置默认值"){
		@Override
		public Object get() {
			return null;
		}
	},
	UUID("uuid", "uuid",true) {
		@Override
		public Object get() {
			return IdUtils.fastSimpleUUID();
		}
	};


	private final String code;
    private final String name;
    private final boolean checkEmpty;
    
    ValueStrategyEnums(String code,String name){
    	this.code = code;
    	this.name = name;
    	this.checkEmpty = false;
    }
    
    ValueStrategyEnums(String code,String name,boolean checkEmpty){
    	this.code = code;
    	this.name = name;
    	this.checkEmpty = checkEmpty;
    }
    
    public abstract Object get();
    
    public static ValueStrategyEnums getByValue(String value) {
    	if(StringUtils.isEmpty(value)) return null;
        for (ValueStrategyEnums e : values()) {
            if (e.getCode().equals(value)) {
                return e;
            }
        }
        return null;
    }

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}


	public boolean isCheckEmpty() {
		return checkEmpty;
	}
	
	
}
