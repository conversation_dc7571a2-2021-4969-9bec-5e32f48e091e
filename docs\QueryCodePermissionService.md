# 通用查询编码权限管理服务

## 概述

`QueryCodePermissionServiceImpl` 是一个企业级的权限管理服务，用于控制用户对通用查询编码的访问权限。该服务支持用户直接权限和角色权限两种授权方式，提供了完整的权限管理功能。

## 功能特性

### 🔐 多层权限检查
- **用户直接权限**：直接为用户分配特定查询编码的访问权限
- **角色权限**：通过用户角色间接获得查询编码的访问权限
- **超级管理员**：自动拥有所有查询编码的访问权限

### 🚀 完整的权限管理
- **单个权限管理**：支持单个权限的授予和撤销
- **批量权限管理**：支持批量权限操作，提高管理效率
- **权限查询**：提供多种权限查询和统计功能

### ⚡ 性能优化
- **缓存机制**：权限检查结果自动缓存，提高查询性能
- **缓存更新**：权限变更时自动清除相关缓存
- **事务支持**：批量操作使用事务确保数据一致性

## 数据库设计

### 用户权限表 (t_query_code_permission)
```sql
CREATE TABLE t_query_code_permission (
    id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    query_code VARCHAR(200) NOT NULL COMMENT '查询编码',
    permission_type VARCHAR(20) DEFAULT 'read' COMMENT '权限类型：read-查询，write-修改',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(64) COMMENT '创建人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(64) COMMENT '更新人',
    remark VARCHAR(500) COMMENT '备注'
);
```

### 角色权限表 (t_role_query_code_permission)
```sql
CREATE TABLE t_role_query_code_permission (
    id VARCHAR(64) PRIMARY KEY,
    role_id VARCHAR(64) NOT NULL COMMENT '角色ID',
    role_key VARCHAR(100) NOT NULL COMMENT '角色标识',
    query_code VARCHAR(200) NOT NULL COMMENT '查询编码',
    permission_type VARCHAR(20) DEFAULT 'read' COMMENT '权限类型',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(64) COMMENT '创建人'
);
```

## 权限检查流程

```mermaid
flowchart TD
    A[权限检查开始] --> B{是否为超级管理员?}
    B -->|是| C[允许访问]
    B -->|否| D[检查用户直接权限]
    D --> E{用户是否有直接权限?}
    E -->|是| C
    E -->|否| F[检查用户角色权限]
    F --> G{角色是否有权限?}
    G -->|是| C
    G -->|否| H[拒绝访问]
```

## API 接口文档

### 权限检查接口

#### 检查用户权限
```http
GET /kernel/queryCodePermission/check/{username}/{queryCode}
```

**参数说明：**
- `username`: 用户名
- `queryCode`: 查询编码

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true
}
```

### 用户权限管理接口

#### 授予用户权限
```http
POST /kernel/queryCodePermission/user/grant
```

**请求体：**
```json
{
    "username": "testuser",
    "queryCode": "qryTestCode",
    "permissionType": "read",
    "remark": "测试权限"
}
```

#### 撤销用户权限
```http
DELETE /kernel/queryCodePermission/user/revoke/{username}/{queryCode}
```

#### 获取用户权限列表
```http
GET /kernel/queryCodePermission/user/{username}
```

#### 批量授予用户权限
```http
POST /kernel/queryCodePermission/user/batchGrant
```

**请求体：**
```json
{
    "username": "testuser",
    "queryCodes": ["qryCode1", "qryCode2", "qryCode3"],
    "permissionType": "read"
}
```

#### 批量撤销用户权限
```http
POST /kernel/queryCodePermission/user/batchRevoke
```

### 角色权限管理接口

#### 授予角色权限
```http
POST /kernel/queryCodePermission/role/grant
```

**请求体：**
```json
{
    "roleKey": "BASE_PRI_ROLE",
    "queryCode": "qryTestCode",
    "permissionType": "read"
}
```

#### 撤销角色权限
```http
DELETE /kernel/queryCodePermission/role/revoke/{roleKey}/{queryCode}
```

#### 获取角色权限列表
```http
GET /kernel/queryCodePermission/role/{roleKey}
```

#### 批量授予角色权限
```http
POST /kernel/queryCodePermission/role/batchGrant
```

#### 批量撤销角色权限
```http
POST /kernel/queryCodePermission/role/batchRevoke
```

### 查询统计接口

#### 获取用户可访问的查询编码
```http
GET /kernel/queryCodePermission/accessible/{username}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "username": "testuser",
        "queryCodes": ["qryCode1", "qryCode2"],
        "permissionType": "read",
        "totalCount": 2
    }
}
```

#### 获取查询编码的权限统计
```http
GET /kernel/queryCodePermission/users/{queryCode}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "queryCode": "qryTestCode",
        "users": ["user1", "user2"],
        "roles": ["BASE_PRI_ROLE"],
        "userCount": 2,
        "roleCount": 1
    }
}
```

### 缓存管理接口

#### 清除用户权限缓存
```http
POST /kernel/queryCodePermission/clearUserCache/{username}
```

#### 清除查询编码权限缓存
```http
POST /kernel/queryCodePermission/clearCodeCache/{queryCode}
```

## 使用示例

### 1. 注解方式权限验证

在需要权限验证的方法上添加 `@CheckQueryCodePerms` 注解：

```java
@GetMapping("/data/{code}")
@CheckQueryCodePerms(message = "无权限访问该查询编码")
public AjaxResult getData(@PathVariable("code") String code) {
    // 业务逻辑
    return AjaxResult.success(data);
}
```

### 2. 编程方式权限检查

```java
@Autowired
private IQueryCodePermissionService queryCodePermissionService;

public void someBusinessMethod(String queryCode) {
    String username = SecurityUtils.getUserName();
    boolean hasPermission = queryCodePermissionService.hasPermission(username, queryCode);
    
    if (!hasPermission) {
        throw new QueryCodePermissionException(queryCode, username, "无权限访问该查询编码");
    }
    
    // 业务逻辑
}
```

### 3. 权限管理示例

```java
// 授予用户权限
queryCodePermissionService.grantUserPermission("testuser", "qryTestCode", "read");

// 批量授予角色权限
List<String> codes = Arrays.asList("qryCode1", "qryCode2", "qryCode3");
queryCodePermissionService.batchGrantRolePermissions("BASE_PRI_ROLE", codes, "read");

// 查询用户可访问的编码
List<String> accessibleCodes = queryCodePermissionService.getUserAccessibleCodes("testuser");
```

## 权限类型说明

| 权限类型 | 说明 | 用途 |
|---------|------|------|
| `read` | 查询权限 | 允许用户查询数据 |
| `write` | 修改权限 | 允许用户修改数据（预留） |

## 注意事项

### 🔒 安全考虑
- 所有权限管理接口都需要管理员权限（`ADMIN` 角色）
- 权限检查支持缓存，但会在权限变更时自动清除
- 超级管理员（用户ID为1）自动拥有所有权限

### ⚡ 性能优化
- 权限检查结果会被缓存，缓存键格式：`username:queryCode`
- 批量操作使用事务，确保数据一致性
- 建议定期清理无效的权限记录

### 🛠️ 扩展性
- 支持自定义权限类型（目前支持 `read` 和 `write`）
- 可以通过扩展 Mapper 接口添加更多查询方法
- 支持通过配置开关控制权限检查功能

## 故障排除

### 常见问题

**Q: 权限检查总是返回 false？**
A: 检查以下几点：
1. 用户是否已登录且能获取到用户信息
2. 用户是否有直接权限或角色权限
3. 权限类型是否正确（默认检查 `read` 权限）

**Q: 权限变更后没有生效？**
A: 可能是缓存问题，尝试：
1. 调用清除缓存接口
2. 检查权限记录是否正确插入数据库
3. 确认事务是否正确提交

**Q: 批量操作部分失败？**
A: 批量操作使用事务，如果部分失败会回滚：
1. 检查数据库约束（如唯一索引）
2. 确认所有参数都有效
3. 查看日志获取详细错误信息

## 更新日志

### v1.0.0 (2025-01-28)
- ✅ 实现完整的权限管理功能
- ✅ 支持用户直接权限和角色权限
- ✅ 提供完整的 REST API 接口
- ✅ 集成缓存机制和事务支持
- ✅ 添加权限验证注解支持

---

**作者**: Claude 4.0 sonnet  
**最后更新**: 2025-01-28
