package cn.cmcc.common.utils;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.util.AntPathMatcher;

import com.alibaba.fastjson2.JSON;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.utils.text.CharsetKit;
import cn.cmcc.common.utils.text.StrFormatter;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils
{
    /** 空字符串 */
    private static final String NULLSTR = "";

    /** 下划线 */
    private static final char SEPARATOR = '_';
    
    private static Pattern humpPattern = Pattern.compile("[A-Z]");

    private final static AntPathMatcher matcher = new AntPathMatcher();

    /**
     * 获取参数不为空值
     *
     * @param value defaultValue 要判断的value
     * @return value 返回值
     */
    public static <T> T nvl(T value, T defaultValue)
    {
        return value != null ? value : defaultValue;
    }

    /**
     * * 判断一个Collection是否为空， 包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Collection<?> coll)
    {
        return isNull(coll) || coll.isEmpty();
    }

    /**
     * * 判断一个Collection是否非空，包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Collection<?> coll)
    {
        return !isEmpty(coll);
    }

    /**
     * * 判断一个对象数组是否为空
     *
     * @param objects 要判断的对象数组
     ** @return true：为空 false：非空
     */
    public static boolean isEmpty(Object[] objects)
    {
        return isNull(objects) || (objects.length == 0);
    }

    /**
     * * 判断一个对象数组是否非空
     *
     * @param objects 要判断的对象数组
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Object[] objects)
    {
        return !isEmpty(objects);
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Map<?, ?> map)
    {
        return isNull(map) || map.isEmpty();
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Map<?, ?> map)
    {
        return !isEmpty(map);
    }
    
    /**
     *
     * 所有不为空
     *
     * @Title: isAllNotEmpty
     * @param css
     * @return boolean
     * @throws
     */
    public static boolean isAllNotEmpty(final CharSequence... css) {
        if (ArrayUtils.isEmpty(css)) {
            return false;
        }
        for (final CharSequence cs : css) {
            if (isEmpty(cs)) {
                return false;
            }
        }
        return true;
    }

    /**
     * * 判断一个字符串是否为空串
     * 
     * @param str String
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(String str)
    {
        return isNull(str) || NULLSTR.equals(str.trim());
    }
    public static boolean isEmpty(Object object)
    {
        return object == null || object.toString().trim().length() == 0;
    }

    /**
     * * 判断一个字符串是否为非空串
     *
     * @param str String
     * @return true：非空串 false：空串
     */
    public static boolean isNotEmpty(String str)
    {
        return !isEmpty(str);
    }

    /**
     * * 判断一个对象是否为空
     *
     * @param object Object
     * @return true：为空 false：非空
     */
    public static boolean isNull(Object object)
    {
        return object == null;
    }

    /**
     * * 判断一个对象是否非空
     *
     * @param object Object
     * @return true：非空 false：空
     */
    public static boolean isNotNull(Object object)
    {
        return !isNull(object);
    }

    /**
     * * 判断一个对象是否是数组类型（Java基本型别的数组）
     *
     * @param object 对象
     * @return true：是数组 false：不是数组
     */
    public static boolean isArray(Object object)
    {
        return isNotNull(object) && object.getClass().isArray();
    }

    /**
     * 去空格
     */
    public static String trim(String str)
    {
        return (str == null ? "" : str.trim());
    }

    /**
     * 截取字符串
     *
     * @param str 字符串
     * @param start 开始
     * @return 结果
     */
    public static String substring(final String str, int start)
    {
        if (str == null)
        {
            return NULLSTR;
        }

        if (start < 0)
        {
            start = str.length() + start;
        }

        if (start < 0)
        {
            start = 0;
        }
        if (start > str.length())
        {
            return NULLSTR;
        }

        return str.substring(start);
    }

    /**
     * 截取字符串
     *
     * @param str 字符串
     * @param start 开始
     * @param end 结束
     * @return 结果
     */
    public static String substring(final String str, int start, int end)
    {
        if (str == null)
        {
            return NULLSTR;
        }

        if (end < 0)
        {
            end = str.length() + end;
        }
        if (start < 0)
        {
            start = str.length() + start;
        }

        if (end > str.length())
        {
            end = str.length();
        }

        if (start > end)
        {
            return NULLSTR;
        }

        if (start < 0)
        {
            start = 0;
        }
        if (end < 0)
        {
            end = 0;
        }

        return str.substring(start, end);
    }

    /**
     * 格式化文本, {} 表示占位符<br>
     * 此方法只是简单将占位符 {} 按照顺序替换为参数<br>
     * 如果想输出 {} 使用 \\转义 { 即可，如果想输出 {} 之前的 \ 使用双转义符 \\\\ 即可<br>
     * 例：<br>
     * 通常使用：format("this is {} for {}", "a", "b") -> this is a for b<br>
     * 转义{}： format("this is \\{} for {}", "a", "b") -> this is \{} for a<br>
     * 转义\： format("this is \\\\{} for {}", "a", "b") -> this is \a for b<br>
     *
     * @param template 文本模板，被替换的部分用 {} 表示
     * @param params 参数值
     * @return 格式化后的文本
     */
    public static String format(String template, Object... params)
    {
        if (isEmpty(params) || isEmpty(template))
        {
            return template;
        }
        return StrFormatter.format(template, params);
    }

    /**
     * 字符串转set
     *
     * @param str 字符串
     * @param sep 分隔符
     * @return set集合
     */
    public static final Set<String> str2Set(String str, String sep)
    {
        return new HashSet<String>(str2List(str, sep, true, false));
    }

    /**
     * 字符串转list
     *
     * @param str 字符串
     * @param sep 分隔符
     * @param filterBlank 过滤纯空白
     * @param trim 去掉首尾空白
     * @return list集合
     */
    public static final List<String> str2List(String str, String sep, boolean filterBlank, boolean trim)
    {
        List<String> list = new ArrayList<String>();
        if (StringUtils.isEmpty(str))
        {
            return list;
        }

        // 过滤空白字符串
        if (filterBlank && StringUtils.isBlank(str))
        {
            return list;
        }
        String[] split = str.split(sep);
        for (String string : split)
        {
            if (filterBlank && StringUtils.isBlank(string))
            {
                continue;
            }
            if (trim)
            {
                string = string.trim();
            }
            list.add(string);
        }

        return list;
    }

    /**
     * 下划线转驼峰命名
     */
    public static String toUnderScoreCase(String str)
    {
        if (str == null)
        {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        // 前置字符是否大写
        boolean preCharIsUpperCase = true;
        // 当前字符是否大写
        boolean curreCharIsUpperCase = true;
        // 下一字符是否大写
        boolean nexteCharIsUpperCase = true;
        for (int i = 0; i < str.length(); i++)
        {
            char c = str.charAt(i);
            if (i > 0)
            {
                preCharIsUpperCase = Character.isUpperCase(str.charAt(i - 1));
            }
            else
            {
                preCharIsUpperCase = false;
            }

            curreCharIsUpperCase = Character.isUpperCase(c);

            if (i < (str.length() - 1))
            {
                nexteCharIsUpperCase = Character.isUpperCase(str.charAt(i + 1));
            }

            if (preCharIsUpperCase && curreCharIsUpperCase && !nexteCharIsUpperCase)
            {
                sb.append(SEPARATOR);
            }
            else if ((i != 0 && !preCharIsUpperCase) && curreCharIsUpperCase)
            {
                sb.append(SEPARATOR);
            }
            sb.append(Character.toLowerCase(c));
        }

        return sb.toString();
    }

    /**
     * 是否包含字符串
     *
     * @param str 验证字符串
     * @param strs 字符串组
     * @return 包含返回true
     */
    public static boolean inStringIgnoreCase(String str, String... strs)
    {
        if (str != null && strs != null)
        {
            for (String s : strs)
            {
                if (str.equalsIgnoreCase(trim(s)))
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 将下划线大写方式命名的字符串转换为驼峰式。如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。 例如：HELLO_WORLD->HelloWorld
     *
     * @param name 转换前的下划线大写方式命名的字符串
     * @return 转换后的驼峰式命名的字符串
     */
    public static String convertToCamelCase(String name)
    {
        StringBuilder result = new StringBuilder();
        // 快速检查
        if (name == null || name.isEmpty())
        {
            // 没必要转换
            return "";
        }
        else if (!name.contains("_"))
        {
            // 不含下划线，仅将首字母大写
            return name.substring(0, 1).toUpperCase() + name.substring(1);
        }
        // 用下划线将原始字符串分割
        String[] camels = name.split("_");
        for (String camel : camels)
        {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty())
            {
                continue;
            }
            // 首字母大写
            result.append(camel.substring(0, 1).toUpperCase());
            result.append(camel.substring(1).toLowerCase());
        }
        return result.toString();
    }

    /**
     * 驼峰式命名法 例如：user_name->userName
     */
    public static String toCamelCase(String s)
    {
        if (s == null)
        {
            return null;
        }
        s = s.toLowerCase();
        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++)
        {
            char c = s.charAt(i);

            if (c == SEPARATOR)
            {
                upperCase = true;
            }
            else if (upperCase)
            {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            }
            else
            {
                sb.append(c);
            }
        }
        return sb.toString();
    }

   /**
    *
    * @Title: toHump
    * @Description: 驼峰转下划线，例如：userName->user_name
    * @param: @param str
    * @param: @return
    * @return: String
    * @throws
    */
    public static String toHump(String str) {
    	Matcher matcher = humpPattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		while (matcher.find()) {
			matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
		}
		matcher.appendTail(sb);
		return sb.toString();
    }

    @SuppressWarnings("unchecked")
    public static <T> T cast(Object obj)
    {
        return (T) obj;
    }

    /**
	 * list 转 Stirng,用于生成 in ('','','')
	 * @param str list字符串，其中包含分隔符 separator
	 * @return 返回字符串
	 */
	public static String strFormat(String str) {
		AtomicReference<StringBuilder> stringBuilder = new AtomicReference<>(new StringBuilder());
		String[] arr = str.split(",");
		for (int i = 0; i < arr.length; i++) {
			if (i == arr.length - 1) {
				stringBuilder.get().append("'").append(arr[i]).append("'");
			} else {
				stringBuilder.get().append("'").append(arr[i]).append("'");
				stringBuilder.get().append(",");
			}
		}
		return stringBuilder.toString();
	}

	/**
     * 校验数组是否包含指定值
     *
     * @param arr 数组
     * @param targetValue 值
     * @return 是否包含
     */
    public static boolean arraysContains(String[] arr, String targetValue)
    {
        return Arrays.asList(arr).contains(targetValue);
    }

	/**
     * 判断是否包含中文
     * @param str
     * @return
     */
	public static boolean isContainChinese(String str) {
    	 Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
    	 Matcher m = p.matcher(str);
    	 if (m.find()) {
    	    return true;
    	 }
    	 return false;
    }
	
	/**
     * 从url中获取文件名
     * @param str
     * @return
     */
	public static String getFileNameFromUrl(String url) {
		return url.substring(url.lastIndexOf(Constants.FILE_SEPARATOR) + 1,url.length());
	}

	/**
     * 获取文件后缀
     * @param str
     * @return
     */
	public static String getFileExtension(String fileName) {
        if(fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0)
            return fileName.substring(fileName.lastIndexOf(".")+1);
        else return "";
	}

	/**
     * 获取文件后缀
     * @param str
     * @return
     */
	public static String getFileName(String fileName) {
        if(fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0)
            return fileName.substring(0,fileName.lastIndexOf(".") - 1);
        else return "";
	}

	//判断整数
	public static boolean isInteger(String str) {
		if (null == str || "".equals(str)) {
			return false;
		}
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}

	//判断浮点数（double和float）
	public static boolean isDouble(String str) {
		if (null == str || "".equals(str)) {
			return false;
		}
		Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
		return pattern.matcher(str).matches();
	}

    public static  boolean strToBool(String str) {
        boolean result = false;
        switch(str.trim().toLowerCase()){
            case "t":
                result = true;
                break;
            case "true":
                result = true;
                break;
            case "false":
                result = false;
                break;
            case "y":
                result = true;
                break;
            case "n":
                result = false;
                break;
            case "yes":
                result = true;
                break;
            case "no":
                result = false;
                break;
            case "ENABLE":
                result = true;
                break;
            case "DISABLE":
                result = false;
                break;
            case "1":
                result = true;
                break;
            default:
                result = false;
                break;
        }
		return result;
	}
	
	/**
	 * 替换字符串中的占位符
	 * 
	 * @param strsql
	 * @param dataMap
	 * @return
	 * @version v1.0
	 * @date 2023年4月24日上午10:05:29
	 */
	public static String replace(String str,Map<String, Object> dataMap) {
		Matcher matcher = Pattern.compile("\\$\\{(.+?)\\}").matcher(str);
		while(matcher.find()) {
			String replace = matcher.group(0);
			String key = replace.replace("${", StringUtils.EMPTY).replace("}", StringUtils.EMPTY).trim();
			str = str.replace(replace, MapUtils.getString(dataMap, key, StringUtils.EMPTY));
		}
		return str;
	}
	
	 /**
     * 查找指定字符串是否匹配指定字符串列表中的任意一个字符串
     * 
     * @param str 指定字符串
     * @param strs 需要检查的字符串数组
     * @return 是否匹配
     */
    public static boolean matches(String str, List<String> strs)
    {
        if (isEmpty(str) || isEmpty(strs)) return false;
        return strs.stream().anyMatch(pattern->isMatch(pattern,str));
    }
    
    public static boolean matches(String str, String [] strs)
    {
        if (isEmpty(str) || isEmpty(strs)) return false;
        return Stream.of(strs).anyMatch(pattern->isMatch(pattern,str));
    }
    
    /**
     * 判断url是否与规则配置: 
     * ? 表示单个字符; 
     * * 表示一层路径内的任意字符串，不可跨层级; 
     * ** 表示任意层路径;
     * 
     * @param pattern 匹配规则
     * @param url 需要匹配的url
     * @return
     */
    public static boolean isMatch(String pattern, String url)
    {
        return matcher.match(pattern, url);
    }
    
    /**
     * 判断是否为空，并且不是空白字符
     *
     * @param str 要判断的value
     * @return 结果
     */
    public static boolean hasText(String str)
    {
        return (str != null && !str.isEmpty() && containsText(str));
    }

    private static boolean containsText(CharSequence str)
    {
        int strLen = str.length();
        for (int i = 0; i < strLen; i++)
        {
            if (!Character.isWhitespace(str.charAt(i)))
            {
                return true;
            }
        }
        return false;
    }
    
    /**
     * json 字符串转 bean，兼容普通json和字符串包裹情况
     *
     * @param jsonStr json 字符串
     * @param cls     要转为bean的类
     * @param <T>     泛型
     * @return data
     */
    public static <T> List<T> jsonConvertArray(String jsonStr, Class<T> cls) {
        try {
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            return JSON.parseArray(jsonStr, cls);
        } catch (Exception e) {
            Object parse = JSON.parse(jsonStr);
            return JSON.parseArray(parse.toString(), cls);
        }
    }
    
    /**
	 * 
	 * 获取程序运行目录
	 *
	 * @Title: getRunPath   
	 * @return String
	 * @throws
	 */
	public static String getRunPath() {
		ApplicationHome applicationHome = new ApplicationHome(StringUtils.class);
		File jarFile = applicationHome.getSource();
	    return jarFile.getParentFile().toString().replace("/lib", "");
	}
	
	public static Map<String, String> parseQueryString(String str) throws IOException {
		Map<String, String> params = new HashMap<>();
		if (StringUtils.isEmpty(str)) {
			return params;
		}
		str = URLDecoder.decode(str, CharsetKit.UTF_8);
		if (str.startsWith("?")) {
			str = str.substring(1);
		}
		String[] queryArr = str.split("&");
		for (String query : queryArr) {
			String key = "";
			String value = "";

			int index = query.indexOf("=");
			if (index == -1) {
				key = query;
			} else {
				key = query.substring(0, index);
				value = query.substring(index + 1);
			}
			params.put(URLDecoder.decode(key, CharsetKit.UTF_8), URLDecoder.decode(value, CharsetKit.UTF_8));
		}
		return params;
	}

    /**
     * list 转 Stirng,用于生成 in ('','','')
     * @param str list字符串，其中包含分隔符 separator
     * @return 返回字符串
     */
    public static String strFormat(String str, String defaultValue) {
        if (str == null) {
            str = defaultValue;
        }
        AtomicReference<StringBuilder> stringBuilder = new AtomicReference<>(new StringBuilder());
        String[] arr = str.split(",");
        for (int i = 0; i < arr.length; i++) {
            if (i == arr.length - 1) {
                stringBuilder.get().append("'").append(arr[i]).append("'");
            } else {
                stringBuilder.get().append("'").append(arr[i]).append("'");
                stringBuilder.get().append(",");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * Stirng 转 list 按逗号切分
     * @param str 字符串
     * @return 返回list不可变
     */
    public static List<String> strToList(String str) {
        if (str == null || str.isEmpty()) {
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(str.split(",")));
    }


    public static String replaceStrUTF8BOM(String header) {
        if (header.startsWith("\uFEFF")) {
            return header.substring(1);
        }
        return header;
    }

    /**
     * <AUTHOR>  2025/4/22 09:12
     * @description: 对象转字符串
     */
    public static String objTransferStr(Object value){
        if(value == null) return null;
        return value.toString().trim();
    }

    /**
      * <AUTHOR> @date 2025/7/11 15:14
      * @param collection 字符串集合
     * @param separator 分隔符
     * @param quote 是否对单个元素使用引号
      * @return java.lang.String
      */
	public static String join(Collection<String> collection, char separator,boolean quote) {
        StringBuilder builder = new StringBuilder();
        List<String> list = new ArrayList<>(collection);
        for(int i=0;i < list.size(); i++) {
            String item = list.get(i);
            if(quote){
                builder.append('"'+item+'"');
            }else {
                builder.append(item);
            }
            if(i != list.size() - 1){
                builder.append(separator);
            }
        }
        return builder.toString();
    }
    public static String join(String [] array, char separator,boolean quote){
        return join(Arrays.asList(array),separator,quote);
    }
}
