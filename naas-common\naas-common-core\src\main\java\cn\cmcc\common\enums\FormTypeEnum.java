package cn.cmcc.common.enums;

/**
 * <p>
 * 表单类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
public enum FormTypeEnum {

    TEXT("text", "文本"),
    TEXTAREA("textarea", "文本域"),
    NUMBER("number", "数字"),
    DATE("date", "日期"),
    DATETIME("datetime", "日期时间"),
    RADIO("radio","单选"),
    CHECKBOX("checkbox", "多选"),
    TIME("time", "时间"),
    PIC("pic", "图片"),
    FILE("file","文件"),
    SWITCH("switch","开关"),
    EDITOR("editor","富文本"),
    SELECT("select", "下拉框"),
    SELECT_MULTIPLE("selectMultiple","下拉多选"),
    SELECT_INPUT("selectInput","下拉多选输入"),
    SELECT_RADIO_INPUT("selectRadioInput","下拉单选输入");

    /**
     * 值
     */
    private final String value;

    /**
     * 名称
     */
    private final String name;

    FormTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 通过值获取枚举，获取失败返回空
     *
     * @param value 值
     * @return 枚举
     */
    public static FormTypeEnum getByValue(String value) {
        for (FormTypeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 枚举中包含值
     *
     * @param value 值
     * @return boolean
     */
    public static boolean containValue(String value) {
        return getByValue(value) != null;
    }

    /**
     * 通过值名称获取枚举，获取失败返回空
     *
     * @param name 名称
     * @return 枚举
     */
    public static FormTypeEnum getByName(String name) {
        for (FormTypeEnum e : values()) {
            if (e.getName().equals(name)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 枚举中包含值名称
     *
     * @param name 名称
     * @return boolean
     */
    public static boolean containName(String name) {
        return getByName(name) != null;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

}
