package cn.cmcc.common.datasource.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.Map;

import cn.cmcc.common.utils.StringUtils;
import com.alibaba.fastjson2.JSONArray;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * 
 * @Class Name JsonTypeHandler
 * <AUTHOR>
 * @Create In 2021年03月23日
 */
//@MappedJdbcTypes({JdbcType.OTHER})
@MappedTypes(Object.class)
public class JsonTypeHandler extends BaseTypeHandler<Object> {
	
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter,
            JdbcType jdbcType) throws SQLException {
		PGobject jsonObject = new PGobject();
		if (parameter instanceof JSONObject) {
			jsonObject.setType("jsonb");
			jsonObject.setValue(parameter.toString());
			ps.setObject(i, jsonObject);
		}else if (parameter instanceof JSONArray) {
			jsonObject.setType("jsonb");
			jsonObject.setValue(parameter.toString());
			ps.setObject(i, jsonObject);
		}else if(parameter instanceof Map) {
			jsonObject.setType("jsonb");
			jsonObject.setValue(JSON.toJSONString(parameter));
			ps.setObject(i, jsonObject);
		}else if(parameter instanceof String) {
			if (StringUtils.isEmpty(parameter.toString())) {
				ps.setObject(i, null);
			} else {
				jsonObject.setType("jsonb");
				jsonObject.setValue((String) parameter);
				ps.setObject(i, jsonObject);
			}
		}else {
			ps.setObject(i, parameter);
		}
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
    	Object obj = rs.getObject(columnName);
        if(obj instanceof PGobject) {
        	return JSON.parse(obj.toString());
        }
        return rs.getString(columnName);
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if(obj instanceof PGobject)
        	return JSON.parse(obj.toString());
    	return rs.getString(columnIndex);
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
    	Object obj = cs.getObject(columnIndex);
        if(obj instanceof PGobject)
        	return JSON.parse(obj.toString());
    	return cs.getString(columnIndex);
    }
}
