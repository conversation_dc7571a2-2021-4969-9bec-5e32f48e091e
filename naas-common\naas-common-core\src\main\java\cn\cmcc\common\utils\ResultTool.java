package cn.cmcc.common.utils;

import cn.cmcc.common.utils.bean.ObjectUtils;
import cn.cmcc.common.web.domain.AjaxResult;

/**
 * @Name 简写接口成功和失败的分别响应结果
 * <AUTHOR>
 * @Description
 * @Created 2023-11-07 21:05
 */
public class ResultTool {

    public static <T> Builder succeed(T data) {
        return new Builder<>(data, AjaxResult.success(data));
    }

    public static class Builder<T> {
        private AjaxResult ajaxResult;
        private boolean isSucceed;

        private T data;

        public Builder(T data, AjaxResult successResult) {
            if (ObjectUtils.isNotEmpty(data)) {
                if (data instanceof String) {
                    if (String.valueOf(data).startsWith("ERROR: ")) {
                        this.isSucceed = false;
                    } else {
                        this.isSucceed = true;
                    }
                } else {
                    this.isSucceed = true;
                }
            } else {
                this.isSucceed = false;
            }
            this.data = data;
            this.ajaxResult = successResult;
        }

        public AjaxResult orFailed(String message) {
            if (!this.isSucceed) {
                this.ajaxResult = AjaxResult.error(5000, message);
                if (this.data instanceof String && String.valueOf(this.data).startsWith("ERROR:")) {
                    this.ajaxResult = AjaxResult.error(5000, ((String) this.data).replace("ERROR:", "").replace("ERROR: ", ""));
                }
            }
            return this.ajaxResult;
        }

        public AjaxResult build() {
            return this.ajaxResult;
        }
    }
}
