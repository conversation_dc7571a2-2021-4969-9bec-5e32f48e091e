/**
 * @Title: RedisCaffeineMessage.java
 * @Package cn.cmcc.common.core.cache.listener
 * 
 * <AUTHOR>
 * @date 2021年9月2日
 * @version V1.0
 */
package cn.cmcc.common.cache.listener;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class RedisCaffeineMessage implements Serializable{

	private static final long serialVersionUID = 1L;

	private String name;
	
	private Object key; 
	
	public RedisCaffeineMessage() {}
	
}
