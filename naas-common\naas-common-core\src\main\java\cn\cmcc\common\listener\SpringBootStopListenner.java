/**
 * TODO(用一句话描述该文件做什么)
 *
 * @Title: SpringBootStopListenner.java
 * <AUTHOR>
 * @date 2022年10月20日
 * @version V1.0
 */
package cn.cmcc.common.listener;

import javax.annotation.Resource;

import org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class SpringBootStopListenner implements ApplicationListener<ContextClosedEvent>{
	
	@SuppressWarnings("rawtypes")
	@Resource
    private AbstractAutoServiceRegistration abstractAutoServiceRegistration;

	@EventListener(ContextClosedEvent.class)
    public void doDeregister() {
        log.info("nacos补偿注销流程，开始");
        try {
            abstractAutoServiceRegistration.destroy();
        }catch (Exception e){
        }
        log.info("nacos补偿注销流程，结束");
    }

    
	// 监听kill pid     无法监听 kill -9 pid
    @Override
    public void onApplicationEvent(ContextClosedEvent contextClosedEvent) {
        doDeregister();
        log.info("系统即将要停止");
    }
}
