# NAAS 系统文档中心

欢迎来到 NAAS（Network Analysis and Automation System）系统文档中心！

## 📚 文档导航

### 🔐 权限管理

#### 查询编码权限管理
- **[快速开始指南](./QueryCodePermissionQuickStart.md)** - 5分钟快速上手权限管理功能
- **[功能说明文档](./QueryCodePermissionService.md)** - 完整的功能介绍和架构说明
- **[API 接口文档](./QueryCodePermissionAPI.md)** - 详细的 REST API 接口说明

## 🔐 查询编码权限管理系统

### 系统概述

查询编码权限管理系统是 NAAS 核心安全模块，提供细粒度的数据访问控制。支持用户直接权限和角色权限两种授权方式，确保数据安全访问。

### 核心特性

- ✅ **多层权限检查** - 用户直接权限 + 角色权限
- ✅ **注解式权限验证** - 简单易用的 `@CheckQueryCodePerms` 注解
- ✅ **完整的权限管理** - 支持单个和批量权限操作
- ✅ **高性能缓存** - 权限检查结果自动缓存
- ✅ **REST API** - 完整的权限管理接口
- ✅ **事务支持** - 批量操作数据一致性保证

### 快速开始

1. **权限验证注解使用**
```java
@GetMapping("/{code}")
@CheckQueryCodePerms(message = "无权限访问该查询编码")
public AjaxResult getData(@PathVariable("code") String code) {
    // 业务逻辑
}
```

2. **编程式权限检查**
```java
boolean hasPermission = queryCodePermissionService.hasPermission(username, queryCode);
```

3. **权限管理操作**
```bash
# 授予用户权限
POST /kernel/queryCodePermission/user/grant

# 批量授予角色权限  
POST /kernel/queryCodePermission/role/batchGrant
```

### 文档结构

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [快速开始指南](./QueryCodePermissionQuickStart.md) | 5分钟快速上手，包含常用场景示例 | 开发者、系统管理员 |
| [功能说明文档](./QueryCodePermissionService.md) | 完整的功能介绍、架构设计和使用说明 | 架构师、高级开发者 |
| [API 接口文档](./QueryCodePermissionAPI.md) | 详细的 REST API 接口规范 | 前端开发者、接口调用者 |

### 权限模型

```mermaid
graph TD
    A[用户请求] --> B{超级管理员?}
    B -->|是| C[允许访问]
    B -->|否| D[检查用户直接权限]
    D --> E{有直接权限?}
    E -->|是| C
    E -->|否| F[检查角色权限]
    F --> G{角色有权限?}
    G -->|是| C
    G -->|否| H[拒绝访问]
```

### 数据库表结构

- **t_query_code_permission** - 用户权限表
- **t_role_query_code_permission** - 角色权限表

### API 接口概览

| 接口类型 | 接口数量 | 主要功能 |
|----------|----------|----------|
| 权限检查 | 1 | 检查用户权限 |
| 用户权限管理 | 5 | 授予、撤销、查询、批量操作 |
| 角色权限管理 | 5 | 授予、撤销、查询、批量操作 |
| 查询统计 | 2 | 权限查询和统计 |
| 缓存管理 | 2 | 缓存清理 |

### 使用场景

- **数据查询权限控制** - 控制用户对不同查询编码的访问权限
- **角色权限管理** - 通过角色批量管理用户权限
- **动态权限检查** - 在业务代码中动态检查权限
- **权限审计** - 查询和统计权限分配情况

## 🛠️ 开发指南

### 环境要求

- Java 8+
- Spring Boot 2.x
- MyBatis-Plus
- MySQL/PostgreSQL

### 依赖配置

权限管理功能已集成在 `naas-kernel` 模块中，无需额外配置。

### 配置说明

```yaml
# 权限检查开关（可选）
naas:
  checkPerimissonSwitch: true
```

## 📝 更新日志

### v1.0.0 (2025-01-28)
- ✅ 实现完整的权限管理功能
- ✅ 支持用户直接权限和角色权限
- ✅ 提供完整的 REST API 接口
- ✅ 集成缓存机制和事务支持
- ✅ 添加权限验证注解支持
- ✅ 完善文档和使用指南

## 🤝 贡献指南

### 报告问题
如果发现问题或有改进建议，请：
1. 查看现有文档是否已有解决方案
2. 在项目中创建 Issue
3. 提供详细的问题描述和复现步骤

### 文档贡献
欢迎改进文档：
1. Fork 项目
2. 修改或添加文档
3. 提交 Pull Request

## 📞 支持与联系

- **技术支持**: 查看相关文档或联系系统管理员
- **功能建议**: 通过项目 Issue 提交建议
- **紧急问题**: 联系项目维护团队

## 📄 许可证

本项目采用企业内部许可证，仅供内部使用。

---

**文档维护**: Claude 4.0 sonnet  
**最后更新**: 2025-01-28  
**版本**: v1.0.0

---

## 🔗 相关链接

- [项目主页](../README.md)
- [API 文档](./QueryCodePermissionAPI.md)
- [快速开始](./QueryCodePermissionQuickStart.md)
- [功能说明](./QueryCodePermissionService.md)
