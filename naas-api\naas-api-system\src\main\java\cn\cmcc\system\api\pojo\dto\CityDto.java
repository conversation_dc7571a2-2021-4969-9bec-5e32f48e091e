package cn.cmcc.system.api.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 行政区划及边界对象 t_city_list
 *
 * <AUTHOR>
 * @date 2021-04-13
 */
@Data
public class CityDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 编码 */
    private String code;

    /** 名称 */
    private String name;

    /** 父级id */
    private Long parentId;

    /** 父级ids */
    private String parentIds;

    /** 状态 */
    private String status;

    /** 排序 */
    private Long sort;

    /** 层级 */
    private Integer level;

    /** 边界 */
    private String geom;

    /** 边界 */
    private String geom2;

    /** 中心经度 */
    private String centerLon;

    /** 中心纬度 */
    private String centerLat;

    /** 父级名称 */
    private String parentName;

    /** 父级编码 */
    private String parentCode;

    /**
     * 省份
     */
    private String provinceCode;

    private String provinceName;

    /** 子节点 */
    private List<CityDto> children = new ArrayList<>();

}
