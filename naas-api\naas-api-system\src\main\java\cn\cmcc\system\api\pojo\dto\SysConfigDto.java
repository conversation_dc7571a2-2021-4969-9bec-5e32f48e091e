/**
 *
 *
 * @Title: SysConfigDto.java
 * <AUTHOR>
 * @date 2022年9月19日
 * @version V1.0
 */
package cn.cmcc.system.api.pojo.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class SysConfigDto implements Serializable{

	/**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 属性编码标识
     */
    private String code;

    /**
     * 属性值，如果是字典中的类型，则为dict的code
     */
    private String value;


    private String configType;

    /**
     * 是否数据字典
     */
    private String dictFlag;

    /**
     * 数据字典编码
     */
    private String dictTypeCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否返回前端
     */
    private String backFront;

    /**
     * 分类
     */
    private String classify;

    /**
     * 归属模块
     */
    private String belongSystem;

    /**
     * 模板
     */
    private String template;
}
