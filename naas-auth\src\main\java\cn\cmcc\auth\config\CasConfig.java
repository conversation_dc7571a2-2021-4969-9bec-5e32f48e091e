package cn.cmcc.auth.config;

import static cn.cmcc.auth.service.cas.CasServiceOauth2Impl.OAUTH2;

import java.io.IOException;
import java.io.StringReader;
import java.util.Collections;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.XMLConstants;

import cn.cmcc.auth.service.cas.*;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;
import org.xml.sax.helpers.XMLReaderFactory;

import cn.cmcc.auth.interceptor.CasLogoutInterceptor;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.auth.service.SysLoginService;
import cn.cmcc.common.cache.service.CacheService;
import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.enums.ProvinceCode;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.cmcc.system.api.service.RemoteUserService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Configuration
@Data
@Slf4j
public class CasConfig implements InitializingBean, ICasService , WebMvcConfigurer {

    /**
     * 重定向
     */
    public static final String REDIRECT = "redirect",
    /**
     * 用户名属性
     */
    USER_NAME = "userName",
    /**
     * 注销参数名称
     */
    LOGOUT_PARAMETER_NAME = "logoutRequest",
    /**
     * 根后缀
     */
    ROOT_SUFFIX = "/",
    /**
     * 登录后缀
     */
    LOGIN_SUFFIX = "/login",
    /**
     * 票据前缀
     */
    TICKET_PREFIX = "ticket:",
    /**
     * 注销前缀
     */
    LOGOUT_PREFIX = "logout:",
    /**
     * 缓存类型
     */
    CACHE_TYPE = "CAS_CACHE";

    /**
     * 开关
     */
    @Value("${cas.enable:false}")
    private boolean enable;

    /**
     * 开关
     */
    @Value("${cas.impl:default}")
    private String impl;

    /**
     * URL
     */
    @Value("${cas.serverUrl:}")
    private String serverUrl;

    /**
     * 登录后缀
     */
    @Value("${cas.serverLoginSuffix:}")
    private String serverLoginSuffix;

    /**
     * 注销后缀
     */
    @Value("${cas.serverLogoutSuffix:}")
    private String serverLogoutSuffix;

    /**
     * CAS 服务器 URL
     */
    @Value("${cas.casServerUrl:}")
    private String casServerUrl;

    /**
     * CAS 登录后缀
     */
    @Value("${cas.casServerLoginSuffix:}")
    private String casServerLoginSuffix;

    /**
     * CAS 服务参数
     */
    @Value("${cas.casServerServiceParameter:service}")
    private String casServerServiceParameter;

    /**
     * CAS 验证后缀
     */
    @Value("${cas.casServerValidateSuffix:}")
    private String casServerValidateSuffix;

    /**
     * CAS 登出后缀
     */
    @Value("${cas.casServerLogoutSuffix:}")
    private String casServerLogoutSuffix;

    /**
     * CAS 票据参数
     */
    @Value("${cas.casTicketParameter:ticket}")
    private String casTicketParameter;

    /**
     * 其他参数
     */
    @Value("${cas.otherParameters:}")
    private String otherParameters;

    /**
     * CAS 退出URL
     */
    @Value("${cas.casLogoutUrl:/casLogin/**}")
    private String casLogoutUrl;

    /**
     * CAS 登录 URL
     */
    private String casLoginUrl;

    /**
     * CAS 验证 URL
     */
    private String casValidateUrl;

    /**
     * 过期时间
     */
    private long expire;

    /**
     * 请求方式，默认get
     */
    @Value("${cas.requestType:get}")
    private String requestType;

    @Autowired
    private SysLoginService loginService;
    @Autowired
    private CacheService cacheService;

    private ICasService casService;

    @Resource
    private CasLogoutInterceptor casLogoutInterceptor;

    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        registry.addInterceptor(casLogoutInterceptor)
        		.addPathPatterns("/casLogin")
                .addPathPatterns(casLogoutUrl)
                .order(-20);
    }

    @Override
    public void afterPropertiesSet() {
        if (!this.enable) {
            log.warn("The cas switch is off.");
            return;
        }

        if (OAUTH2.equals(this.impl)) {
        	casService = new CasServiceOauth2Impl(this,remoteUserService);
        }// 河南：iamcaspticket/HTTP POST/JSON/accountID
        else if (ProvinceCode.HA.name().equals(this.impl)) {
            casService = new CasServiceHAImpl(this);
        } // 吉林：ticket/HTTP POST/URL encode/ACCOUNT
        else if (ProvinceCode.JL.name().equals(this.impl)) {
            casService = new CasServiceJLImpl(this);
        } // 山西：iamcaspticket/DES/RPC/XML/name
        else if (ProvinceCode.SX.name().equals(this.impl)) {
            casService = new CasServiceSXImpl(this);
        } // 河北：ticket/WSDL/XML/accId
        else if (ProvinceCode.HE.name().equals(this.impl)) {
            casService = new CasServiceHEImpl(this);
        } // 上海
        else if (ProvinceCode.SH.name().equals(this.impl)) {
            casService = new CasServiceSHImpl(this);
        }// 云南
        else if (ProvinceCode.YN.name().equals(this.impl)) {
            casService = new CasServiceYNImpl(this);
        } else if (ProvinceCode.BJ.name().equals(this.impl)) {
            casService = new CasServiceBJImpl(this);
        }
        // 默认：ticket/HTTP GET/XML/cas:user/logout
        else casService = new CasServiceImpl(this);
    }

    @Override
    public AjaxResult login(HttpServletRequest request) {
        if (!this.enable) {
            log.error("Please turn on the cas switch.");
            return AjaxResult.success(Collections.singletonMap("redirect", LOGIN_SUFFIX));
        }
        return casService.login(request);
    }

    @Override
    public void logout(HttpServletRequest request) {
        if (!this.enable) {
            log.error("Please turn on the cas switch.");
            return;
        }
        casService.logout(request);
    }

    @Override
    public boolean tokenExpired(String token) {
        if (!this.enable) return false;
        return casService.tokenExpired(token);
    }

    /**
     * 获取文本（默认）
     *
     * @param xml            XML
     * @param element        元素
     * @param suffixMatching 后缀匹配
     * @return 文本
     */
    public String getTextForElement(String xml, String element, boolean suffixMatching) throws IOException, SAXException {
    	XMLReader reader = XMLReaderFactory.createXMLReader();
        reader.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
        reader.setFeature("http://xml.org/sax/features/external-general-entities", false);
        reader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        reader.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        StringBuilder sb = new StringBuilder();

        DefaultHandler handler = new DefaultHandler() {

            private boolean found = false;

            @Override
            public void startElement(String uri, String localName, String qName, Attributes attributes) {
                if (localName.equals(element) || (suffixMatching && qName.endsWith(element))) {
                    this.found = true;
                }
            }

            @Override
            public void endElement(String uri, String localName, String qName) {
                if (localName.equals(element) || (suffixMatching && qName.endsWith(element))) {
                    this.found = false;
                }
            }

            @Override
            public void characters(char[] ch, int start, int length) {
                if (this.found) {
                    sb.append(ch, start, length);
                }
            }

        };
        reader.setContentHandler(handler);
        reader.setErrorHandler(handler);
        reader.parse(new InputSource(new StringReader(xml)));

        return sb.toString();
    }

    /**
     * 获取真实IP（吉林）
     *
     * @param request 请求
     * @return IP
     */
    public String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || Constants.Common.UNKNOWN.equalsIgnoreCase(ip))
            ip = request.getHeader("Proxy-Client-IP");
        if (ip == null || ip.isEmpty() || Constants.Common.UNKNOWN.equalsIgnoreCase(ip))
            ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip == null || ip.isEmpty() || Constants.Common.UNKNOWN.equalsIgnoreCase(ip))
            ip = request.getRemoteAddr();
        return ip;
    }

}
