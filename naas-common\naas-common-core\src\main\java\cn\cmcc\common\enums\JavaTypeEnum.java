/**
 * @Title: JavaTypeEnum.java
 * @Package cn.cmcc.common.enums
 * 
 * <AUTHOR>
 * @date 2022年5月31日
 * @version V1.0
 */
package cn.cmcc.common.enums;

import java.math.BigDecimal;
import java.text.ParseException;

import cn.cmcc.common.utils.DateUtils;
import cn.cmcc.common.utils.bean.ObjectUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum JavaTypeEnum {
	
	STRING("String", "字符串",IdTypeEnums.UUID){
        @Override
        public  Object conver(Object value){
            if(ObjectUtils.isNotEmpty(value))
                return value.toString();
            return null;
        }

		@Override
		public String genTableField(String fieldName,Integer length) {
			if (length != null && length == 0) {
                return fieldName + " text";
            }
			return fieldName + " varchar(" + length + ")" ;
		}

		@Override
		public String getDbType(Integer length) {
			if (length != null && length == 0) {
                return "text";
            }
			return  "varchar(" + length + ")" ;
		}

		@Override
		public FormTypeEnum getFromType() {
			return FormTypeEnum.TEXT;
		}
    },
	INTEGER("Integer","整形",IdTypeEnums.AUTO){
        @Override
        public  Object conver(Object value){
            if(ObjectUtils.isNotEmpty(value))
                return Integer.valueOf(value.toString());
            return null;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
        	return fieldName + " int4" ;
		}
		@Override
		public String getDbType(Integer length) {
			return "int4";
		}
		
		@Override
		public FormTypeEnum getFromType() {
			return FormTypeEnum.NUMBER;
		}
    },
	LONG("Long", "长整形",IdTypeEnums.AUTO){
        @Override
        public  Object conver(Object value){
            if(ObjectUtils.isNotEmpty(value))
                return Long.valueOf(value.toString());
            return null;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
			return fieldName + " int8" ;
		}
        
        @Override
		public String getDbType(Integer length) {
			return "int8";
		}
        
        @Override
		public FormTypeEnum getFromType() {
			return FormTypeEnum.NUMBER;
		}
    },
	BIGDECIMAL("BigDecimal","高精度计算类型"){
        @Override
        public  Object conver(Object value){
            if(ObjectUtils.isNotEmpty(value))
                return new BigDecimal(value.toString());
            return null;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
        	return fieldName + " numeric" ;
		}
        
        @Override
		public String getDbType(Integer length) {
			return "numeric";
		}
        
        @Override
		public FormTypeEnum getFromType() {
			return FormTypeEnum.NUMBER;
		}
    },
	DATE("Date","日期类型"){
        @Override
        public  Object conver(Object value) {
            if (ObjectUtils.isNotEmpty(value) && value instanceof String) {
                try {
                    return DateUtils.parseDate(value.toString(), DateUtils.parsePatterns);
                } catch (ParseException e) {
                	log.error("DateUtils.parseDate error:",e);
                }
            }
            return value;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
        	return fieldName + " date" ;
		}
        
        @Override
		public String getDbType(Integer length) {
			return "date";
		}
        
        @Override
		public FormTypeEnum getFromType() {
			return FormTypeEnum.DATE;
		}
    },
	DATE_TIME("DateTime","日期时间类型"){
        @Override
        public  Object conver(Object value) {
            if (ObjectUtils.isNotEmpty(value) && value instanceof String) {
                try {
                    return DateUtils.parseDate(value.toString(), DateUtils.parsePatterns);
                } catch (ParseException e) {
                	log.error("DateUtils.parseDate error:",e);
                }
            }
            return null;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
        	return fieldName + " timestamp(6)" ;
		}
        
        @Override
		public String getDbType(Integer length) {
			return "timestamp(6)";
		}
        
        @Override
		public FormTypeEnum getFromType() {
			return FormTypeEnum.DATE;
		}
    },
	GEOMETRY("geometry","空间对象"){
        @Override
        public  Object conver(Object value){
            return "SRID=4326;" + value;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
        	return fieldName + " geometry(GEOMETRY,4326)" ;
		}
        @Override
		public String getDbType(Integer length) {
			return "geometry(GEOMETRY,4326)";
		}
        
        @Override
		public FormTypeEnum getFromType() {
			return null;
		}
    },
    COMPOSITE_STAGE("StageCompositeBO", "阶段对象"){
        @Override
        public  Object conver(Object value){
            return null;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
        	return null ;
		}
        @Override
		public String getDbType(Integer length) {
			return null;
		}
        
        @Override
		public FormTypeEnum getFromType() {
			return null;
		}
    },
    BOOLEAN("Boolean","布尔值") {
        @Override
        public Object conver(Object value) {
            if (ObjectUtils.isNotEmpty(value)) {
                try {
                    return Boolean.parseBoolean(String.valueOf(value));
                } catch (Exception e) {
                	log.error("Boolean.parseBoolean error:",e);
                }
            }
            return null;
        }
        @Override
		public String genTableField(String fieldName,Integer length) {
        	return fieldName + " bool" ;
		}
        @Override
        public String getDbType(Integer length) {
			return "bool";
		}
        
        @Override
		public FormTypeEnum getFromType() {
			return FormTypeEnum.SWITCH;
		}
    }

    ;

    /**
     * 值
     */
    private final String value;

    /**
     * 名称
     */
    private final String name;
    
    /**
     * ID策略
     */
    private final IdTypeEnums idType;

    public  abstract Object conver(Object value);
    
    public  abstract String genTableField(String fieldName,Integer length);
    
    public  abstract String getDbType(Integer length);
    
    public abstract FormTypeEnum getFromType();

    public static JavaTypeEnum getByValue(String value) {
        for (JavaTypeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

    JavaTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
        this.idType = null;
    }
    
    JavaTypeEnum(String value, String name,IdTypeEnums IdType) {
        this.value = value;
        this.name = name;
        this.idType = IdType;
    }

	public String getValue() {
		return value;
	}

	public String getName() {
		return name;
	}
	
	public IdTypeEnums getIdType() {
		return idType;
	}
    
}
