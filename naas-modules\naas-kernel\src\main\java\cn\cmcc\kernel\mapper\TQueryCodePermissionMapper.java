package cn.cmcc.kernel.mapper;

import cn.cmcc.kernel.domain.TQueryCodePermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用查询编码权限表 数据层
 *
 * <AUTHOR> 4.0 sonnet
 */
@Mapper
public interface TQueryCodePermissionMapper extends BaseMapper<TQueryCodePermission> {

    /**
     * 根据用户名查询用户的所有权限
     *
     * @param username 用户名
     * @return 权限列表
     */
    List<TQueryCodePermission> selectByUsername(@Param("username") String username);

    /**
     * 根据用户名和查询编码查询特定权限
     *
     * @param username 用户名
     * @param queryCode 查询编码
     * @return 权限信息
     */
    TQueryCodePermission selectByUsernameAndCode(@Param("username") String username, 
                                                 @Param("queryCode") String queryCode);

    /**
     * 根据用户名和查询编码查询特定权限（指定权限类型）
     *
     * @param username 用户名
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     * @return 权限信息
     */
    TQueryCodePermission selectByUsernameAndCodeAndType(@Param("username") String username,
                                                        @Param("queryCode") String queryCode,
                                                        @Param("permissionType") String permissionType);

    /**
     * 根据用户名和查询编码删除特定权限
     *
     * @param username 用户名
     * @param queryCode 查询编码
     * @return 影响行数
     */
    int deleteByUsernameAndCode(@Param("username") String username, 
                                @Param("queryCode") String queryCode);

    /**
     * 根据用户名和查询编码删除特定权限（指定权限类型）
     *
     * @param username 用户名
     * @param queryCode 查询编码
     * @param permissionType 权限类型
     * @return 影响行数
     */
    int deleteByUsernameAndCodeAndType(@Param("username") String username,
                                       @Param("queryCode") String queryCode,
                                       @Param("permissionType") String permissionType);

    /**
     * 根据查询编码查询所有有权限的用户
     *
     * @param queryCode 查询编码
     * @return 权限列表
     */
    List<TQueryCodePermission> selectByQueryCode(@Param("queryCode") String queryCode);

    /**
     * 获取用户可访问的所有查询编码
     *
     * @param username 用户名
     * @param permissionType 权限类型
     * @return 查询编码列表
     */
    List<String> selectAccessibleCodesByUsername(@Param("username") String username,
                                                 @Param("permissionType") String permissionType);

    /**
     * 批量插入用户权限
     *
     * @param permissions 权限列表
     * @return 影响行数
     */
    int batchInsert(@Param("permissions") List<TQueryCodePermission> permissions);

    /**
     * 批量删除用户权限
     *
     * @param username 用户名
     * @param queryCodes 查询编码列表
     * @return 影响行数
     */
    int batchDeleteByUsernameAndCodes(@Param("username") String username,
                                      @Param("queryCodes") List<String> queryCodes);
}
