package cn.cmcc.common.enums;

import lombok.Getter;

@Getter
public enum EnableStatus {

	ENABLE("ENABLE", "启用"), 
	DISABLE("DISABLE", "禁用"), 
	LOCK("LOCK", "锁定"), 
	DELETED("DELETED", "删除");

	String code;
	String message;

	EnableStatus(String code, String message) {
	        this.code = code;
	        this.message = message;
	    }

	public static String getDescription(String status) {
		if (status == null) {
			return "";
		} else {
			for (EnableStatus s : EnableStatus.values()) {
				if (s.getCode().equals(status)) {
					return s.getMessage();
				}
			}
			return "";
		}
	}
}
