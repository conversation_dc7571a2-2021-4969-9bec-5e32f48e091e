package cn.cmcc.auth.config;

import javax.xml.ws.Endpoint;

import org.apache.cxf.Bus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.cmcc.auth.service.HECasService;
import cn.cmcc.common.webservice.interceptor.WebserviceAuthInterceptor;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * CAS 服务配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@Configuration
@RequiredArgsConstructor
public class HECasServiceConfig {

    private final Bus bus;
    private final HECasService heCasService;

    @Value("${cmcc.webservice.auth:false}")
    private boolean auth;

    @Bean
    public Endpoint heCasServiceEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(bus, heCasService);
        if (auth) {
            endpoint.getInInterceptors().add(new WebserviceAuthInterceptor());
        }
        endpoint.publish("/cas/he");
        return endpoint;
    }

}
