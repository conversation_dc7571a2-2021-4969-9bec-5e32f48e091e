package cn.cmcc.common.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import org.apache.commons.lang3.time.DateFormatUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */

@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String[] parsePatterns = {
    		"yyyyMM","yyyyMMdd",
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNowSSS()
    {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYYMMDDHHMMSSSSS);
        return now.format(formatter);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String date(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取当前日期
     *
     *
     * @return
     *
     */
    public static String getCurrentDate() {
      String datestr = null;
      SimpleDateFormat df = new SimpleDateFormat(YYYY_MM_DD);
      datestr = df.format(new Date());
      return datestr;
    }

    /**
     * 获取当前月份
     * */
    public static String getCurentMonth() {
  	  String datestr = null;
  	  SimpleDateFormat df = new SimpleDateFormat(YYYY_MM_DD);
  	  datestr = df.format(new Date());
  	  return datestr.replace("-", "").substring(0, 6);

    }

    /**
     * 获取当前日期时间
     *
     *
     * @return
     *
     */
    public static String getCurrentDateTime() {
      String datestr = null;
      SimpleDateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
      datestr = df.format(new Date());
      return datestr;
    }

    /**
     * 获取当前日期时间,并格式
     *
     * @return
     *
     */
    public static String getCurrentDateTime(String Dateformat) {
      String datestr = null;
      SimpleDateFormat df = new SimpleDateFormat(Dateformat);
      datestr = df.format(new Date());
      return datestr;
    }

    public static String dateToDateTimeStr(Date date) {
      String datestr = null;
      SimpleDateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
      datestr = df.format(date);
      return datestr;
    }


    /**
     * 将字符串日期转换为日期格式
     * 自定義格式
     *
     * @param datestr
     * @return
     *
     */
    public static Date stringToDate(String datestr, String dateformat) {
      Date date = new Date();
      SimpleDateFormat df = new SimpleDateFormat(dateformat);
      try {
        date = df.parse(datestr);
      } catch (ParseException e) {
    	  log.error(e.getMessage(),e);
      }
      return date;
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate)
    {
        long nd = 1000L * 24 * 60 * 60;
        long nh = 1000L * 60 * 60;
        long nm = 1000L * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 时间差second
     * @param endDate
     * @param startDate
     * @return
     */
    public static Long getDateDifference(Date endDate, Date startDate) {
        Long second = null;
        long diff = endDate.getTime() - startDate.getTime();
        second = diff / (1000);

        return second;
    }

    /**
     * 时间差second
     * @param endDate
     * @param startDate
     * @return
     */
    public static Long getDateDifferenceMil(Date endDate, Date startDate) {
        Long millisecond = null;
        long diff = endDate.getTime() - startDate.getTime();
        millisecond = diff;

        return millisecond;
    }

    /**
     * 在输入日期上增加（+）或减去（-）分钟
     *
     * @param date
     *      输入日期
     * @param iMimute
     *      要增加或减少的分钟
     */
    public static Date addMinute(Date date, int iMimute) {
      Calendar cd = Calendar.getInstance();

        cd.setTime(date);

        cd.add(Calendar.MINUTE, iMimute);

        return cd.getTime();
    }

    /**
     * 在输入日期上增加（+）或减去（-）小时
     *
     * @param date
     *      输入日期
     * @param iHour
     *      要增加或减少的小时
     */
    public static Date addHour(Date date, int iHour) {
      Calendar cd = Calendar.getInstance();

        cd.setTime(date);

        cd.add(Calendar.HOUR, iHour);

        return cd.getTime();
    }


    /**
     * 在输入日期上增加（+）或减去（-）小时
     *
     * @param iHour
     *      要增加或减少的小时
     */
    public static String addNowHour(int iHour) {
        Calendar cd = Calendar.getInstance();

        cd.setTime(new Date());

        cd.add(Calendar.HOUR, iHour);

        return parseDateToStr(YYYY_MM_DD_HH_MM_SS,cd.getTime());
      }

    /**
     * 在输入日期上增加（+）或减去（-）天数
     *
     * @param date
     *      输入日期
     *      要增加或减少的天数
     */
    public static Date addDay(Date date, int iday) {
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);
        cd.add(Calendar.DAY_OF_MONTH, iday);
        return cd.getTime();
    }

    /**
     * 在输入日期上增加（+）或减去（-）月份
     *
     * @param date
     *      输入日期
     * @param imonth
     *      要增加或减少的月分数
     */
    public static Date addMonth(Date date, int imonth) {
      Calendar cd = Calendar.getInstance();

        cd.setTime(date);

        cd.add(Calendar.MONTH, imonth);

        return cd.getTime();
    }

    /**
     * 在输入日期上增加（+）或减去（-）年份
     *
     * @param date
     *      输入日期
     * @param iyear
     *      要增加或减少的年数
     */
    public static Date addYear(Date date, int iyear) {
      Calendar cd = Calendar.getInstance();

        cd.setTime(date);

        cd.add(Calendar.YEAR, iyear);

        return cd.getTime();
    }

    /**
     * Date转LocalDateTime
     * @param date Date
     * @return LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        try {
            Instant instant = date.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            return instant.atZone(zoneId).toLocalDateTime();
        } catch (Exception e) {
        	log.error(e.getMessage(),e);
        }
        return null;
    }

    /**
     * 判断是否是有效的日期字符串
     *
     * @param string
     * @return boolean
     */
    public static boolean isValidDate(String string) {
        boolean convertSuccess = true;
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        // 如果是时间戳
        if (string.matches("^[0-9]{13}$")) {
            try {
                Date date = new Date(Long.parseLong(string));
                return true;
            } catch (Exception e) {
                // e.printStackTrace();
                convertSuccess = false;
            }
        } else {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            try {
                // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
                format.setLenient(false);
                format.parse(string);
            } catch (ParseException e) {
                // e.printStackTrace();
                convertSuccess = false;
            }
        }
        return convertSuccess;
    }
}
