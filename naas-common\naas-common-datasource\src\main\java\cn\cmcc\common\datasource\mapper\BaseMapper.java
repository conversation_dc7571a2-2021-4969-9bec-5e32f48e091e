package cn.cmcc.common.datasource.mapper;

import java.util.Collection;

/**
 * TODO(用一句话描述该文件做什么)
 *
 * <AUTHOR>
 * @date 2023年1月13日
 * @version V1.0
 */
public interface BaseMapper<T> extends com.baomidou.mybatisplus.core.mapper.BaseMapper<T> {

	/**
	 * 
	 * 批量插入，拼接成一条SQL 
	 *
	 * @Title: InsertBatchSomeColumn   
	 * @param entityList
	 * @return Integer
	 * @throws
	 */
	public Integer insertBatchSomeColumn(Collection<T> entityList);
}
