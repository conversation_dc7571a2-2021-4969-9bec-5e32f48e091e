package cn.cmcc.common.config;

import cn.hutool.core.text.csv.CsvWriteConfig;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: csv写入配置
 * @date 2025/4/28 10:55
 */
public class CustomCsvWriteConfig extends CsvWriteConfig {
    public CustomCsvWriteConfig() {
        super();
    }
    /**
     * <AUTHOR>  2025/4/28 11:00
     * @description:  获取字段分割符
     * @param
     * @return char
     */
    public char getFieldSeparator(){
        return this.fieldSeparator;
    }

    /**
     * <AUTHOR>  2025/4/28 11:00
     * @description:  获取表头简称，解决中文表头情况，key为字段名，value为中文表头
     * @param
     * @return Map<String>
     */
    public Map<String,String> getHeaderAlias(){
        return this.headerAlias;
    }
}
