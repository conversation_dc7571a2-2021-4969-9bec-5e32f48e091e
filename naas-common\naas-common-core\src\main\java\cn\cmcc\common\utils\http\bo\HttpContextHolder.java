/**
 *
 *
 * @Title: HttpContextHolder.java
 * <AUTHOR>
 * @date 2022年10月12日
 * @version V1.0
 */
package cn.cmcc.common.utils.http.bo;

import org.springframework.core.NamedThreadLocal;

import lombok.Builder;
import lombok.Data;

public class HttpContextHolder {

	private static final ThreadLocal<RequestConfig> threadLocal = new NamedThreadLocal<>("HTTP请求上下文信息");

    public static void set(RequestConfig requestConfig) {
        threadLocal.set(requestConfig);
    }

    public static RequestConfig get() {
        return threadLocal.get();
    }

    public static void remove() {
        threadLocal.remove();
    }

	@Data
	@Builder
	public static class RequestConfig{
		private Integer connectTimeout;
		private Integer readTimeout;
	}
}
