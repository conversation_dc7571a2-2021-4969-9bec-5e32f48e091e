package cn.cmcc.cad.cuting.service;

import java.util.List;
import java.util.Map;

import cn.cmcc.cad.cuting.entity.CadCutingBoundaryConfig;
import cn.cmcc.cad.cuting.entity.CadCutingConfig;
import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadBlockEntity;
import cn.cmcc.cad.entity.XhxkCadImage;
import cn.cmcc.common.web.domain.AjaxResult;

public interface ICadCutingService {
	
	/**
	 * 参数配置:INDOOR_Boundary_Config
	 * @return
	 */
	public Map<String, CadCutingBoundaryConfig> boundaryConfig();
	
	/**
	 * 根据图框配置数据（参数配置:INDOOR_Boundary_Config），获取图纸中使用的图框
	 * @param cadImage
	 * @param boundaryConfig - 图框配置数据，来源参数配置表:INDOOR_Boundary_Config
	 * @return
	 */
	public List<XhxkCadBlockEntity> getCadBlockConfigList(XhxkCadImage cadImage,Map<String, CadCutingBoundaryConfig> boundaryConfig);
	
	/**
	 * 获取Block对像，主要用于切割图纸
	 * @param indoorCadBlockEntities
	 * @return
	 */
	Map<String,List<XhxkCadBaseEntity>> getCadBlockEntities(XhxkCadImage cadImage);
	
	
	
	
	/**
	 * 获取自动切割图纸边界参数
	 * @param cadImage
	 * @param cadBlockEntities
	 * @param indoorCadBaseEntities
	 * @param cadData
	 * @param insertObjectNameMap
	 * @return
	 */
	public List<CadCutingConfig> getCutBoundary(XhxkCadImage cadImage,List<XhxkCadBlockEntity> cadBlockEntities,Map<String,List<XhxkCadBaseEntity>> cadBlockBaseEntityMap);
	

	/**
	 * 批量切割图纸
	 * @param cutConfigs - 图框边界参数
	 * @return
	 */
	public List<CadCutingConfig> cutDwgByBoundary(XhxkCadImage cadImage,List<CadCutingConfig> cutConfigs);
	
	/**
	 * 切割图纸
	 * @param cadImage
	 * @param cutConfigs - 切割参数，当切割系统图时是批量合并再切割
	 * @param dwgUnitTypeConfig
	 * @param xhxkModelFile
	 * @return
	 */
	public AjaxResult cutDwgByBoundaryItem(XhxkCadImage cadImage,List<XhxkCadBaseEntity> cadBaseEntities,String type,List<CadCutingConfig> cutConfigs);
	

	/**
	 * 获取切图参数
	 * @param image
	 * @param dwgUnitTypeConfig
	 * @return
	 */
	public List<CadCutingConfig> getCAdAutoCutConfigs(XhxkCadImage image);
	
}
