/**
 * @Title: CommonCallThirdParam.java
 * @Package cn.cmcc.common.pojo
 * @Description: 通用调用三方接口参数
 * <AUTHOR>
 * @date 2022年4月27日
 * @version V1.0
 */
package cn.cmcc.common.pojo.template;

import java.util.Map;

import cn.cmcc.common.enums.MsgTypeEnums;
import cn.cmcc.common.enums.ProtocolTypeEnums;
import cn.cmcc.common.enums.RetJsonType;
import lombok.Data;

@Data
public class CommonCallThirdParam {

	/**
	 * 通用查询编码
	 */
	private String code;
	
	/**
	 * 业务类型
	 */
	private String businessType;
	
	/**
	 * 协议类型，支持http，和webservice
	 */
	private String protocolType = ProtocolTypeEnums.HTTP.getValue();
	
	/**
	 * 报文类型,默认json，支持xml
	 */
	private String msgType = MsgTypeEnums.JSON.getValue();
	
	/**
	 * json类型， object：对象， array：数组
	 */
	private String jsonType = RetJsonType.OBJECT.getValue();
	
	/**
	 * 业务id字段
	 */
	private String buinessId;
	
	/**
	 * 三方接口地址,当协议是webservice时，值为WebserviceParam对象
	 */
	private String url;
	
	/**
	 * 返回成功字段
	 */
	private String returnFieldName;
	
	/**
	 * 返回成功值
	 */
	private String returnSuccessValue;
	
	/**
	 * xml的根节点名称
	 */
	private String rootName = "body";
	
	/**
	 * 当数据时数组时，xml的listname
	 */
	private String rootListName = "datas#data";
	
	/**
	 * 三方接口请求头
	 */
	private Map<String,String> headers;
}
