package cn.cmcc.common.utils.encryption;

import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

import org.apache.commons.codec.binary.Base64;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.utils.StringUtils;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;

/**
* <AUTHOR>
* @date 2021年6月23日 上午11:25:16
* @Description
* @version 1.0
*/

@Slf4j
public class RSAEncryptUtil {
	public static final int MAX_DECRYPT_BLOCK = 128;
	/**
	 * 从字符串中加载公钥
	 *
	 * @param publicKeyStr
	 *            公钥数据字符串
	 * @throws Exception
	 *             加载公钥时产生的异常
	 */
	public static RSAPublicKey genPublicKeyByStr(String publicKeyStr)
			throws Exception {
		try {
			byte[] buffer = Base64.decodeBase64(publicKeyStr);
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
			return (RSAPublicKey) keyFactory.generatePublic(keySpec);
		} catch (NoSuchAlgorithmException e) {
			throw new Exception("无此算法");
		} catch (InvalidKeySpecException e) {
			throw new Exception("公钥非法");
		} catch (NullPointerException e) {
			throw new Exception("公钥数据为空");
		}
	}


	public static RSAPrivateKey genPrivateKeyByStr(String privateKeyStr)
			throws Exception {
		try {
			byte[] buffer = Base64.decodeBase64(privateKeyStr);
			PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
		} catch (NoSuchAlgorithmException e) {
			throw new Exception("无此算法");
		} catch (InvalidKeySpecException e) {
			throw new Exception("私钥非法");
		} catch (NullPointerException e) {
			throw new Exception("私钥数据为空");
		}
	}

	 /**
     * 公钥加密
     *
     * @param data
     * @param publicKey
     * @return
     */
    public static String encrypt(String data, RSAPublicKey publicKey) {
    	 try {
             RSA rsa = new RSA(null, publicKey);
             return rsa.encryptBase64(data, KeyType.PublicKey);
         } catch (Exception e) {
             log.error(e.getMessage(),e);
             throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
         }
    }

    /**
     * 公钥加密
     *
     * @param data
     * @param privateKey
     * @return
     */
    public static String encrypt(String data, RSAPrivateKey privateKey) {
    	 try {
             RSA rsa = new RSA(privateKey, null);
             return rsa.encryptBase64(data, KeyType.PrivateKey);
         } catch (Exception e) {
             log.error(e.getMessage(),e);
             throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
         }
    }

	/**
	 * 私钥解密过程
	 *
	 * @param str 密文数据
	 * @param privateKey
	 * @return 明文
	 * @throws Exception
	 *             解密过程中的异常信息
	 */
	public static String decrypt(String str,RSAPrivateKey privateKey)
			throws Exception {
		try {
            RSA rsa = new RSA(privateKey, null);
            return rsa.decryptStr(str, KeyType.PrivateKey);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw new RuntimeException("解密字符串[" + str + "]时遇到异常", e);
        }
	}

	/**
	 * 私钥解密过程
	 *
	 * @param data 密文数据
	 * @param publicKey
	 * @return 明文
	 * @throws Exception
	 *             解密过程中的异常信息
	 */
	public static String decrypt(String data,RSAPublicKey publicKey)
			throws Exception {
		try {
            RSA rsa = new RSA(null, publicKey);
            return rsa.decryptStr(data, KeyType.PublicKey);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw new RuntimeException("解密字符串[" + data + "]时遇到异常", e);
        }
	}

	/**
	 * 签名
	 *
	 * @param data
	 * @param privateKey
	 * @return
	 * @throws Exception
	 * String
	 */
	public static String sign(byte[] data, String privateKey) throws Exception {
		if (data == null || StringUtils.isEmpty(privateKey)) {
			return StringUtils.EMPTY;
		}
		/**
		 * sonar 漏洞，改用下面的，实际上是一模一样的。
		 *
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
		KeyFactory keyFactory = KeyFactory.getInstance("RSA");
		PrivateKey pk = keyFactory.generatePrivate(pkcs8KeySpec);

		Signature signature = Signature.getInstance("MD5withRSA");
		signature.initSign(pk);
		signature.update(data);

		return Base64.encodeBase64String(signature.sign());*/

		Sign sgin = new Sign(SignAlgorithm.MD5withRSA,Constants.PRIVAGE_KEY,null);
		return Base64.encodeBase64String(sgin.sign("asdfadsfssfasda"));
	}

	/**
	 * <AUTHOR> @date: 2024/8/29 上午11:26
	 * @description:AI工艺验收结果 RSA公钥加密。使用功能：基站验收平台获取报告，鉴权时用户名密码加密
	 * @param str 想要加密的明文
	 * @param publicKey 公钥
	 */
	public static String encrypt( String str, String publicKey ) throws Exception{
		//base64编码的公钥
		byte[] decoded = Base64.decodeBase64(publicKey);
		RSAPublicKey pubKey = (RSAPublicKey)KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
		//RSA加密
		Cipher cipher = Cipher.getInstance("RSA");// NOSONAR
		cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        return Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
	}
}
