package cn.cmcc.cad.entity;

import java.io.Serializable;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

@Data
public class XhxkCadPoint implements Serializable{
	private static final long serialVersionUID = -4087587190637405232L;
	private double x;
	private double y;
	@JSONField(serialize = false)
	private double z;

	public XhxkCadPoint(double x, double y) {
		this.x = x;
		this.y = y;
	}

	public XhxkCadPoint(double x, double y, double z) {
		this.x = x;
		this.y = y;
		this.z = z;
	}

}
