package cn.cmcc.auth;

import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;

import cn.cmcc.common.security.annotation.EnableCustomConfig;

/**
 * 认证授权中心
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
		DruidDataSourceAutoConfigure.class,
		RedisAutoConfiguration.class,
		RedissonAutoConfiguration.class})
public class NaasAuthApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(NaasAuthApplication.class, args);
    }
}
