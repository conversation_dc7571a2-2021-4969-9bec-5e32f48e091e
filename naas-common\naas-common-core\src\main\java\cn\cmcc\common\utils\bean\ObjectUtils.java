/**
 * @Title: ObjectUtils.java
 * @Package cn.cmcc.common.utils.bean
 * 
 * <AUTHOR>
 * @date 2022年1月11日
 * @version V1.0
 */
package cn.cmcc.common.utils.bean;

import java.lang.reflect.Field;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;


@Slf4j
public class ObjectUtils extends org.apache.commons.lang3.ObjectUtils{

	/**
     * 
     * @Title: allAttrIsNull   
     * @Description: 判断所有属性是否为空  
     * @param: @param object
     * @param: @return      
     * @return: boolean      
     * @throws
     */
    public static  boolean allAttrIsNull(Object object){
        Class clazz = (Class)object.getClass(); 
        Field fields[] = clazz.getDeclaredFields();
        boolean flag = true; 
        for(Field field : fields){
            field.setAccessible(true);
            Object fieldValue = null;
            try {
                fieldValue = field.get(object); 
            } catch (IllegalArgumentException e) {
            	log.error(e.getMessage(),e);
            } catch (IllegalAccessException e) {
            	log.error(e.getMessage(),e);
            }
            if(fieldValue != null){  
            	//只要有一个属性值不为null 就返回false 表示对象不为null
                flag = false;
                break;
            }
        }
        return flag;
    }
    
    public static String toStr(final Object obj) {
        return obj == null ? StringUtils.EMPTY : obj.toString();
    }

    public static<T> T setDefaultIfEmpty(T value, T defaultValue) {
        return isEmpty(value) ? defaultValue : value;
    }
}
