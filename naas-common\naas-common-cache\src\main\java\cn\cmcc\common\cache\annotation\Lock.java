package cn.cmcc.common.cache.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 *
 *
 * <AUTHOR>
 * @date 2022年12月27日
 * @version V1.0
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Lock {

	/*
	 * key
	 */
    String key() default "";

    /*
     * key前缀，默认采用类全限定名:方法名
     */
    String  prefix() default "";

    /*
     * 等待时间,0表示不等待
     */
    long waitTime() default 0L;

    /*
     * 有效期5分钟
     */
    long leaseTime() default 300L;

    /*
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /*
     * 返回错误信息
     */
    String msg() default "请勿重复操作！";
}
