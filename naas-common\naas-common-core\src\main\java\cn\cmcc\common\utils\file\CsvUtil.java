package cn.cmcc.common.utils.file;

import cn.cmcc.common.config.CustomCsvWriteConfig;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.text.CharsetKit;
import cn.hutool.core.text.csv.CsvWriteConfig;
import cn.hutool.core.text.csv.CsvWriter;
import com.google.common.collect.Lists;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.UnsupportedCharsetException;
import java.nio.file.Files;
import java.util.*;

public class CsvUtil {
    private static Logger log = LoggerFactory.getLogger(CsvUtil.class);
    private static CSVFormat userFormat = CSVFormat.DEFAULT.builder()
            .setIgnoreEmptyLines(false)
            .setAllowMissingColumnNames(true)
            .setHeader().setDelimiter(',')
            .build();

    @SuppressWarnings({"unchecked", "rawtypes"})
    public static void expCsv(Appendable out, List headers, List data, List columns)
            throws IOException {
        try (CSVPrinter printer = new CSVPrinter(out, userFormat)) {
            if (null != headers && !headers.isEmpty()) {
                printer.printRecord(headers);
            }
            if (null != data && !data.isEmpty()) {
                Iterator itr = data.iterator();
                while (itr.hasNext()) {
                    Object obj = itr.next(); // 将数据添加到csv字符串
                    if (obj != null) {
                        if (obj instanceof Map) {
                            Map objmap = (Map) obj;
                            if (null == columns || columns.isEmpty()) { // 如�?�没有限制，默认全部显示
                                printer.printRecord(objmap.values());
                            } else {
                                List tmp = new ArrayList();
                                for (int i = 0; i < columns.size(); i++) {
                                    tmp.add(objmap.get(columns.get(i)));
                                }
                                printer.printRecord(tmp);
                            }
                        } else if (obj instanceof Record) {
                            Record objrecord = (Record) obj;
                            Map<String, Object> map = objrecord.getColumns();
                            if (null == columns || columns.isEmpty()) { // 如�?�没有限制，默认全部显示
                                printer.printRecord(map.values());
                            } else {
                                List tmp = new ArrayList();
                                for (int i = 0; i < columns.size(); i++) {
                                    tmp.add(objrecord.get(columns.get(i) + ""));
                                }
                                printer.printRecord(tmp);
                            }
                        } else {
                            printer.printRecord(obj);
                        }
                    }
                    printer.flush();
                }
            }
        }
    }

    /**
     * 给csv文件追加列
     */
    public static void appendCsv(File sourceFile, List<String> headers,
                                 List<String> columns, String charsetName, File targetFile) {
        try (CSVReader reader = new CSVReader(Files.newBufferedReader(sourceFile.toPath(), Charset.forName(charsetName)));
             CSVWriter csvWriter = new CSVWriter(
                     Files.newBufferedWriter(targetFile.toPath(), Charset.forName(charsetName)),
                     CSVWriter.DEFAULT_SEPARATOR, CSVWriter.NO_QUOTE_CHARACTER,
                     CSVWriter.DEFAULT_ESCAPE_CHARACTER,
                     CSVWriter.DEFAULT_LINE_END)) {
            // csv文件首行添加表头
            String[] nextLine;
            while ((nextLine = reader.readNext()) != null) {
                //首行不添加数据,只添加表头
                if (reader.getLinesRead() == 1) {
                    String[] newLine = Arrays.copyOf(nextLine, nextLine.length + headers.size());
                    for (int i = 0; i < headers.size(); i++) {
                        newLine[nextLine.length + i] = headers.get(i);
                    }
                    csvWriter.writeNext(newLine);
                    continue;
                }
                // 添加自定义列和数据
                String[] newLine = Arrays.copyOf(nextLine, nextLine.length + headers.size());
                for (int i = 0; i < headers.size(); i++) {
                    newLine[nextLine.length + i] = columns.get(i);
                }
                // 将新的行数据写入输出文件
                csvWriter.writeNext(newLine);
            }
        } catch (Exception e) {
            log.error("CSV文件追加数据操作失败:{},文件名:{}", e.getMessage(), sourceFile.getName());
        }
    }

    /**
     * 可传入分隔符 建议 | ;
     *
     * @param sourceFile
     * @param headers
     * @param columns
     * @param charsetName
     * @param targetFile
     * @param delimiter
     */
    public static void appendCsv(File sourceFile, List<String> headers, List<String> columns, String charsetName,
                                 File targetFile, char delimiter) {
        try (CSVReader reader = new CSVReader(Files.newBufferedReader(sourceFile.toPath(), Charset.forName(charsetName)));
             /**
              * 参数说明
              * 设置 CSVWriter.NO_ESCAPE_CHARACTER 后,将不会对特殊字符进行转义,解决了比如分隔符是|,但是实际结果会被出现"|"的情况
              */
             CSVWriter csvWriter = new CSVWriter(
                     Files.newBufferedWriter(targetFile.toPath(), Charset.forName(charsetName)), delimiter,
                     CSVWriter.NO_QUOTE_CHARACTER, CSVWriter.NO_ESCAPE_CHARACTER,
                     CSVWriter.DEFAULT_LINE_END)) {
            // csv文件首行添加表头
            String[] nextLine;
            boolean isFirstLine = true;
            while ((nextLine = reader.readNext()) != null) {
                if (isFirstLine) {
                    String[] newLine = Arrays.copyOf(nextLine, nextLine.length + headers.size());
                    for (int i = 0; i < headers.size(); i++) {
                        newLine[nextLine.length + i] = headers.get(i);
                    }
                    csvWriter.writeNext(newLine);
                    isFirstLine = false;
                    continue;
                }
                String[] newLine = Arrays.copyOf(nextLine, nextLine.length + columns.size());
                for (int i = 0; i < columns.size(); i++) {
                    newLine[nextLine.length + i] = columns.get(i);
                }
                csvWriter.writeNext(newLine);
            }
        } catch (Exception e) {
            log.error("CSV文件追加数据操作失败:{},文件名:{}", e.getMessage(), sourceFile.getName());
        }
    }

    /**
     * 读取csv表头
     *
     * @param file      文件
     * @param charset   格式
     * @param lowerCase 是否小写
     * @return
     */
    public static List<String> header(File file, Charset charset, boolean lowerCase) {
        List<String> list = new ArrayList<>();
        CSVParser parser = null;
        try {
            parser = CSVParser.parse(new BOMInputStream(Files.newInputStream(file.toPath())),
                    charset, userFormat);
            Map<String, Integer> map = parser.getHeaderMap();
            for (String header : map.keySet()) {
                if (lowerCase) {
                    list.add(header.toLowerCase()
                                   .trim());
                } else {
                    list.add(header.trim());
                }
            }
        } catch (IOException e) {
            log.error("CsvUtil->header, read csv file failt：" + e, e);
        } finally {
            IOUtils.closeQuietly(parser);
        }
        return list;
    }

    /**
     * 默认文件编码UTF-8,表头小写
     *
     * @param file
     * @return
     */
    public static List<String> header(File file) {
        return header(file, CharsetKit.CHARSET_UTF_8,true);
    }


    /**
     * 读取csv表头，默认文件编码UTF-8,表头小写，字段为数字时使用引号包裹
     *
     * @param file
     * @param delimiter
     * @param charset
     * @return List<String>
     */
    public static List<String> header(File file, char delimiter, String charset) {
        Objects.requireNonNull(file, "File cannot be null");
        Objects.requireNonNull(charset, "Charset cannot be null");

        List<String> list = new ArrayList<>();
        try (CSVParser parser = CSVParser.parse(
                new BOMInputStream(Files.newInputStream(file.toPath())),
                Charset.forName(charset),
                CSVFormat.DEFAULT.builder()
                        .setIgnoreEmptyLines(false)
                        .setAllowMissingColumnNames(true)
                        .setHeader()
                        .setDelimiter(delimiter)
                        .build())) {
            List<String> headerList = parser.getHeaderNames();
            log.info("header:{}", headerList.toString());

            Map<String, Integer> headerMap = parser.getHeaderMap();
            if (headerMap != null && !headerMap.isEmpty()) {
                for (String header : headerMap.keySet()) {
                    // 处理表头字段为数字开头的情况
                    if (header.matches("^\\d.*")) {
                        header = "\"" + header + "\""; // 使用引号包裹
                    }
                    list.add(header.toLowerCase());
                }
            } else {
                log.info("CsvUtil->header: The CSV file does not contain a header row.");
            }
            if(headerList.size() != list.size()){
                log.error("错误,表头字段重复,文件名："+file.getName());
                return  null;
            }

        } catch (UnsupportedCharsetException e) {
            log.error("错误,获取文件表头,并不支持编码异常,编码:{}", charset, e);
            return null; // 或者抛出自定义异常
        } catch (Exception e) {
            log.error("错误,获取文件头异常, {}", e.getMessage(), e);
            return null; // 或者抛出自定义异常
        }

        return list;
    }

    public static List<Record> readCsv(File file, Charset charset) {
        List<Record> datas = new ArrayList<Record>();
        CSVParser parser = null;
        try {
            parser = CSVParser.parse(new BOMInputStream(Files.newInputStream(file.toPath())),
                    charset, userFormat);
            List<CSVRecord> list = parser.getRecords();
            Map<String, Integer> map = parser.getHeaderMap();
            for (CSVRecord record : list) {
                Record rd = new Record();
                for (String header : map.keySet()) {
                    rd.set(header.toLowerCase(), record.get(header));
                }
                datas.add(rd);
            }
        } catch (IOException e) {
            log.error("CsvUtil->header, read csv file failt：" + e, e);
        } finally {
            IOUtils.closeQuietly(parser);
        }
        return datas;
    }

    /**
     * BufferedReader 读取csv内容
     *
     * @param filePath
     * @return
     */
    public static ArrayList<String> readCsvByBufferedReader(String filePath, String separator, String newSeparator) {
        File csv = FileUtils.getFile(filePath);
        if(csv.setReadable(true) && csv.setWritable(true)) {
            try(InputStreamReader isr =  new InputStreamReader(new FileInputStream(csv), "UTF-8");
            		 BufferedReader br = new BufferedReader(isr)){
            	 String line = "";
                 ArrayList<String> records = new ArrayList<>();
                 try {
                     while ((line = br.readLine()) != null) {
                         //System.out.println(line);
                         records.add(line.replace(separator, newSeparator));
                     }
                     //System.out.println("csv表格读取行数：" + records.size());
                 } catch (IOException e) {
                	 log.error(e.getMessage(),e);
                 }
                 return records;
            } catch (Exception e) {
            	log.error(e.getMessage(),e);
            }

        }

        return Lists.newArrayList();

    }

    /**
     * 生成csv文件
     *
     * @param headers
     * @param headersCn
     * @param types
     * @param fileName
     * @param charset
     */
    public static void makeCsvFile(String headersCn, String types, String requireds, String example, String remark, String fileName, Charset charset) {
        List<String> list = new ArrayList<>();

        list.add(headersCn.substring(0, headersCn.length() - 1));
        if (StringUtils.isNotEmpty(types)) list.add(types.substring(0, types.length() - 1));
        if (StringUtils.isNotEmpty(requireds)) list.add(requireds.substring(0, requireds.length() - 1));
        if (StringUtils.isNotEmpty(example)) list.add(example.substring(0, example.length() - 1));
        if (StringUtils.isNotEmpty(remark)) list.add(remark.substring(0, remark.length() - 1));

        CsvWriter writer = cn.hutool.core.text.csv.CsvUtil.getWriter(fileName, charset);
        writer.write(list);
        writer.close();
    }

    /**
     * 生成csv文件
     *
     * @param list
     * @param fileName
     * @param charset
     */
    public static void makeCsvFile(List<String> list, String fileName, Charset charset) {
        CsvWriter writer = cn.hutool.core.text.csv.CsvUtil.getWriter(fileName, charset);
        writer.write(list);
        writer.close();
    }

    /**
     * <AUTHOR>  2025/4/3 17:35
     * @description: 生成文件并写入数据，无配置时使用key作为header,此种方式只能将实体写入，不能保证字段顺序，根据配置亦可填写中文表头
     * @param dataList 将要写如的数据，
     * @param absolutePath 文件绝对路径，若不存在则新建
     * @param config  csv文件配置，可空，为空时使用默认配置,以key作为header
     */
    public static <T> void generateFileAndWriteData(List<T> dataList, String absolutePath,CsvWriteConfig config){
        log.info("文件绝对路径:{}",absolutePath);
        // 可以通过设置FileWriter的编码来控制输出文件的编码格式
        try (FileWriter fileWriter = new FileWriter(absolutePath);// NOSONAR
            CsvWriter csvWriter = cn.hutool.core.text.csv.CsvUtil.getWriter(fileWriter,config)){
            csvWriter.writeBeans(dataList);
            csvWriter.flush();
        } catch (IOException e) {
            log.info("写入csv文件失败:",e.getMessage(),e);
        }
    }

    /**
     * <AUTHOR>  2025/4/3 17:35
     * @description: 生成文件并写入数据，无配置时使用key作为header
     * @param dataList 将要写如的数据，
     * @param absolutePath 文件绝对路径，若不存在则新建
     * @param config  csv文件配置，可空，为空时使用默认配置,以key作为header,
     * @param fields 字段顺序
     */
    public static void generateFileAndWriteData(List<Map<String,Object>> dataList, String absolutePath, CustomCsvWriteConfig config, List<String> fields){
        log.info("文件绝对路径:{}",absolutePath);
        // 可以通过设置FileWriter的编码来控制输出文件的编码格式
        try (FileWriter fileWriter = new FileWriter(absolutePath);// NOSONAR
            CsvWriter csvWriter = cn.hutool.core.text.csv.CsvUtil.getWriter(fileWriter,config)){
            csvWriter.writeHeaderLine(fields.toArray(new String[fields.size()]));
            //写入数据内容
            for(Map<String,Object> dataMap: dataList){
                String [] tmpStr = new String[fields.size()];
                for(int i=0; i<fields.size();i++){
                    tmpStr[i] = dataMap.get(fields.get(i)) + "";
                }
                csvWriter.writeLine(tmpStr);
            }
            csvWriter.flush();
        } catch (IOException e) {
            log.info("写入csv文件失败:",e.getMessage(),e);
        }
    }
}
