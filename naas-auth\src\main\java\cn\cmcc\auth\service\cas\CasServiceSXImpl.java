package cn.cmcc.auth.service.cas;

import static cn.cmcc.auth.config.CasConfig.CACHE_TYPE;
import static cn.cmcc.auth.config.CasConfig.LOGIN_SUFFIX;
import static cn.cmcc.auth.config.CasConfig.REDIRECT;
import static cn.cmcc.auth.config.CasConfig.ROOT_SUFFIX;
import static cn.cmcc.auth.config.CasConfig.TICKET_PREFIX;
import static cn.cmcc.auth.config.CasConfig.USER_NAME;

import java.net.InetSocketAddress;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.core.service.IoConnector;
import org.apache.mina.core.session.IoSession;
import org.apache.mina.filter.logging.LoggingFilter;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.serialization.ObjectSerializationCodecFactory;
import org.apache.mina.transport.socket.nio.NioSocketConnector;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ultrapower.casp.common.datatran.DataUtil;
import com.ultrapower.casp.common.datatran.data.CaspRequest;
import com.ultrapower.casp.common.datatran.data.Header;
import com.ultrapower.casp.common.datatran.data.ticket.TransferTicket;
import com.ultrapower.casp.common.encrypt.EncryptFactory;
import com.ultrapower.casp.common.encrypt.EncryptParam;
import com.ultrapower.casp.common.nio.NioDriverHandler;
import com.ultrapower.casp.common.nio.NioSocketData;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.common.cache.context.SysConfigContext;
import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.exception.BaseException;
import cn.cmcc.common.web.domain.AjaxResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 山西实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Slf4j
public class CasServiceSXImpl implements ICasService {

    /**
     * 头-体 分隔符
     */
    private static final String HEADER_BODY_SEPARATOR = "G|T",
            HEADER_BODY_SPLIT = "G\\|T";

    private final CasConfig config;

    /**
     * 应用代码
     */
    private final String appCode;

    /**
     * 应用程序密钥
     */
    private final String appKey;

    /**
     * 数据格式
     */
    private final String dataFormat;

    /**
     * 服务器列表
     */
    private final List<Server> serverList;

    public CasServiceSXImpl(CasConfig config) {
        this.config = config;
        JSONObject otherParameters = JSON.parseObject(config.getOtherParameters());
        this.appCode = otherParameters.getString("appCode");
        EncryptParam param = new EncryptParam();
        param.setKey("IAM");
        param.setValue(otherParameters.getString("appKey"));
        this.appKey = EncryptFactory.decode(param);
        this.dataFormat = otherParameters.getString("dataFormat");
        this.serverList = new ArrayList<>();
        for (Object s : otherParameters.getJSONArray("serverList")) {
            JSONObject server = (JSONObject) s;
            this.serverList.add(new Server(server.getString("id"), server.getString("ip"), server.getInteger("port")));
        }
        try {
            String service = URLEncoder.encode(String.format("%s%s", config.getServerUrl(), config.getServerLoginSuffix()),
                    StandardCharsets.UTF_8.name());
            config.setCasLoginUrl(String.format("%s%s?appCode=%s&%s=%s", config.getCasServerUrl(), config.getCasServerLoginSuffix(),
                    this.appCode, config.getCasServerServiceParameter(), service));
            log.info("casLoginUrl: {}", config.getCasLoginUrl());
            config.setCasValidateUrl(String.format("%s%s", config.getCasServerUrl(), config.getCasServerValidateSuffix()));
            log.info("casValidateUrl: {}", config.getCasValidateUrl());
            config.setExpire(SysConfigContext.getJwtSecretExpireSec());
            log.info("expire: {}", config.getExpire());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public AjaxResult login(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>(16);
        try {
            // 已经有登录信息
            if (validToken(request)) {
                data.put(REDIRECT, ROOT_SUFFIX);
                return AjaxResult.success(data);
            }

            String ticket = getTicket(request, config.getCasTicketParameter());
            if (StringUtils.isEmpty(ticket)) {
                log.info("Ticket is empty, jumps to the CAS login page.");
                data.put(REDIRECT, config.getCasLoginUrl());
            } else {
                log.info("Ticket: " + ticket);
                // 包引用了 Log4j 等依赖包，只引用没有依赖的类
                CaspRequest caspRequest = getRequest(ticket);
                String stringRequest = toString(caspRequest);
                String stringResponse = getResponse(stringRequest, caspRequest.getBody().getServerId());
                String account = toResponse(caspRequest.getBody().getTempKey(), stringResponse);
                if (StringUtils.isNotEmpty(account)) {
                    String token = (String) config.getLoginService().ssoLogin(account).get(SecurityConstants.TOKEN);
                    data.put(REDIRECT, ROOT_SUFFIX);
                    data.put(SecurityConstants.TOKEN, token);
                    data.put(USER_NAME, account);

                    // 票据-TOKEN
                    config.getCacheService().set(TICKET_PREFIX + ticket, token, config.getExpire(), TimeUnit.SECONDS, CACHE_TYPE);
                } else {
                    data.put(REDIRECT, config.getCasLoginUrl());
                    data.put("msg", stringResponse);
                }
            }
        }catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            if( ex instanceof InterruptedException) {
            	Thread.currentThread().interrupt();
            }
            data.put(REDIRECT, config.getCasLoginUrl());
            data.put("msg", ex.getMessage());
        }
        return AjaxResult.success(data);
    }

    /**
     * 编码
     *
     * @param key   键
     * @param value 值
     * @return 编码
     */
    public String encode(String key, String value)
            throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, NoSuchPaddingException, IllegalBlockSizeException, BadPaddingException {
        EncryptParam param = new EncryptParam();
        param.setKey(key);
        param.setValue(value);
        return EncryptFactory.encode(param);
    }

    /**
     * 解码
     *
     * @param key   键
     * @param value 值
     * @return 解码
     */
    public String decode(String key, String value) {
        EncryptParam param = new EncryptParam();
        param.setKey(key);
        param.setValue(value);
        return EncryptFactory.decode(param);
    }

    /**
     * 获取请求
     *
     * @param ticket 票据
     * @return 请求
     */
    public CaspRequest getRequest(String ticket) {
        CaspRequest request = new CaspRequest();

        Header header = new Header();
        header.setDataFormatMode(this.dataFormat);
        header.setRequestTypeCode("3");
        header.setAppCode(this.appCode);
        header.setSign(StringUtils.EMPTY);

        if (StringUtils.isNotEmpty(this.appKey)) {
            ticket = decode(this.appKey, ticket);
            log.info("Plaintext: {}.", ticket);
        }
        TransferTicket body = new TransferTicket();
        body.strToObj(ticket);
        body.setTempKey(DataUtil.generateTempKey());

        request.setHeader(header);
        request.setBody(body);
        return request;
    }

    /**
     * 转字符串
     *
     * @param request 请求
     * @return 字符串
     */
    public String toString(CaspRequest request)
            throws NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeySpecException, BadPaddingException, InvalidKeyException {
        String header = request.getHeader().objToStr();

        String body = request.getBody().objToStr();
        log.info("Body: {}.", body);
        if (StringUtils.isNotEmpty(this.appKey)) {
            body = encode(this.appKey, body);
            log.info("Ciphertext: {}.", body);
        }
        return header + HEADER_BODY_SEPARATOR + body;
    }

    /**
     * 获取响应
     *
     * @param request  请求
     * @param serverId 服务器ID
     * @return 响应
     */
    public String getResponse(String request, String serverId) throws InterruptedException {
        Server server = null;
        for (Server s : serverList) {
            if (StringUtils.equals(serverId, s.getId())) {
                server = s;
                break;
            }
        }
        if (server == null)
            throw new RuntimeException(String.format("Unable to find server [%s] configuration", serverId));

        log.info("Request: {}.", request);
        String response = null;

        IoConnector connector = null;
        IoSession session = null;
        try {
            connector = new NioSocketConnector();
            connector.getSessionConfig().setReadBufferSize(2048);
            connector.getFilterChain().addLast("logger", new LoggingFilter());
            connector.getFilterChain().addLast("codec", new ProtocolCodecFilter(new ObjectSerializationCodecFactory()));
            connector.setHandler(new NioDriverHandler());
            ConnectFuture future = connector.connect(new InetSocketAddress(server.getIp(), server.getPort()));
            future.awaitUninterruptibly();
            session = future.getSession();

            NioSocketData data = new NioSocketData();
            data.setMessage(request);
            session.write(data);
            session.getCloseFuture().awaitUninterruptibly();

            response = (String) session.getAttribute("result");
            log.info("Response: {}.", response);
        } catch (Exception ex) {
            log.error("MINA: " + ex.getMessage(), ex);
        } finally {
            if (session != null) session.closeNow();
            if (connector != null) connector.dispose(true);
        }

        return StringUtils.defaultIfEmpty(response, StringUtils.EMPTY);
    }

    /**
     * 转响应
     *
     * @param key      键
     * @param response 响应
     * @return 账户
     */
    public String toResponse(String key, String response) {
        String[] headerBody = response.split(HEADER_BODY_SPLIT);
        Header header = new Header();
        header.strToObj(headerBody[0]);

        if (StringUtils.isNotEmpty(key)) {
            headerBody[1] = decode(key, headerBody[1]);
            log.info("Plaintext: {}.", headerBody[1]);
        }

        // appIp, descIp, retCode, tempKey, name, accountID, empNo, phone, mobile, email, idCardNum, serverId, jkReqID
        String[] splits = headerBody[1].split(",;");
        return splits[5];
    }

    /**
     * <p>
     * 服务器
     * </p>
     *
     * <AUTHOR>
     * @since 2023-05-16
     */
    @AllArgsConstructor
    @Data
    public static class Server {

        /**
         * ID
         */
        private String id;

        /**
         * IP
         */
        private String ip;

        /**
         * 端口
         */
        private Integer port;

    }

}
