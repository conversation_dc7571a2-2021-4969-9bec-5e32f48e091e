package cn.cmcc.kernel.controller;

import cn.cmcc.common.annotation.CheckPerms;
import cn.cmcc.common.enums.AuthorizeType;
import cn.cmcc.common.web.controller.BaseController;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.cmcc.kernel.domain.TQueryCodePermission;
import cn.cmcc.kernel.domain.TRoleQueryCodePermission;
import cn.cmcc.kernel.dto.BatchPermissionRequest;
import cn.cmcc.kernel.dto.PermissionGrantRequest;
import cn.cmcc.kernel.dto.PermissionQueryResponse;
import cn.cmcc.kernel.service.IQueryCodePermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 查询编码权限管理控制器
 *
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@RestController
@RequestMapping("/kernel/queryCodePermission")
public class QueryCodePermissionController extends BaseController {

    @Autowired
    private IQueryCodePermissionService queryCodePermissionService;

    /**
     * 清除用户权限缓存
     */
    @PostMapping("/clearUserCache/{username}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult clearUserCache(@PathVariable String username) {
        queryCodePermissionService.clearUserPermissionCache(username);
        return AjaxResult.success("用户权限缓存清除成功");
    }

    /**
     * 清除查询编码权限缓存
     */
    @PostMapping("/clearCodeCache/{queryCode}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult clearCodeCache(@PathVariable String queryCode) {
        queryCodePermissionService.clearQueryCodePermissionCache(queryCode);
        return AjaxResult.success("查询编码权限缓存清除成功");
    }

    /**
     * 检查用户权限
     */
    @GetMapping("/check/{username}/{queryCode}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult checkPermission(@PathVariable String username, @PathVariable String queryCode) {
        boolean hasPermission = queryCodePermissionService.hasPermission(username, queryCode, userId);
        return AjaxResult.success(hasPermission);
    }

    // ==================== 用户权限管理接口 ====================

    /**
     * 授予用户权限
     */
    @PostMapping("/user/grant")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult grantUserPermission(@Validated @RequestBody PermissionGrantRequest request) {
        if (!request.isUserPermission()) {
            return AjaxResult.error("用户名不能为空");
        }

        queryCodePermissionService.grantUserPermission(
                request.getUsername(),
                request.getQueryCode(),
                request.getPermissionType()
        );

        return AjaxResult.success("用户权限授予成功");
    }

    /**
     * 撤销用户权限
     */
    @DeleteMapping("/user/revoke/{username}/{queryCode}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult revokeUserPermission(@PathVariable String username, @PathVariable String queryCode) {
        queryCodePermissionService.revokeUserPermission(username, queryCode);
        return AjaxResult.success("用户权限撤销成功");
    }

    /**
     * 获取用户权限列表
     */
    @GetMapping("/user/{username}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult getUserPermissions(@PathVariable String username) {
        List<TQueryCodePermission> permissions = queryCodePermissionService.getUserPermissions(username);
        return AjaxResult.success(permissions);
    }

    /**
     * 批量授予用户权限
     */
    @PostMapping("/user/batchGrant")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult batchGrantUserPermissions(@Validated @RequestBody BatchPermissionRequest request) {
        if (!request.isUserPermission()) {
            return AjaxResult.error("用户名不能为空");
        }

        queryCodePermissionService.batchGrantUserPermissions(
                request.getUsername(),
                request.getQueryCodes(),
                request.getPermissionType()
        );

        return AjaxResult.success("用户权限批量授予成功");
    }

    /**
     * 批量撤销用户权限
     */
    @PostMapping("/user/batchRevoke")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult batchRevokeUserPermissions(@Validated @RequestBody BatchPermissionRequest request) {
        if (!request.isUserPermission()) {
            return AjaxResult.error("用户名不能为空");
        }

        queryCodePermissionService.batchRevokeUserPermissions(
                request.getUsername(),
                request.getQueryCodes()
        );

        return AjaxResult.success("用户权限批量撤销成功");
    }

    // ==================== 角色权限管理接口 ====================

    /**
     * 授予角色权限
     */
    @PostMapping("/role/grant")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult grantRolePermission(@Validated @RequestBody PermissionGrantRequest request) {
        if (!request.isRolePermission()) {
            return AjaxResult.error("角色标识不能为空");
        }

        queryCodePermissionService.grantRolePermission(
                request.getRoleKey(),
                request.getQueryCode(),
                request.getPermissionType()
        );

        return AjaxResult.success("角色权限授予成功");
    }

    /**
     * 撤销角色权限
     */
    @DeleteMapping("/role/revoke/{roleKey}/{queryCode}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult revokeRolePermission(@PathVariable String roleKey, @PathVariable String queryCode) {
        queryCodePermissionService.revokeRolePermission(roleKey, queryCode);
        return AjaxResult.success("角色权限撤销成功");
    }

    /**
     * 获取角色权限列表
     */
    @GetMapping("/role/{roleKey}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult getRolePermissions(@PathVariable String roleKey) {
        List<TRoleQueryCodePermission> permissions = queryCodePermissionService.getRolePermissions(roleKey);
        return AjaxResult.success(permissions);
    }

    /**
     * 批量授予角色权限
     */
    @PostMapping("/role/batchGrant")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult batchGrantRolePermissions(@Validated @RequestBody BatchPermissionRequest request) {
        if (!request.isRolePermission()) {
            return AjaxResult.error("角色标识不能为空");
        }

        queryCodePermissionService.batchGrantRolePermissions(
                request.getRoleKey(),
                request.getQueryCodes(),
                request.getPermissionType()
        );

        return AjaxResult.success("角色权限批量授予成功");
    }

    /**
     * 批量撤销角色权限
     */
    @PostMapping("/role/batchRevoke")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult batchRevokeRolePermissions(@Validated @RequestBody BatchPermissionRequest request) {
        if (!request.isRolePermission()) {
            return AjaxResult.error("角色标识不能为空");
        }

        queryCodePermissionService.batchRevokeRolePermissions(
                request.getRoleKey(),
                request.getQueryCodes()
        );

        return AjaxResult.success("角色权限批量撤销成功");
    }

    // ==================== 查询接口 ====================

    /**
     * 获取用户可访问的查询编码
     */
    @GetMapping("/accessible/{username}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult getUserAccessibleCodes(@PathVariable String username) {
        List<String> codes = queryCodePermissionService.getUserAccessibleCodes(username);
        PermissionQueryResponse.UserPermissionResponse response =
                new PermissionQueryResponse.UserPermissionResponse(username, codes, "read");
        return AjaxResult.success(response);
    }

    /**
     * 获取有权限访问指定编码的用户列表
     */
    @GetMapping("/users/{queryCode}")
    @CheckPerms(value = "ADMIN", authorizeType = AuthorizeType.ROLE)
    public AjaxResult getUsersWithCodeAccess(@PathVariable String queryCode) {
        List<String> users = queryCodePermissionService.getUsersWithCodeAccess(queryCode);
        List<String> roles = queryCodePermissionService.getRolesWithCodeAccess(queryCode);

        PermissionQueryResponse.CodePermissionStatsResponse response =
                new PermissionQueryResponse.CodePermissionStatsResponse(queryCode, users, roles);
        return AjaxResult.success(response);
    }
}
