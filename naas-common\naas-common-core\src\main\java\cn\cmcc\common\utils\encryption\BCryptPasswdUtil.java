package cn.cmcc.common.utils.encryption;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.utils.StringUtils;
import cn.hutool.crypto.SecureUtil;

public class BCryptPasswdUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(BCryptPasswdUtil.class);
	/**
     * 默认密码盐长度
     */
    public static final int SALT_LENGTH = 6;

	/**
     * 获取密码盐
     *
     * <AUTHOR>
     * @Date 2019/7/20 17:35
     */
    public static String getRandomSalt() {
    	 String base = "abcdefghijklmnopqrstuvwxyz0123456789";
         StringBuilder sb = new StringBuilder();
         for (int i = 0; i < SALT_LENGTH; i++) {
             int number = Constants.rand.nextInt(base.length());
             sb.append(base.charAt(number));
         }
         return sb.toString();
    }

    /**
     * md5加密，带盐值
     *
     * <AUTHOR>
     * @Date 2019/7/20 17:36
     */
    public static String md5Encrypt(String password, String salt) {
        if (StringUtils.isEmpty(password) || StringUtils.isEmpty(salt)) {
            throw new IllegalArgumentException("密码或盐为空！");
        } else {
            return SecureUtil.md5(password + salt);
        }
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 加密
     * @param password
     * @return
     */
    public static String encryptPassword(String password,String salt)
    {
        return BCryptPasswdUtil.md5Encrypt(password, salt);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @param  salt
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword,String salt)
    {
        return encryptPassword(rawPassword,salt).equals(encodedPassword);
    }

    public static void  main(String args[]) {
        LOGGER.info(md5Encrypt("1qaz!QAZ", "vm43e"));
    }
}
