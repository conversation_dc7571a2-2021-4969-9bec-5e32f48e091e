/**
 * @Title: CmccPasswordCallback.java
 * @Package cn.cmcc.core.datasource
 * 
 * <AUTHOR>
 * @date 2021年11月19日
 * @version V1.0
 */
package cn.cmcc.common.datasource.config;

import java.util.Properties;

import com.alibaba.druid.util.DruidPasswordCallback;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.encryption.AESUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class CmccPasswordCallback extends DruidPasswordCallback {

	/**
	 * @Fields serialVersionUID : 
	 */
	private static final long serialVersionUID = 1L;
	
	private String password;
	
	public CmccPasswordCallback(String password) {
		this.password = password;
	}

	@Override  
    public void setProperties(Properties properties) {  
        if (StringUtils.isNoneBlank(this.password)) {  
            try {  
                String pwd = AESUtil.decrypt(this.password,Constants.AES_KEY);  
                setPassword(pwd.toCharArray());  
            } catch (Exception e) {  
            	log.error("AESUtil.decrypt error:",e);
            }  
        }  
    }   
}
