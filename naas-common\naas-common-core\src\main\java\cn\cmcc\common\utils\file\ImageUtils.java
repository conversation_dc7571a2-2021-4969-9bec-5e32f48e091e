package cn.cmcc.common.utils.file;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;

import cn.cmcc.common.config.NaasConfig;
import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.utils.StringUtils;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ImageUtils {

    public static byte[] getImage(String imagePath) {
        InputStream is = getFile(imagePath);
        try {
            return IOUtils.toByteArray(is);
        } catch (Exception e) {
            log.error("图片加载异常 {}", e.getMessage());
            return null;
        } finally {
            IOUtils.closeQuietly(is);
        }
    }

    public static InputStream getFile(String imagePath) {
        try {
            byte[] result = readFile(imagePath);
            if (result != null) {
                result = Arrays.copyOf(result, result.length);
                return new ByteArrayInputStream(result);
            }
        } catch (Exception e) {
            log.error("获取图片异常 {}", e.getMessage());
        }
        return null;
    }

    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @return 字节数据
     */
    public static byte[] readFile(String url) {
        InputStream in = null;
        ByteArrayOutputStream baos = null;
        try {
            if (url.startsWith("http")) {
                // 网络地址
                in = new ByteArrayInputStream(HttpUtil.downloadBytes(url));
            } else {
                // 本机地址
                String localPath = NaasConfig.getProfile();
                String downloadPath =  localPath + StringUtils.substringAfter(url, Constants.RESOURCE_PREFIX);
                in = new FileInputStream(FileUtils.getFile(downloadPath));
            }
            return IOUtils.toByteArray(in);
        } catch (Exception e) {
            log.error("获取文件路径异常 {}", e.getMessage());
            return null;
        } finally {
            IOUtils.closeQuietly(baos);
        }
    }

    public static Map<String, Object> getImageBase64AndHeight(String path, String keyImage, String heightKey,
                                                              Double rate) {
        String imageStr = getImageBase64(path);
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(imageStr)) {
            try {
                File picFile = FileUtils.getFile(path);
                BufferedImage image = ImageIO.read(picFile);
                if (image != null) {
                    map.put(keyImage, imageStr);
                    map.put(heightKey, image.getHeight() * rate / image.getWidth());
                }
            } catch (IOException ex) {
                log.error("getImageBase64AndHeight:->" + ex.getMessage());
                map.put(keyImage, "");
                map.put(heightKey, rate);
            }
        } else {
            map.put(keyImage, "");
            map.put(heightKey, rate);
        }
        return map;
    }

    /**
     * 该方法用来将指定的文件转换成base64编码
     *
     * @param path:图片路径
     **/
    public static String getImageBase64(String path) {
        log.info("getImageBase64:->" + path);
        //1、校验是否为空
        if (path == null || path.trim().length() <= 0) {
            return "";
        }

    	//2、校验文件是否为目录或者是否存在
    	File picFile = FileUtils.getFile(path);
    	if(picFile.isDirectory() || (!picFile.exists())) return "";

    	//3、校验是否为图片
        try {
            BufferedImage image = ImageIO.read(picFile);
            if (image == null) {
    	    	return "";
            }
        } catch (IOException ex) {
            log.error("getImageBase64:->" + ex.getMessage());
    		return "";
    	}

        //4、转换成base64编码
    	String imageStr = "";
    	try(InputStream in = FileUtils.openInputStream(FileUtils.getFile(path));){
    		byte[] data = null;
    		data = new byte[in.available()];
    		int count = 0;
    	    while((count = in.read(data)) > 0) {
    	    	Base64 encoder = new Base64();
        		imageStr = encoder.encodeToString(data);
    	    }
    	} catch (Exception e) {
    		log.error(e.getMessage(),e);
    	}

        return imageStr;
    }
}
