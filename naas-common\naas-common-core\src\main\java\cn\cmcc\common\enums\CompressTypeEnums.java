package cn.cmcc.common.enums;

import java.util.Collections;
import java.util.List;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.file.CompressUtil;
import cn.cmcc.common.utils.file.FileUtils;

/**
 * 压缩方式
 * 
 * @version v1.0
 * <AUTHOR>
 * @date 2023年7月6日下午5:31:59
 */
public enum CompressTypeEnums {
	
	WU("wu","不压缩") {
		@Override
		public String compress(String filePathName) {
			return filePathName;
		}

		@Override
		public List<String> decompress(String filePathName) {
			return Collections.singletonList(filePathName);
		}
	},
	ZIP("zip","zip") {
		@Override
		public String compress(String filePathName) throws Exception {
			String newFilePathName  = FileUtils.getFilePathNameNewPrefix(filePathName, this.getSuffix());
			CompressUtil.compress(Collections.singletonList(filePathName), newFilePathName, false);
			return newFilePathName;
		}
		
		@Override
		public List<String> decompress(String filePathName)  throws Exception{
			return CompressUtil.unZipFiles(filePathName);
		}
	},
	GZIP("gzip","gz") {
		@Override
		public String compress(String filePathName) throws Exception {
			String newFilePathName  = FileUtils.getFilePathNameNewPrefix(filePathName, this.getSuffix());
			CompressUtil.compressGzip(filePathName,newFilePathName);
			return newFilePathName;
		}
		
		@Override
		public List<String> decompress(String filePathName)  throws Exception{
			return Collections.singletonList(CompressUtil.unCompressGzip(filePathName));

		}
	},
	TAR_GZ("tar.gz","tar.gz") {
		@Override
		public String compress(String filePathName) throws Exception{
			return CompressUtil.compressTarGz(filePathName);
		}
		
		@Override
		public List<String> decompress(String filePathName) throws Exception {
			return CompressUtil.unCompressTarGz(filePathName);
		}
	};

	private final String code;
	private final String suffix;
	public abstract String compress(String filePathName)  throws Exception;
	public abstract List<String> decompress(String filePathName) throws Exception;

	CompressTypeEnums(String code, String suffix) {
		this.code = code;
		this.suffix = suffix;
	}

	public String getCode() {
		return code;
	}

	public String getSuffix() {
		return suffix;
	}
	
	public static CompressTypeEnums getByValue(String value) {
		if(StringUtils.isEmpty(value)) return WU;
        for (CompressTypeEnums e : values()) {
            if (e.getCode().equals(value)) {
                return e;
            }
        }
        return WU;
    }
}
