package cn.cmcc.flow.service;

import cn.cmcc.flow.pojo.bo.UserTaskArgs;
import java.util.List;
import java.util.Map;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;

/**
 * 任务接口
 * @Class Name ITaskService
 * <AUTHOR>
 * @Create In  2021年8月6日
 *
 * <AUTHOR> 2021年8月6日
 */
public interface ITaskService {

	/**
	 * <p>
	 * 提交任务
	 * </p>
	 *
	 * @param taskId
	 * @param variables
	 */
	void completeTask(String taskId, Map<String, Object> variables);

	/**
	 * 根据未完成任务ID获取候选人-候选组
	 *
	 * @param taskId
	 *            任务ID
	 * @return
	 */
	List<IdentityLink> getIndetityLink(String taskId);

	/**
	 * 任务改派
	 *
	 * @param taskId
	 *            任务ID
	 * @param userId
	 *            改派人
	 */
	void reassignTask(String taskId, String target);

	/**
	 * 任务指派
	 *
	 * @param taskId
	 *            任务ID
	 * @param userId
	 *            改派人
	 */
	void assignTask(String taskId, String target);

	/**
	 * 代理任务
	 *
	 * @param taskId
	 *            任务id
	 * @param userId
	 *            代理人
	 */
	void delegateTask(String taskId, String userId);

	/**
	 * 根据业务id获得当前任务
	 *
	 * @param bussinessKey
	 * @return
	 */
	List<Task> getTasks(String bussinessKey);

	/**
	 * 删除当前任务分派角色或人员
	 *
	 * @param taskId
	 *            任务ID
	 * @param roleId
	 *            角色或组ID
	 * @param userId
	 *            用户ID
	 */
	void deleteCandidateGroupOrUser(String taskId, String groupId, String userId);

	/**
	 * 添加当前任务分派角色或人员
	 *
	 * @param taskId
	 *            任务ID
	 * @param roleId
	 *            角色或组ID
	 * @param userId
	 *            用户ID
	 */
	void addCandidateGroupOrUser(String taskId, String groupId, String userId);

	/**
	 * 领取任务
	 *
	 * @param taskId
	 * @param userId
	 */
	void claim(String taskId, String userId);

	/**
	 * 取消领取任务
	 *
	 * @param taskId
	 * @param userId
	 */
	void unclaim(String taskId, String userId);

	/**
	 * 根据任务ID获得当前任务
	 *
	 * @param taskId
	 * @return
	 */
	Task getTask(String taskId);

	/**
	 * <p>
	 * 添加备注
	 * </p>
	 *
	 * @param taskId
	 * @param processInstanceId
	 * @param type
	 * @param message
	 */
	void addTaskComment(String taskId, String processInstanceId, String type, String message);

	/**
	 * 通过多参数查找任务
	 * @param pars
	 * @return
	 */
	List<Task> getTasksByPars(String ProcessKey, String businessKey, String groupID, String userID);

	/**
	 * 通过实例id查找任务
	 * @param procInstId
	 * @return
	 */
	List<Task> findTasksByInst(String procInstId);

	/**
	 * 设置流程变量
	 * @param taskId
	 * @param variableName
	 * @param value
	 */
	void setVariableLocal(String taskId, String variableName, Object value);

	/**
	 * 设置流程变量
	 * @param taskId
	 * @param variableName
	 * @param value
	 */
	void setVariable(String taskId, String variableName, Object value);

	/**
	 * 提交完成任务
	 * @param taskId
	 * @param variables
	 * @param localScope
	 */
	void complete(String taskId, Map<String, Object> variables, boolean localScope);

	/**
	 * 提交完成任务
	 * @param taskId
	 * @param variables
	 */
	void complete(String taskId, Map<String, Object> variables);


	/**
	 * 工单号必填
	 * 阶段必填
	 * @Title: completeUserTask
	 * @Description: 完成任务
	 * @param:  {@link cn.cmcc.flow.pojo.bo.UserTaskArgs}
	 * @return: void 无返回值
	 * @throws
	 */
	void completeUserTask(UserTaskArgs userTaskArgs);

	/**
	 * 通过实例id、taskDefKey查找任务
	 * @param procInstId
	 * @param taskDefKey
	 * @return
	 */
	List<Task> findTasksByInstAndTaskDefKey(String procInstId, String taskDefKey);

	}
