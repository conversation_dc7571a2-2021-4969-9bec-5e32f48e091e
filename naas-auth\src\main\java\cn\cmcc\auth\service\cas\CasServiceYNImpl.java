package cn.cmcc.auth.service.cas;

import static cn.cmcc.auth.config.CasConfig.CACHE_TYPE;
import static cn.cmcc.auth.config.CasConfig.REDIRECT;
import static cn.cmcc.auth.config.CasConfig.ROOT_SUFFIX;
import static cn.cmcc.auth.config.CasConfig.TICKET_PREFIX;
import static cn.cmcc.auth.config.CasConfig.USER_NAME;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.utils.http.HttpClientUtils;
import cn.cmcc.common.utils.http.bo.HttpReqParam;
import cn.cmcc.common.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 云南实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Slf4j
public class CasServiceYNImpl implements ICasService {

	private final CasConfig config;

	public CasServiceYNImpl(CasConfig config) {
		this.config = config;
		try {
			String service = URLEncoder.encode(
					String.format("%s%s", config.getServerUrl(), config.getServerLoginSuffix()),
					StandardCharsets.UTF_8.name());
			config.setCasLoginUrl(String.format("%s%s?%s=%s", config.getCasServerUrl(),
					config.getCasServerLoginSuffix(), config.getCasServerServiceParameter(), service));
			log.info("casLoginUrl: {}", config.getCasLoginUrl());
			config.setCasValidateUrl(
					String.format("%s%s", config.getCasServerUrl(), config.getCasServerValidateSuffix()));
			log.info("casValidateUrl: {}", config.getCasValidateUrl());
			config.setExpire(CacheConstants.EXPIRE_AFTER_WRITE);
			log.info("expire: {}", config.getExpire());
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
	}

	@Override
	public AjaxResult login(HttpServletRequest request) {
		Map<String, Object> data = new HashMap<>(16);
		try {
			// 已经有登录信息
			if (validToken(request)) {
				data.put(REDIRECT, ROOT_SUFFIX);
				return AjaxResult.success(data);
			}
			String ticket = getTicket(request, config.getCasTicketParameter());
			if (StringUtils.isEmpty(ticket)) {
				log.info("Ticket is empty, jumps to the CAS login page.");
				data.put(REDIRECT, config.getCasLoginUrl());
			} else {

				AjaxResult result = HttpClientUtils
						.send(HttpReqParam.builder(config.getCasValidateUrl(), null, HttpMethod.POST)
								.addHeaderParam("ibuildtoken", ticket).build());
				if (result.isSuccess()) {
					String response = result.getData().toString();
					log.info("Response:\n{}", response);

					JSONObject responseJson = JSONObject.parseObject(response);
					JSONObject userData = responseJson.getJSONObject("data");
					if ("1".equals(responseJson.getString("code"))) {
						String userName = userData.getString("account");
						String token = (String) config.getLoginService().ssoLogin(userName)
								.get(SecurityConstants.TOKEN);
						data.put(REDIRECT, ROOT_SUFFIX);
						data.put(SecurityConstants.TOKEN, token);
						data.put(USER_NAME, userName);

						// 票据-TOKEN
						config.getCacheService().set(TICKET_PREFIX + ticket, token, config.getExpire(),
								TimeUnit.SECONDS, CACHE_TYPE);
					} else {
						data.put(REDIRECT, config.getCasLoginUrl());
						data.put("msg", responseJson.getString("msg"));
					}
				} else {
					data.put(REDIRECT, config.getCasLoginUrl());
					data.put("msg", result.getMsg());
				}
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);

			data.put(REDIRECT, config.getCasLoginUrl());
			data.put("msg", ex.getMessage());
		}
		return AjaxResult.success(data);
	}

}
