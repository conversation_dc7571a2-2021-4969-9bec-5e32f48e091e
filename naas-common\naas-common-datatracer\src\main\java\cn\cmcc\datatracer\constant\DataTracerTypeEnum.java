package cn.cmcc.datatracer.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据业务类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DataTracerTypeEnum implements BaseEnum {

    /**
     * 通用查询
     */
    COMMON_QRY("common_query", "通用查询"),

    COMMON_QRY_FIELD("common_query_field", "通用查询-字段"),

    SYS_CONFIG("sys_config", "系统配置");
    private final String value;

    private final String desc;
}
