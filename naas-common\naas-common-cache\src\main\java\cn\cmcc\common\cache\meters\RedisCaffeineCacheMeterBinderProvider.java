/**
 * @Title: RedisCaffeineCacheMeterBinderProvider.java
 * @Package cn.cmcc.common.core.cache.meters
 * 
 * <AUTHOR>
 * @date 2021年9月3日
 * @version V1.0
 */
package cn.cmcc.common.cache.meters;

import org.springframework.boot.actuate.metrics.cache.CacheMeterBinderProvider;

import cn.cmcc.common.cache.support.RedisCaffeineCache;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.MeterBinder;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class RedisCaffeineCacheMeterBinderProvider implements CacheMeterBinderProvider<RedisCaffeineCache> {

    @Override
    public MeterBinder getMeterBinder(RedisCaffeineCache cache, Iterable<Tag> tags) {
        return new CaffeineCacheMetrics(cache.getCaffeineCache(), cache.getName(), tags);
    }
}
