package cn.cmcc.cad.entity;

import java.io.Serializable;

import cn.cmcc.cad.service.ICadService;
import lombok.Data;

@Data
public class XhxkCadText extends XhxkCadBaseEntity implements Serializable {
	private static final long serialVersionUID = 2753954597126012102L;
	private String key;
	private String value;
	private double textWidth;
	
	public XhxkCadText(String id, String type, String block,String layer, String key, String value) {
		super(id, type, block,layer);
		this.key = key;
		this.value = value;
	}

	public XhxkCadText(String id, String type, String block,String layer, String key, String value,double textWidth) {
		super(id, type, block,layer);
		this.key = key;
		this.value = value;
		this.textWidth=textWidth;
	}

	@Override
	public void transfer(ICadService cadService,Double scale, XhxkCadPoint minPoint) {
		// TODO Auto-generated method stub
		super.transfer(cadService,scale, minPoint);
	}

}
