package cn.cmcc.auth.service.cas;

import static cn.cmcc.auth.config.CasConfig.LOGOUT_PARAMETER_NAME;
import static cn.cmcc.auth.config.CasConfig.LOGOUT_PREFIX;
import static cn.cmcc.auth.config.CasConfig.ROOT_SUFFIX;
import static cn.cmcc.auth.config.CasConfig.TICKET_PREFIX;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.apache.commons.lang3.StringUtils;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.common.cache.config.CacheConfig;
import cn.cmcc.common.cache.constant.CacheConstants;
import cn.cmcc.common.cache.enums.CachesEnums;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.utils.text.CharsetKit;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 默认实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Slf4j
public class CasServiceImpl extends CacheConfig implements ICasService {

    private final CasConfig config;

    public CasServiceImpl(CasConfig config) {
        this.config = config;
        try {
            String service = URLEncoder.encode(String.format("%s%s", config.getServerUrl(), config.getServerLoginSuffix()), CharsetKit.UTF_8);
            config.setCasLoginUrl(String.format("%s%s?%s=%s",
                    config.getCasServerUrl(),
                    config.getCasServerLoginSuffix(),
                    config.getCasServerServiceParameter(),
                    service));
            log.info("casLoginUrl: {}", config.getCasLoginUrl());
            config.setCasValidateUrl(String.format("%s%s?%s=%s&%s=",
                    config.getCasServerUrl(),
                    config.getCasServerValidateSuffix(),
                    config.getCasServerServiceParameter(),
                    service,
                    config.getCasTicketParameter()));
            log.info("casValidateUrl: {}", config.getCasValidateUrl());
            config.setExpire(CacheConstants.EXPIRE_AFTER_WRITE);
            log.info("expire: {}", config.getExpire());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public AjaxResult login(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>(16);
        try {
            // 已经有登录信息
            if (validToken(request)) {
                data.put("redirect", ROOT_SUFFIX);
                return AjaxResult.success(data);
            }

            String ticket = getParameter(request, config.getCasTicketParameter());
            if (StringUtils.isEmpty(ticket)) {
                log.info("Ticket is empty, jumps to the CAS login page.");
                data.put("redirect", getURL(request, ticket));
            } else {
                String validResponse = getResponse(getURL(request, ticket));
                log.info("Response:\n{}", validResponse);

                Map<String, Object> attributes = parseXml(validResponse);
                // userName, loginId, roleId, sex, mobile, telephone, state, userName, userId, email, orgId
                String userName = String.valueOf(attributes.get("userName"));
                String token = config.getLoginService().ssoLogin(userName).get(SecurityConstants.TOKEN).toString();
                data.put("redirect", ROOT_SUFFIX);
                data.put(SecurityConstants.TOKEN, token);
                data.put("userName", userName);

                // 票据-TOKEN
                config.getCacheService().set(TICKET_PREFIX + ticket, token, config.getExpire(), TimeUnit.SECONDS, CachesEnums.CACHE_LOGIN.name());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);

            data.put("redirect", config.getCasLoginUrl());
            data.put("msg", ex.getMessage());
        }
        return AjaxResult.success(data);
    }

    @Override
    public void logout(HttpServletRequest request) {
        try {
            String logoutRequest = request.getParameter(LOGOUT_PARAMETER_NAME),
                    ticket = null, ticketKey;
            Object token;
            if (StringUtils.isEmpty(logoutRequest) ||
                    StringUtils.isEmpty(ticket = getTextForElement(logoutRequest, "SessionIndex")) ||
                    ((token = config.getCacheService().get(ticketKey = TICKET_PREFIX + ticket, CachesEnums.CACHE_LOGIN.name())) == null)) {
                log.error("Logout request: {}, ticket: {}, token: {}.", StringUtils.replace(logoutRequest, "[\r\n]", ""),
                        StringUtils.replace(ticket, "[\r\n]", ""), null);
                return;
            }

            // 缓存时间和 TOKEN 过期时间一样，如果缓存中存在，就说明没有过期
            config.getCacheService().del(ticketKey, CachesEnums.CACHE_LOGIN.name());
            config.getCacheService().set(LOGOUT_PREFIX + token, ticketKey, config.getExpire(), TimeUnit.SECONDS, CachesEnums.CACHE_LOGIN.name());

            config.getLoginService().logout((String) token);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public boolean tokenExpired(String token) {
        return config.getCacheService().get(LOGOUT_PREFIX + token, CachesEnums.CACHE_LOGIN.name()) != null;
    }

    /**
     * 获取请求
     *
     * @param url URL
     * @return 响应字符串
     */
    public String getResponse(String url) throws IOException {
        log.info("URL: {}.", StringUtils.replace(url, "[\r\n]", ""));
        return HttpRequest.get(url).execute().body();
    }

    /**
     * 解析 XML
     *
     * @param xml XML
     * @return 属性结果
     */
    public Map<String, Object> parseXml(String xml) {
        try {
            String authenticationFailure = getTextForElement(xml, "authenticationFailure");
            if (StringUtils.isNotEmpty(authenticationFailure)) {
                throw new RuntimeException(authenticationFailure);
            } else {
                String user = getTextForElement(xml, "user");
                Map<String, Object> attributes = new HashMap<>();
                attributes.put("userName", user);
                for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                    log.info("{}: {}.", entry.getKey(), entry.getValue());
                }
                return attributes;
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);

            throw new RuntimeException(ex.getMessage());
        }
    }

    /**
     * 获取列表
     *
     * @param xml     XML
     * @param element 元素
     * @return 列表
     * @throws ParserConfigurationException
     */
    public static List<String> getListForElements(String xml, String element) throws IOException, SAXException, ParserConfigurationException {
        List<String> elements = new ArrayList<>(8);

        SAXParserFactory parserFactory = SAXParserFactory.newInstance();
        // to be compliant, completely disable DOCTYPE declaration:
        parserFactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        SAXParser saxParser = parserFactory.newSAXParser();
        DefaultHandler handler = new DefaultHandler() {

            private boolean found = false;
            private StringBuilder sb = new StringBuilder();

            public void startElement(String uri, String localName, String qName, Attributes attributes) {
                if (localName.equals(element)) {
                    this.found = true;
                }
            }

            public void endElement(String uri, String localName, String qName) {
                if (localName.equals(element)) {
                    this.found = false;
                    elements.add(this.sb.toString());
                    this.sb = new StringBuilder();
                }
            }

            public void characters(char[] ch, int start, int length) {
                if (this.found) {
                    this.sb.append(ch, start, length);
                }
            }

        };
        saxParser.parse(new InputSource(new StringReader(xml)), handler);

        return elements;
    }

    /**
     * 获取文本
     *
     * @param xml     XML
     * @param element 元素
     * @return 文本
     * @throws ParserConfigurationException
     */
    public String getTextForElement(String xml, String element) throws IOException, SAXException, ParserConfigurationException {
        SAXParserFactory parserFactory = SAXParserFactory.newInstance();
        // to be compliant, completely disable DOCTYPE declaration:
        parserFactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        SAXParser saxParser = parserFactory.newSAXParser();
        StringBuilder sb = new StringBuilder();

        DefaultHandler handler = new DefaultHandler() {

            private boolean found = false;

            public void startElement(String uri, String localName, String qName, Attributes attributes) {
                if (localName.equals(element) || qName.endsWith(element)) {
                    this.found = true;
                }

            }

            public void endElement(String uri, String localName, String qName) {
                if (localName.equals(element) || qName.endsWith(element)) {
                    this.found = false;
                }

            }

            public void characters(char[] ch, int start, int length) {
                if (this.found) {
                    sb.append(ch, start, length);
                }
            }

        };
        saxParser.parse(new InputSource(new StringReader(xml)), handler);
        return sb.toString();
    }

    /**
     * 读取属性
     *
     * @param xml XML
     * @return 属性
     * @throws ParserConfigurationException
     */
    @SuppressWarnings("unused")
    protected Map<String, Object> readAttributes(String xml) throws IOException, SAXException, ParserConfigurationException {
        int index1 = xml.indexOf("<cas:attributes>");
        int index2 = xml.indexOf("</cas:attributes>");
        if (index1 == -1) return Collections.emptyMap();
        else {
            String attributesText = xml.substring(index1 + 16, index2);
            Map<String, Object> attributes = new HashMap<>();
            try (BufferedReader br = new BufferedReader(new StringReader(attributesText))) {
                List<String> attributeNames = new ArrayList<>();
                String line;
                while ((line = br.readLine()) != null) {
                    String trimmedLine = line.trim();
                    if (trimmedLine.length() > 0) {
                        int leftPos = trimmedLine.indexOf(":");
                        int rightPos = trimmedLine.indexOf(">");
                        attributeNames.add(trimmedLine.substring(leftPos + 1, rightPos));
                    }
                }

                for (String attributeName : attributeNames) {
                    List<String> values = getListForElements(xml, attributeName);
                    if (values.size() == 1) {
                        attributes.put(attributeName, values.get(0));
                    } else {
                        attributes.put(attributeName, values);
                    }
                }
            }

            return attributes;
        }
    }

    /**
     * 获取 URL
     *
     * @param request 请求
     * @param ticket  票据
     * @return URL
     */
    public String getURL(HttpServletRequest request, String ticket) throws UnsupportedEncodingException {
        String chapters = getParameter(request, "chapters");
        if (StringUtils.isEmpty(chapters)) {
            return StringUtils.isEmpty(ticket) ?
                    config.getCasLoginUrl() :
                    config.getCasValidateUrl() + ticket;
        } else {
            String service = chapters.startsWith("/") ?
                    URLEncoder.encode(String.format("%s%s%s", config.getServerUrl(), config.getServerLoginSuffix(), chapters), CharsetKit.UTF_8) :
                    URLEncoder.encode(String.format("%s%s/%s", config.getServerUrl(), config.getServerLoginSuffix(), chapters), CharsetKit.UTF_8);
            return StringUtils.isEmpty(ticket) ?
                    String.format("%s%s?%s=%s",
                            config.getCasServerUrl(),
                            config.getCasServerLoginSuffix(),
                            config.getCasServerServiceParameter(),
                            service) :
                    String.format("%s%s?%s=%s&%s=%s",
                            config.getCasServerUrl(),
                            config.getCasServerValidateSuffix(),
                            config.getCasServerServiceParameter(),
                            service,
                            config.getCasTicketParameter(),
                            ticket);
        }
    }

}
