package cn.cmcc.common.utils.ftp;

import cn.cmcc.common.pojo.template.FtpParam;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.file.CompressUtil;
import cn.cmcc.common.utils.file.FileUtils;
import cn.cmcc.common.utils.text.CharsetKit;
import org.apache.commons.net.ftp.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class FtpUtil implements Ftp{
	private static final Logger logger = LoggerFactory.getLogger(FtpUtil.class);
	private FTPClient ftpClient;
	private String address;
	private int port;
	private String username;
	private String password;
	private String basepath;
	private String clientConfig;
	private Boolean isLogin = false;

	/** 本地字符编码 */
	private String LOCAL_CHARSET = "GBK";

	public FtpUtil(String address, int port, String username, String password, String basepath, String clientConfig) {
		this.address = address;
		this.port = port;
		this.username = username;
		this.password = password;
		this.basepath = basepath;
		this.clientConfig = clientConfig;
		this.ftpClient = new FTPClient();
	}

	public FtpUtil(String address, int port, String username, String password) {
		this.address = address;
		this.port = port;
		this.username = username;
		this.password = password;
		this.ftpClient = new FTPClient();
	}

	public FTPClient getFtpClient() {
		return this.ftpClient;
	}

	public void setFtpClient(FTPClient ftpClient) {
		this.ftpClient = ftpClient;
	}

	public FtpUtil(FtpParam ftpParam, String clientConfig) {
		this.address = ftpParam.getIp();
		this.port =  ftpParam.getPort();
		this.username = ftpParam.getUserName();
		this.password = ftpParam.getPassword();
		this.basepath = ftpParam.getPath();
		this.clientConfig = clientConfig;
		this.ftpClient = new FTPClient();
	}

	@Override
	public boolean ftpLogin() {
		long start = System.currentTimeMillis();
		boolean isLogin = false;
		FTPClientConfig ftpClientConfig = new FTPClientConfig();
		ftpClientConfig.setServerTimeZoneId(TimeZone.getDefault().getID());
		//this.ftpClient.setAutodetectUTF8(true);
		this.ftpClient.configure(ftpClientConfig);

		try {
			if (this.port > 0) {
				this.ftpClient.connect(this.address, this.port);
			} else {
				this.ftpClient.connect(this.address);
			}

			int reply = this.ftpClient.getReplyCode();
			if (!FTPReply.isPositiveCompletion(reply)) {
				this.ftpClient.disconnect();
				logger.error("登录FTP服务失败！");
				return isLogin;
			}

			isLogin = this.ftpClient.login(this.username, this.password);
			if (!isLogin) {
				logger.error("登录FTP服务失败！");
				return isLogin;
			}

			if(FTPReply.isPositiveCompletion(this.ftpClient.sendCommand("OPTS UTF8", "ON"))) {
				// 开启服务器对UTF-8的支持，如果服务器支持就用UTF-8编码，否则就使用本地编码（GBK）
				logger.info("send OPTS UTF8 on is true,设置：LOCAL_CHARSET=UTF-8");
				LOCAL_CHARSET = CharsetKit.UTF_8;
			}

			this.ftpClient.setCharset(Charset.forName(LOCAL_CHARSET));
			this.ftpClient.setControlEncoding(LOCAL_CHARSET);
			this.ftpClient.enterLocalPassiveMode(); // 设置被动模式
			isLogin = this.ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
			if (!isLogin) {
				logger.error("登录FTP服务失败！");
				return isLogin;
			}

			logger.info(this.username + "成功登陆FTP服务器");
			logger.info("登录耗时：***********" + (System.currentTimeMillis() - start) + "毫秒");
		} catch (Exception var6) {
			logger.error(this.username + "登录FTP服务失败！" + var6.getMessage());
		}

		this.ftpClient.setBufferSize(1024*1024*10);
		this.ftpClient.setDataTimeout(30000);
		this.isLogin = isLogin;
		return isLogin;
	}

	@Override
	public void ftpLogOut() {
		if (null != this.ftpClient && this.ftpClient.isConnected()) {
			try {
				boolean reuslt = this.ftpClient.logout();
				if (reuslt) {
					logger.info("成功退出服务器");
				}
			} catch (IOException var10) {
				var10.printStackTrace();
				logger.warn("退出FTP服务器异常！" + var10.getMessage());
			} finally {
				try {
					this.ftpClient.disconnect();
					this.isLogin = false;
				} catch (IOException var9) {
					var9.printStackTrace();
					logger.warn("关闭FTP服务器的连接异常！");
				}

			}
		}

	}

	@Override
	public List<String> downloadFile(String remoteFileName, String localFilePath, String remoteFilePath) {
		List<String> downList = new ArrayList<>();
		try {
			String workDir = this.ftpClient.printWorkingDirectory();
			logger.info("workingDirectory:" + workDir);

			logger.info("changeWorkingDirectory:" + remoteFilePath);
			if(!this.ftpClient.changeWorkingDirectory(remoteFilePath)) {
				logger.error("changeWorkingDirectory: ERROR ");
			}

			List<String> fileList = Collections.singletonList(remoteFileName);
			if(remoteFileName.contains("{rand}")) {
				String[] names = ftpClient.listNames();
				List<String> sepFileNameList = Arrays.asList(remoteFileName.replace("{rand}", ",").split(","));
				fileList = Stream.of(names).filter(s->{
					return sepFileNameList.stream().allMatch(s::contains);
				}).collect(Collectors.toList());
			}

			for (String realFileName : fileList) {
				String realLocalFileName = localFilePath;
				if(remoteFileName.contains("{rand}")) {
					if(localFilePath.contains("{rand}")) {
						realLocalFileName = localFilePath.replace(remoteFileName,realFileName);
					}else {
						realLocalFileName = localFilePath + realFileName;
					}
				}
				createDirectory(realLocalFileName);
 				File localFile  =  FileUtils.getFile(realLocalFileName);
				try(OutputStream outStream = new FileOutputStream(localFile)) {
					String remoteFileNameEncode = new String(realFileName.getBytes(),CharsetKit.CHARSET_ISO_8859_1);
					logger.info("LOCAL_CHARSET:" + LOCAL_CHARSET + ",localFile:" + realLocalFileName + ",ftpFile：" + realFileName + ",ftpFileEncode:" + "开始下载....");

					if (this.ftpClient.retrieveFile(remoteFileNameEncode, outStream)) {
						if (realLocalFileName.indexOf("tsv") >0) {
							// 处理.tsv文件
							String csvFilePath = realLocalFileName.replace("tsv.gz","csv");
							boolean TsvToCsv =  processTsvToCsv(realLocalFileName, csvFilePath);
							// 注意：这里存储的是转换后的CSV文件路径
							if(TsvToCsv){
								downList.add(csvFilePath);
								// 可选：删除原始的.tsv文件
								File file = new File(realLocalFileName);// NOSONAR
								file.delete();// NOSONAR
							}
						}else{
							downList.add(realLocalFileName);
							logger.info(realFileName + "成功下载到" + realLocalFileName);
						}
					}else {
						logger.error(realFileName + "下载失败,retrieveFile返回false!!!");
					}
				}
			}
		} catch (Exception var19) {
			var19.printStackTrace();
			logger.error(remoteFileName + "下载失败");
		}
		return downList;
	}

	public  boolean processTsvToCsv(String tsvFilePath, String csvFilePath) throws IOException {
		String pathName = CompressUtil.unCompressGzip(tsvFilePath);
		File file = new File(pathName);// NOSONAR
		Path tsvPath = Paths.get(pathName);// NOSONAR
		Path csvPath = Paths.get(csvFilePath);// NOSONAR
		boolean herad = true;
		try (BufferedReader reader = Files.newBufferedReader(tsvPath, StandardCharsets.UTF_8);
			 BufferedWriter writer = Files.newBufferedWriter(csvPath, StandardCharsets.UTF_8)) {

			String line;
			int size = 0;
			while ((line = reader.readLine()) != null) {
				String lineStr = "";
				if(herad) {
					lineStr = line.replace(".","_");
					size = lineStr.split("\t").length;
				}else{
					lineStr = line;
				}
				lineStr = lineStr.replace(",","，");
				String[] fields = lineStr.split("\t");
				String csvLine = String.join(",", fields);
				if(size > fields.length){
					for(int s = size-fields.length; s>0;s--){
						csvLine	+=",";
					}
				}
				csvLine = csvLine.replace("\\N","");
				writer.write(csvLine);
				writer.newLine();
				herad = false;
			}
			file.delete();// NOSONAR
			return true;
		} catch (IOException e) {
			e.printStackTrace();
		}
		file.delete();// NOSONAR
        return false;
    }

	@Override
	public List<String> downLoadDirectory(String localDirectoryPath, String remoteDirectory,final FileNameFilter filter) {
		List<String> downList = new ArrayList<>();
		try {
			File localDir = FileUtils.getFile(localDirectoryPath);
			if(!localDir.exists()) localDir.mkdirs();
			FTPFile[] allFile = this.ftpClient.listFiles(remoteDirectory);

			int currentFile;
			for (currentFile = 0; currentFile < allFile.length; ++currentFile) {
				if (!allFile[currentFile].isDirectory()) {
					final String subFileName = allFile[currentFile].getName();
					if (null == filter || filter.accept(subFileName)) {
						downList.addAll(downloadFile(subFileName, localDirectoryPath + "/" + subFileName, remoteDirectory));
					}
				}else {
					String strremoteDirectoryPath = remoteDirectory + "/" + allFile[currentFile].getName();
					downList.addAll(downLoadDirectory(localDirectoryPath, strremoteDirectoryPath,filter));
				}
			}
		} catch (IOException var7) {
			var7.printStackTrace();
			logger.info("下载文件夹失败");
		}
		return downList;
	}

	@Override
	public List<String> downLoadDirectory(String localDirectoryPath, String remoteDirectory) {
		return downLoadDirectory(localDirectoryPath,remoteDirectory,fileName->true);
	}

	@Override
	public boolean uploadFile(File localFile, String directory) {
		BufferedInputStream inStream = null;
		boolean success = false;
		int loop = 0;
		boolean var5;
		try {
			do {
				try {
					success = this.ftpClient.changeWorkingDirectory(directory);
					if(!success) {
						makeDir(directory);
					}
				} catch (Exception e) {
					logger.info("create directory :" + directory);
					makeDir(directory);
				}
				loop ++;
			} while (!success && loop < 2);

			if (success) {
				inStream = new BufferedInputStream(FileUtils.openInputStream(localFile));
				logger.info(localFile.getName() + "开始上传.....");
				success = this.ftpClient.storeFile(localFile.getName(), inStream);
				if (!success) {
					logger.error("storeFile方法异常");
					var5 = success;
					return var5;
				}
				logger.info(localFile.getName() + "上传成功");
				return success;
			}

			logger.error("changeWorkingDirectory方法异常");
			var5 = success;
		} catch (FileNotFoundException var19) {
			var19.printStackTrace();
			logger.error(localFile + "未找到");
			return success;
		} catch (IOException var20) {
			var20.printStackTrace();
			return success;
		} finally {
			if (inStream != null) {
				try {
					inStream.close();
				} catch (IOException var18) {
					var18.printStackTrace();
				}
			}

		}

		return var5;
	}

	/**
	 * 上传单个文件
	 *
	 * @param directory      ：远程下载目录(以路径符号结束)
	 * @param uploadFilePath 要上传的文件 如：D:\\test\\xxx.txt
	 * @param fileName       FTP服务器文件名称 如：xxx.txt ||xxx.txt.zip
	 * @throws Exception
	 */
    @Override
	public boolean uploadFile(String directory, String uploadFilePath, String fileName) {
    	BufferedInputStream inStream = null;
		boolean success = false;

		boolean var5;
		try {
			try {
				success = this.ftpClient.changeWorkingDirectory(directory);
			} catch (Exception e) {
				this.ftpClient.makeDirectory(directory);
				success = this.ftpClient.changeWorkingDirectory(directory);
			}

			if (success) {
				inStream = new BufferedInputStream(new FileInputStream(FileUtils.getFile(uploadFilePath)));
				logger.info(fileName + "开始上传.....");
				success = this.ftpClient.storeFile(fileName, inStream);
				if (!success) {
					logger.error("storeFile方法异常");
					var5 = success;
					return var5;
				}
				logger.info(fileName + "上传成功");
				return success;
			}

			logger.error("changeWorkingDirectory方法异常");
			var5 = success;
		} catch (FileNotFoundException var19) {
			var19.printStackTrace();
			logger.error(fileName + "未找到");
			return success;
		} catch (IOException var20) {
			var20.printStackTrace();
			return success;
		} finally {
			if (inStream != null) {
				try {
					inStream.close();
				} catch (IOException var18) {
					var18.printStackTrace();
				}
			}

		}

		return var5;
    }

	public boolean uploadDirectory(String localDirectory, String remoteDirectoryPath) {
		File src = FileUtils.getFile(localDirectory);

		try {
			remoteDirectoryPath = remoteDirectoryPath + src.getName() + "/";
			this.ftpClient.makeDirectory(remoteDirectoryPath);
		} catch (IOException var7) {
			var7.printStackTrace();
			logger.info(remoteDirectoryPath + "目录创建失败");
		}

		File[] allFile = src.listFiles();

		int currentFile;
		for (currentFile = 0; currentFile < allFile.length; ++currentFile) {
			if (!allFile[currentFile].isDirectory()) {
				String srcName = allFile[currentFile].getPath().toString();
				this.uploadFile(FileUtils.getFile(srcName), remoteDirectoryPath);
			}
		}

		for (currentFile = 0; currentFile < allFile.length; ++currentFile) {
			if (allFile[currentFile].isDirectory()) {
				this.uploadDirectory(allFile[currentFile].getPath().toString(), remoteDirectoryPath);
			}
		}

		return true;
	}


	public File getSchemeFile(String fileName, String fileUrl) {
		Object var6;
		try {
			this.ftpClient.connect(this.address, this.port);
			boolean flag = this.ftpClient.login(this.username, this.password);
			FTPClientConfig ftpClientConfig;
			if (!flag) {
				logger.error("登录FTP失败。");
				ftpClientConfig = null;
				return null;
			}

			ftpClientConfig = new FTPClientConfig(this.clientConfig);
			this.ftpClient.configure(ftpClientConfig);
			this.ftpClient.setFileType(2);
			int ch = this.ftpClient.getReplyCode();
			if (FTPReply.isPositiveCompletion(ch)) {
				logger.info("ftp链接成功");
				this.ftpClient.setRemoteVerificationEnabled(false);
				this.ftpClient.enterLocalPassiveMode();
				this.ftpClient.setControlEncoding(CharsetKit.GBK);
				boolean flag2 = this.ftpClient
						.changeWorkingDirectory(new String(fileUrl.getBytes(CharsetKit.GBK), CharsetKit.CHARSET_ISO_8859_1));
				FTPFile[] fs;
				if (!flag2) {
					logger.error("changeWorkingDirectory方法出错！");
					// fs = null;
					return null;
				}

				logger.info("changeWorkingDirectory：" + fileUrl);
				fs = this.ftpClient.listFiles();
				FTPFile[] var8 = fs;
				int var9 = fs.length;

				for (int var10 = 0; var10 < var9; ++var10) {
					FTPFile ff = var8[var10];
					logger.info("文件名：" + ff.getName());
					if (URLDecoder.decode(ff.getName(), CharsetKit.UTF_8).equals(fileName)) {
						logger.info("找到目标文件：" + fileName);
						File localFile = FileUtils.getFile(URLDecoder.decode(ff.getName(), CharsetKit.UTF_8));
						try(OutputStream outputStream = FileUtils.openOutputStream(FileUtils.getFile(localFile))){
							String remoteFileName = URLDecoder.decode(ff.getName(), CharsetKit.UTF_8);
							this.ftpClient.retrieveFile(new String(remoteFileName.getBytes(CharsetKit.GBK), CharsetKit.CHARSET_ISO_8859_1),
									outputStream);
							this.ftpClient.logout();
							File var15 = localFile;
							return var15;
						}catch (Exception e) {
							logger.error(e.getMessage(),e);
						}

					}
				}

				this.ftpClient.logout();
				logger.info("无法找到目标文件：" + fileName);
				return null;
			}

			this.ftpClient.disconnect();
			var6 = null;
		} catch (IOException var29) {
			logger.error("ftp文件下载出错：" + var29.getMessage());
			return null;
		} finally {
			if (this.ftpClient.isConnected()) {
				try {
					this.ftpClient.disconnect();
				} catch (IOException var28) {
					logger.error("ftp服务关闭出错：" + var28.getMessage());
				}
			}

		}

		return (File) var6;
	}

	/**
	 * 读取FTP服务器指定目录下的文件
	 * @param ip
	 * @param port
	 * @param userName
	 * @param userPass
	 * @param ftpPath
	 * @return
	 */
	public static FTPFile[] readFtpFiles(String ip, Integer port, String userName, String userPass, String ftpPath){
		FTPClient ftp = new FTPClient();
		try {
			ftp.connect(ip, port);
			ftp.login(userName, userPass);
			if(FTPReply.isPositiveCompletion(ftp.getReply())){
				logger.info("连接成功");
				ftp.enterLocalPassiveMode();
				ftp.changeWorkingDirectory(ftpPath);
				return ftp.listFiles();
			}
			logger.info("连接失败");
		} catch (Exception e){
			logger.error(e.getMessage(),e);
		} finally {
			if(ftp.isConnected()){
				try {
					ftp.disconnect();
				} catch (Exception e){
					logger.error(e.getMessage(),e);
				}
			}
		}

		return null;
	}

	/**
	 *
	 * makeDirectory 无法创建多级目录，需一级一级的建
	 *
	 * @param path
	 * @throws IOException
	 * @version v1.0
	 * @date 2023年7月7日下午1:36:18
	 */
	public void makeDir(String path) throws IOException {
		String[] directories = path.split("/");        //创建成功标识
		for (String dir : directories) {
			if (StringUtils.isBlank(dir)) {
				continue;
			}
			//切换目录，根据切换是否成功判断子目录是否存在
			boolean changeSuccess = ftpClient.changeWorkingDirectory(dir);
			//该级路径不存在就创建并切换
			if (!changeSuccess) {
				ftpClient.makeDirectory(dir);
				ftpClient.changeWorkingDirectory(dir);
			}
		}
	}

	public String getBasepath() {
		return basepath;
	}

	public void setBasepath(String basepath) {
		this.basepath = basepath;
	}

	public Boolean getIsLogin() {
		return isLogin;
	}

	public void setIsLogin(Boolean isLogin) {
		this.isLogin = isLogin;
	}

	public static void main(String[] args) throws IOException {
		FtpUtil ftp = new FtpUtil("10.18.18.36",21,"xkxkftp","1qaz!QAZ");
		ftp.ftpLogin();
		List<String> fileList = ftp.downLoadDirectory("D:/test/test1","/data", fileName->{
			return fileName.contains(".txt");
		});
		logger.info("ftp链接成功:" + fileList.toString());
		//FTPFile[] fs = ftpClient.listFiles();
		ftp.ftpLogOut();
	}

}
