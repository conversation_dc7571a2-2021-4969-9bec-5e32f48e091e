/**
 * @Title: PasswordCheckUtil.java
 * @Package cn.cmcc.common.utils
 * 
 * <AUTHOR>
 * @date 2022年3月4日
 * @version V1.0
 */
package cn.cmcc.common.utils;

import java.util.Arrays;
import java.util.List;

public class PasswordCheckUtil {
    /**
     * 密码最小长度，默认为6
     */
    public static String MIN_LENGTH = "8";

    /**
     * 密码最大长度，默认为20
     */
    public static String MAX_LENGTH = "20";

    /**
     * 特殊符号集合
     */
    public static String SPECIAL_CHAR = "!\\\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~";

    /**
     * 键盘物理位置横向不允许最小的连续个数
     */
    public static String LIMIT_HORIZONTAL_NUM_KEY = "4";

    /**
     * 键盘物理位置斜向不允许最小的连续个数
     */
    public static String LIMIT_SLOPE_NUM_KEY = "3";

    /**
     * 密码口令中字符在逻辑位置上不允许最小的连续个数
     */
    public static String LIMIT_LOGIC_NUM_CHAR = "3";

    /**
     * 密码口令中相同字符不允许最小的连续个数
     */
    public static String LIMIT_NUM_SAME_CHAR = "3";

    /**
     * 键盘横向方向规则
     */
    public static String[] KEYBOARD_HORIZONTAL_ARR = { "01234567890", "qwertyuiop", "asdfghjkl", "zxcvbnm", };
    /**
     * 键盘斜线方向规则
     */
    public static String[] KEYBOARD_SLOPE_ARR = { "1qaz", "2wsx", "3edc", "4rfv", "5tgb", "6yhn", "7ujm", "8ik,",
            "9ol.", "0p;/", "=[;.", "-pl,", "0okm", "9ijn", "8uhb", "7ygv", "6tfc", "5rdx", "4esz" };

    /**
     * 常用词库
     */
    public static String[] SIMPLE_WORDS = { "admin", "szim", "epicrouter", "password", "grouter", "dare", "root",
            "guest", "user", "success", "pussy", "mustang", "fuckme", "jordan", "test", "hunter", "jennifer", "batman",
            "thomas", "soccer", "sexy", "killer", "george", "asshole", "fuckyou", "summer", "hello", "secret", "fucker",
            "enter", "cookie", "administrator",
            // 中国网民常用密码
            "xiaoming", "taobao", "iloveyou", "woaini", "982464",
            // 国外网民常用密码
            "monkey", "letmein", "trustno1", "dragon", "baseball", "master", "sunshine", "ashley", "bailey", "shadow",
            "superman", "football", "michael", "qazwsx" };

	public static void main(String[] args) {
        String str = "1qaz!QAZ";
        System.out.println(evalPassword(str,""));
    }

    /**
     * 检测密码中字符长度
     * 
     * @param password
     * @return 符合长度要求 返回
     */
    public static boolean checkPasswordLength(String password) {
        boolean flag = false;

        // 如未设置最大长度，仅判断最小长度即可
        if (password.length() >= Integer.parseInt(MIN_LENGTH)
                && password.length() <= Integer.parseInt(MAX_LENGTH)) {
            flag = true;
        }

        return flag;
    }

    /**
     * 检查密码中是否包含数字
     * 
     * @param passwork
     * @return 包含数字 返回true
     */
    public static boolean checkContainDigit(String password) {
        char[] chPass = password.toCharArray();
        boolean flag = false;
        int num_count = 0;

        for (int i = 0; i < chPass.length; i++) {
            if (Character.isDigit(chPass[i])) {
                num_count++;
            }
        }
        if (num_count >= 1) {
            flag = true;
        }
        return flag;
    }

    /**
     * 检查密码中是否包含字母(不区分大小写)
     * 
     * @param passwork
     * @return 包含字母 返回true
     */
    public static boolean checkContainCase(String password) {
        char[] chPass = password.toCharArray();
        boolean flag = false;
        int char_count = 0;

        for (int i = 0; i < chPass.length; i++) {
            if (Character.isLetter(chPass[i])) {
                char_count++;
            }
        }

        if (char_count >= 1) {
            flag = true;
        }

        return flag;
    }

    /**
     * 检查密码中是否包含小写字母
     * 
     * @param passwork
     * @return 包含小写字母 返回true
     */
    public static boolean checkContainLowerCase(String password) {
        boolean flag = false;
        char[] chPass = password.toCharArray();
        int char_count = 0;

        for (int i = 0; i < chPass.length; i++) {
            if (Character.isLowerCase(chPass[i])) {
                char_count++;
            }
        }

        if (char_count >= 1) {
            flag = true;
        }

        return flag;
    }

    /**
     * 检查密码中是否包含大写字母
     * 
     * @param passwork
     * @return 包含大写字母 返回true
     */
    public static boolean checkContainUpperCase(String password) {
        boolean flag = false;
        char[] chPass = password.toCharArray();
        int char_count = 0;

        for (int i = 0; i < chPass.length; i++) {
            if (Character.isUpperCase(chPass[i])) {
                char_count++;
            }
        }

        if (char_count >= 1) {
            flag = true;
        }

        return flag;
    }

    /**
     * 检查密码中是否包含特殊字符
     * 
     * @param passwork
     * @return 包含特殊字符 返回true
     */
    public static boolean checkContainSpecialChar(String password) {
        boolean flag = false;
        char[] chPass = password.toCharArray();
        int special_count = 0;

        for (int i = 0; i < chPass.length; i++) {
            if (SPECIAL_CHAR.indexOf(chPass[i]) != -1) {
                special_count++;
            }
        }

        if (special_count >= 1) {
            flag = true;
        }

        return flag;
    }

    /**
     * 键盘规则匹配器 横向连续检测
     * 
     * @param password
     * @return 含有横向连续字符串 返回true
     */
    public static boolean checkLateralKeyboardSite(String password) {

        String t_password = new String(password);
        // 将字符串内所有字符转为小写
        t_password = t_password.toLowerCase();
        int n = t_password.length();

        /**
         * 键盘横向规则检测
         */
        boolean flag = false;
        int arrLen = KEYBOARD_HORIZONTAL_ARR.length;
        int limit_num = Integer.parseInt(LIMIT_HORIZONTAL_NUM_KEY);

        for (int i = 0; i + limit_num <= n; i++) {
            String distinguishStr = password.substring(i, i + limit_num);

            for (int j = 0; j < arrLen; j++) {
                String configStr = KEYBOARD_HORIZONTAL_ARR[j];
                String revOrderStr = new StringBuffer(KEYBOARD_HORIZONTAL_ARR[j]).reverse()
                        .toString();

                // 考虑 大写键盘匹配的情况
                String upperStr = KEYBOARD_HORIZONTAL_ARR[j].toUpperCase();
                if ((configStr.indexOf(distinguishStr) != -1) || (upperStr.indexOf(distinguishStr) != -1)) {
                    flag = true;
                    return flag;
                }
                // 考虑逆序输入情况下 连续输入
                String revUpperStr = new StringBuffer(upperStr).reverse().toString();
                if ((revOrderStr.indexOf(distinguishStr) != -1) || (revUpperStr.indexOf(distinguishStr) != -1)) {
                    flag = true;
                    return flag;
                }
            }
        }
        return flag;
    }

    /**
     * 键盘规则匹配器 斜向规则检测
     * 
     * @param password
     * @return 含有斜向连续字符串 返回true
     */
    public static boolean checkKeyboardSlantSite(String password) {
        String t_password = new String(password);
        t_password = t_password.toLowerCase();
        int n = t_password.length();
        /**
         * 键盘斜线方向规则检测
         */
        boolean flag = false;
        int arrLen = KEYBOARD_SLOPE_ARR.length;
        int limit_num = Integer.parseInt(LIMIT_SLOPE_NUM_KEY);

        for (int i = 0; i + limit_num <= n; i++) {
            String distinguishStr = password.substring(i, i + limit_num);
            for (int j = 0; j < arrLen; j++) {
                String configStr = KEYBOARD_SLOPE_ARR[j];
                String revOrderStr = new StringBuffer(KEYBOARD_SLOPE_ARR[j]).reverse().toString();
                // 检测包含字母(区分大小写)

                // 考虑 大写键盘匹配的情况
                String UpperStr = KEYBOARD_SLOPE_ARR[j].toUpperCase();
                if ((configStr.indexOf(distinguishStr) != -1) || (UpperStr.indexOf(distinguishStr) != -1)) {
                    flag = true;
                    return flag;
                }
                // 考虑逆序输入情况下 连续输入
                String revUpperStr = new StringBuffer(UpperStr).reverse().toString();
                if ((revOrderStr.indexOf(distinguishStr) != -1) || (revUpperStr.indexOf(distinguishStr) != -1)) {
                    flag = true;
                    return flag;
                }
            }
        }
        return flag;
    }

    /**
     * 评估a-z,z-a这样的连续字符
     * 
     * @param password
     * @return 含有a-z,z-a连续字符串 返回true
     */
    public static boolean checkSequentialChars(String password) {
        String t_password = new String(password);
        boolean flag = false;
        int limit_num = Integer.parseInt(LIMIT_LOGIC_NUM_CHAR);
        int normal_count = 0;
        int reversed_count = 0;

        // 检测包含字母(区分大小写)
        t_password = t_password.toLowerCase();
        int n = t_password.length();
        char[] pwdCharArr = t_password.toCharArray();

        for (int i = 0; i + limit_num <= n; i++) {
            normal_count = 0;
            reversed_count = 0;
            for (int j = 0; j < limit_num - 1; j++) {
                if (pwdCharArr[i + j + 1] - pwdCharArr[i + j] == 1) {
                    normal_count++;
                    if (normal_count == limit_num - 1) {
                        return true;
                    }
                }

                if (pwdCharArr[i + j] - pwdCharArr[i + j + 1] == 1) {
                    reversed_count++;
                    if (reversed_count == limit_num - 1) {
                        return true;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 评估aaaa, 1111这样的相同连续字符
     * 
     * @param password
     * @return 含有aaaa, 1111等连续字符串 返回true
     */
    public static boolean checkSequentialSameChars(String password) {
        String t_password = new String(password);
        int n = t_password.length();
        char[] pwdCharArr = t_password.toCharArray();
        boolean flag = false;
        int limit_num = Integer.parseInt(LIMIT_NUM_SAME_CHAR);
        int count = 0;
        for (int i = 0; i + limit_num <= n; i++) {
            count = 0;
            for (int j = 0; j < limit_num - 1; j++) {
                if (pwdCharArr[i + j] == pwdCharArr[i + j + 1]) {
                    count++;
                    if (count == limit_num - 1) {
                        return true;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 检测常用词库
     * 
     * @param password
     * @return 含有常见词库 返回true
     */
    public static boolean checkSimpleWord(String password) {
        List<String> simpleWords = Arrays.asList(SIMPLE_WORDS);
        return simpleWords.contains(password.toLowerCase());
    }
    
    /**
     * 检测密码和账号
     * 
     * @param password
     * @return 
     */
    public static boolean checkPasswdAccount(String password,String account) {
    	if(password.equals(account) || password.contains(account)) return false;
    	if(password.equals(StringUtils.reverse(account))) return false;
    	return true;
    }

    /**
     * 评估密码中包含的字符类型是否符合要求
     * 
     * @param password
     * @return 符合要求 返回true
     */
    public static String evalPassword(String password,String account) {
        if (password == null || "".equals(password)) {
            return "密码不能为空";
        }
        boolean flag = false;

        /**
         * 检测密码长度
         */
        flag = checkPasswordLength(password);
        if (!flag) {
            return "密码长度至少8位";
        }

        /**
         * 检测包含数字
         */
        flag = checkContainDigit(password);
        if (!flag) {
            return "密码必须包含数字";
        }

        /**
         * 检测包含字母
         */
        flag = checkContainCase(password);
        if (!flag) {
            return "密码必须包含字母";
        }

        /**
         * 检测包含特殊符号
         */
        flag = checkContainSpecialChar(password);
        if (!flag) {
            return "密码必须包含特殊符号";
        }

        /**
         * 检测键盘横向连续
         */
        flag = checkLateralKeyboardSite(password);
        if (flag) {
            return "密码不能包含横向连续，如qwe";
        }

        /**
         * 检测键盘斜向连续
         */
        flag = checkKeyboardSlantSite(password);
        if (flag) {
            return "密码不能包含斜向连续，如qaz";
        }

        /**
         * 检测逻辑位置连续
         */
        flag = checkSequentialChars(password);
        if (flag) {
            return "密码不能包含字母逻辑连续，如abc";
        }

        /**
         * 检测相邻字符是否相同
         */
        flag = checkSequentialSameChars(password);
        if (flag) {
            return "密码不能包含相邻连续字符，如aaaa";
        }

        /**
         * 检测常用词库
         */
        flag = checkSimpleWord(password);
        if (flag) {
            return "密码不能包含常用词库";
        }
        
        /**
         * 检查密码是否是包含账号或者账号倒序
         */
        flag = checkPasswdAccount(password,account);
        if(!flag) {
        	return "密码包含账号或者账号倒序";
        }
        
        return StringUtils.EMPTY;
    }
}
