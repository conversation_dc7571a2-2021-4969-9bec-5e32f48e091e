/**
 * @Title: RemoteDictService.java
 * @Package cn.cmcc.system.api.service
 *
 * <AUTHOR>
 * @date 2022年4月12日
 * @version V1.0
 */
package cn.cmcc.system.api.service;

import cn.cmcc.common.pojo.TreeSelect;
import cn.cmcc.system.api.pojo.dto.CityDto;

import java.util.List;

public interface RemoteCityService {
    List<CityDto> province();

    List<CityDto> city(String province);

    List<CityDto> county(String province, String city);

    List<String> position(String lon, String lat);

    CityDto selectTCityCompanyById(Long id);

    List<TreeSelect> userCityCounty(Long userId);
}
