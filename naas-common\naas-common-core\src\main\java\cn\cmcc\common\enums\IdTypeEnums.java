package cn.cmcc.common.enums;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.uuid.IdUtils;
import cn.cmcc.common.utils.uuid.SnowflakeIdWorker;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**  
 * 
 * <AUTHOR>
 * @date 2024-01-22 05:28:51
*/
@Getter
@AllArgsConstructor
public enum IdTypeEnums {
	
	UUID("uuid","UUID") {
		@Override
		public Object get() {
			return IdUtils.fastSimpleUUID();
		}
	},
	AUTO("auto","自增") {
		@Override
		public Object get() {
			return null;
		}
	},
	SNOW("snowId","雪花算法ID") {
		@Override
		public Object get() {
			return SnowflakeIdWorker.getId();
		}
	};

	private final String code;
    private final String name;
    
    public abstract Object get();
    
    public static final Object get(String dataType,String valueStrategy) {
    	JavaTypeEnum javaTypeEnum = JavaTypeEnum.getByValue(dataType);
    	if(StringUtils.isEmpty(valueStrategy)) {
    		IdTypeEnums idTypeEnums = javaTypeEnum.getIdType();
    		if(idTypeEnums == null) return null;
    		return idTypeEnums.get();
    	}else {
    		return IdTypeEnums.getByValue(valueStrategy).get();
    	}
    }
    
    public static IdTypeEnums getByValue(String value) {
        for (IdTypeEnums e : values()) {
            if (e.getCode().equals(value)) {
                return e;
            }
        }
        return AUTO;
    }
}
