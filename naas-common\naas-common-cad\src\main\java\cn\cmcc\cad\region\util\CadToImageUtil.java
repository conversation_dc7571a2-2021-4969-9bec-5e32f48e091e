package cn.cmcc.cad.region.util;

import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aspose.cad.fileformats.cad.cadobjects.Cad2DPoint;

import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadPoint;
import cn.cmcc.cad.entity.XhxkCadText;
import cn.cmcc.cad.region.entity.CadRegion;
import cn.cmcc.cad.service.ICadService;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.file.FileUtils;
import cn.cmcc.common.web.domain.AjaxResult;

public class CadToImageUtil {
	public static Logger logger = LoggerFactory.getLogger(CadToImageUtil.class);
	private int DEFAULT_JPG_WIDTH = 2000;
	private String id;
	private CadRegion square;
	private ICadService cadService;
	private String imageFile;
	private boolean clearText;
	private int jpgWidth;
	private int jpgHeight;
	private double scale;
	private int lineStroke;

	public CadToImageUtil(ICadService cadService, String id, CadRegion square, String imageFile, boolean clearText, int lineStroke) {
		super();
		this.id = id;
		this.square = square;
		this.cadService = cadService;
		this.imageFile = imageFile;
		this.clearText = clearText;
		this.lineStroke = lineStroke;

		int width = square.getWidth();
		int height = square.getHeight();
		this.jpgWidth = DEFAULT_JPG_WIDTH;
		this.scale = (double) jpgWidth / width;
		this.jpgHeight = (int) (height * scale);

		if(this.lineStroke<1) {
			this.lineStroke=1;
		}
	}

	public void paint(Graphics g) {
		Graphics2D g2d = (Graphics2D) g;
		g2d.setColor(Color.WHITE);
		g2d.fillRect(0, 0, this.jpgWidth, this.jpgHeight);
		g2d.setColor(Color.BLACK);

		List<XhxkCadBaseEntity> xhxkCadBaseEntities = square.getBaseEntities();
		for (XhxkCadBaseEntity entity : xhxkCadBaseEntities) {
			g2d = drawLine(g2d, entity);
		}
	}

	private Graphics2D drawLine(Graphics2D g2d, XhxkCadBaseEntity entity) {
		XhxkCadPoint minPoint = cadService.transformPoint(square.getMinPoint());
		List<XhxkCadPoint> points = entity.getPoints();
		if (points == null || points.isEmpty()) {
			return g2d;
		}
		if (points.size() == 1) {
			if (entity instanceof XhxkCadText) {
				if (!clearText) {
					XhxkCadText indoorCadEntity = (XhxkCadText) entity;
					double textWidth = indoorCadEntity.getTextWidth() * scale;
					XhxkCadPoint point = indoorCadEntity.getPosition();
					XhxkCadPoint point1 = transfer(point, scale, minPoint);
					Font font = new Font("宋体", Font.PLAIN, 12); // 使用宋体字体
					g2d.setFont(font); // 设置Graphics2D对象的字体
					FontMetrics fm = g2d.getFontMetrics(); // 获取字体度量对象
					String cadEntityText = indoorCadEntity.getText();
					String[] lines = cadEntityText.split("\n");
					int lineHeight = g2d.getFontMetrics().getHeight(); // 获取字体行高
					int x = (int) point1.getX();
					int y = jpgHeight - (int) point1.getY();
					int rowIndex = 0;
					for (int i = 0; i < lines.length; i++) {
						String text = lines[i];
						if (textWidth == 0) {
							g2d.drawString(text, x, y + rowIndex++ * lineHeight); // 绘制文本
						} else {
							int lastIndex = 0;
							String subText = "";
							for (int k = 0; k < text.length(); k++) {
								subText = text.substring(lastIndex, k);
								int rowTextWidth = fm.stringWidth(subText); // 获取文本宽度
								if (rowTextWidth > textWidth) {
									g2d.drawString(subText, x, y + rowIndex++ * lineHeight); // 绘制文本
									lastIndex = k;
								}
							}
							if (StringUtils.isNotEmpty(subText)) {
								g2d.drawString(subText, x, y + rowIndex++ * lineHeight); // 绘制文本
							}
						}
					}
				}
			}
		} else {
			XhxkCadPoint firstPoint = points.get(0);
			for (int i = 1; i < points.size(); i++) {
				XhxkCadPoint point = points.get(i);
				XhxkCadPoint point1 = transfer(firstPoint, scale, minPoint);
				XhxkCadPoint point2 = transfer(point, scale, minPoint);
				
				if (Double.isNaN(point2.getX()) || Double.isNaN(point2.getY())) {
					return g2d;
				}
				g2d.setStroke(new BasicStroke(lineStroke)); // 设置线条宽度为 5

				if (((point1.getX() < 20) && (point1.getY() < 20)) || ((point2.getX() < 20) && (point2.getY() < 20))) {// 仅标注时用
					continue;
				}

				if (((point1.getX() < 20) && (point1.getY() > jpgHeight - 20)) || ((point2.getX() < 20) && (point2.getY() > jpgHeight - 20))) {// 仅标注时用
					continue;
				}

				double lenth = Math.sqrt(Math.pow(point2.getX() - point1.getX(), 2) + Math.pow(point2.getY() - point1.getY(), 2));
				if ((point1.getX() == point2.getX()) && (lenth > jpgHeight * 0.75)) {
					continue;
				}
				if ((point1.getY() == point2.getY()) && (lenth > jpgWidth * 0.75)) {
					continue;
				}

				if ((lenth > jpgHeight * 0.85) && (lenth > jpgWidth * 0.85)) {
					continue;
				}

				g2d.drawLine((int) point1.getX(), jpgHeight - (int) point1.getY(), (int) point2.getX(), jpgHeight - (int) point2.getY());
				firstPoint = point;
			}
		}
		return g2d;
	}

	public AjaxResult buildImage() {
		try {
			long startTask = System.currentTimeMillis();
			String log = id + " : 图框ID[" + square.getIndex() + "] ";
			BufferedImage image = new BufferedImage(this.jpgWidth, this.jpgHeight, BufferedImage.TYPE_INT_RGB);
			Graphics2D g2d = image.createGraphics();
			long startPaint = System.currentTimeMillis();
			this.paint(g2d);
			long endPaint = System.currentTimeMillis();
			g2d.dispose();

			log += "图片绘画用时 : " + (endPaint - startPaint) + " ms , ";
			startPaint = System.currentTimeMillis();
			File file = FileUtils.getFile(imageFile);
			ImageIO.write(image, "jpeg", file);

			endPaint = System.currentTimeMillis();
			log += "图片生成用时 : " + (endPaint - startPaint) + " ms , ";

			long endTask = System.currentTimeMillis();
			log += "导出图片用时: : " + (endTask - startTask) + " ms , 图片路径 :  " + file.getAbsolutePath();
			logger.info(log);
			return AjaxResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return AjaxResult.error(e.getMessage());
		}
	}

	public XhxkCadPoint transfer(XhxkCadPoint point, double scale, XhxkCadPoint minPoint) {
		double x = point.getX() - minPoint.getX();
		double y = point.getY() - minPoint.getY();
		x = x * scale;
		y = y * scale;
		return new XhxkCadPoint((int) x, (int) y);
	}

	public boolean squareContain(XhxkCadPoint point) {
		Cad2DPoint minPoint = square.getMinPoint();
		Cad2DPoint maxPoint = square.getMaxPoint();

		boolean status = false;
		if ((point.getX() >= minPoint.getX()) && (point.getX() <= maxPoint.getX()) && (point.getY() >= minPoint.getY()) && (point.getY() <= maxPoint.getY())) {
			status = true;
		}
		return status;
	}

}