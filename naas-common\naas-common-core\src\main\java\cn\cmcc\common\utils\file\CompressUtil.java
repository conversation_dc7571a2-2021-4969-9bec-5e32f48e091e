package cn.cmcc.common.utils.file;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry;
import org.apache.commons.compress.archivers.sevenz.SevenZFile;
import org.apache.commons.compress.archivers.sevenz.SevenZMethod;
import org.apache.commons.compress.archivers.sevenz.SevenZOutputFile;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.io.FilenameUtils;
import org.springframework.util.Assert;

import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.text.CharsetKit;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @author: niuyh
 * @date: 2022/11/29
 */

@Slf4j
public class CompressUtil {
    public static File compress(List<File> input, String outputPath) {
        Assert.notEmpty(input, "input");
        File outFile = FileUtils.getFile(outputPath);
        try (SevenZOutputFile outArchive = new SevenZOutputFile(outFile)) {
            outArchive.setContentCompression(SevenZMethod.DEFLATE);
            //outArchive.setContentCompression(SevenZMethod.LZMA2); // slower, but better compression ratio, they use the same method to decompress
            SevenZArchiveEntry entry;
            for (File file : input) {
                entry = new SevenZArchiveEntry();
                entry.setName(file.getName());
                Path path = file.toPath();
                BasicFileAttributes attributes = Files.readAttributes(path, BasicFileAttributes.class);
                long accessDate = attributes.lastAccessTime().to(TimeUnit.MILLISECONDS);
                long lastModifiedDate = attributes.lastModifiedTime().to(TimeUnit.MILLISECONDS);
                long creationDate = attributes.creationTime().to(TimeUnit.MILLISECONDS);
                entry.setAccessDate(new Date(accessDate));
                entry.setLastModifiedDate(new Date(lastModifiedDate));
                entry.setCreationDate(new Date(creationDate));
                outArchive.putArchiveEntry(entry);
                InputStream inputStream = Files.newInputStream(path);
                byte[] buffer = new byte[2 << 24];
                int read;
                while ((read = inputStream.read(buffer)) > -1) {
                    outArchive.write(buffer, 0, read);
                }
                inputStream.close();
                outArchive.closeArchiveEntry();
            }
            outArchive.finish();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return outFile;
    }

    /**
     * @Title: compress
     * @Description: TODO
     * @param filePaths 需要压缩的文件地址列表（绝对路径）
     * @param zipFilePath 需要压缩到哪个zip文件（无需创建这样一个zip，只需要指定一个全路径）
     * @param keepDirStructure 压缩后目录是否保持原目录结构
     * @throws IOException
     * @return int   压缩成功的文件个数
     */
     public static int compress(List<String> filePaths, String zipFilePath,Boolean keepDirStructure) throws IOException{
         byte[] buf = new byte[1024];
         File zipFile = FileUtils.getFile(zipFilePath);
         //zip文件不存在，则创建文件，用于压缩
         int fileCount = 0;//记录压缩了几个文件？
         if(!zipFile.exists()) {
             if(!zipFile.createNewFile()) {
            	 return fileCount;
             }
         }
         try(ZipOutputStream zos = new ZipOutputStream(FileUtils.openOutputStream(zipFile))){
             for(int i = 0; i < filePaths.size(); i++){
                 String relativePath = filePaths.get(i);
                 if(StringUtils.isEmpty(relativePath)){
                     continue;
                 }
                 File sourceFile = FileUtils.getFile(relativePath);//绝对路径找到file
                 if(sourceFile == null || !sourceFile.exists()){
                     continue;
                 }

                 log.info("压缩文件:{}",relativePath);
                 try(FileInputStream fis = FileUtils.openInputStream(sourceFile)){
                	 if(keepDirStructure != null && keepDirStructure){
                         //保持目录结构
                         zos.putNextEntry(new ZipEntry(relativePath));
                     }else{
                         //直接放到压缩包的根目录
                         zos.putNextEntry(new ZipEntry(sourceFile.getName()));
                     }
                     int len;
                     while((len = fis.read(buf)) > 0){
                         zos.write(buf, 0, len);
                     }
                     zos.closeEntry();
                     fileCount++;
                 }

             }
         } catch (Exception e) {
        	 log.error(e.getMessage(),e);
         }
         return fileCount;
     }

    /**
     * 解压缩7z文件到指定目录
     * 该方法使用了try-with-resources语句来自动管理资源，确保在操作完成后关闭文件
     *
     * @param input 要解压缩的7z文件
     */
    public static void deCompress(File input) {
        // 使用try-with-resources语句打开7z文件，确保在操作完成后自动关闭
        try (SevenZFile sevenZFile = new SevenZFile(input)) {
            // 获取7z文件中的所有条目
            final Iterable<SevenZArchiveEntry> entries = sevenZFile.getEntries();
            // 初始化缓冲区用于读写数据
            byte[] buffer = new byte[2 << 24];
            // 定义输入流变量
            InputStream inputStream;
            // 获取输入文件的父目录
            File parent = input.getParentFile();
            // 遍历7z文件中的所有条目
            for (SevenZArchiveEntry entry : entries) {
                // 获取当前条目的名称
                String entryName = entry.getName();
                // 根据条目名称和父目录创建文件对象
                File file = FileUtils.getFile(parent, entryName);
                // 使用try-with-resources语句打开文件输出流，确保操作完成后自动关闭
                try (FileOutputStream fos = FileUtils.openOutputStream(file)) {
                    // 获取当前条目的输入流
                    inputStream = sevenZFile.getInputStream(entry);
                    // 循环读取条目中的数据并写入文件
                    while (true) {
                        // 读取数据到缓冲区
                        int read = inputStream.read(buffer);
                        // 如果没有更多数据可读，跳出循环
                        if (!(read > 0)) //noinspection BreakStatement
                            break;
                        // 将读取的数据写入文件
                        fos.write(buffer, 0, read);
                    }
                }
            }
        } catch (IOException e) {
            // 如果发生IO异常，将其包装为运行时异常并抛出
            throw new RuntimeException(e);
        }
    }

    /**
     * @param zipFile 压缩文件
     * @throws IOException
     */
    public static List<String> unZipFiles(final String zipFile) throws IOException {
    	String unFileDir = zipFile.substring(0,zipFile.lastIndexOf("."));
    	return unZipFiles(FileUtils.getFile(zipFile),unFileDir);
    }

    /**
     * @param zipFile 压缩文件
     * @param descDir 目的路径
     * @throws IOException
     */
    public static List<String> unZipFiles(final File zipFile, final String descDir) throws IOException {
        final File pathFile = FileUtils.getFile(descDir);
        if (!pathFile.exists()) {
            pathFile.mkdirs();
        }
        List<String> fileNameList = new ArrayList<>();
        try(ZipFile zip = new ZipFile(zipFile, CharsetKit.CHARSET_GBK)){
        	final Enumeration<?> entries = zip.entries();
            while (entries.hasMoreElements()) {
                final ZipEntry entry = (ZipEntry) entries.nextElement();
                final String zipEntryName = entry.getName();
                final String outPath = (descDir + "/" + zipEntryName).replaceAll("\\*", "/");
                final File file = FileUtils.getFile(FilenameUtils.getFullPathNoEndSeparator(outPath));
                if (!file.exists()) {
                    file.mkdirs();
                }

                final File outFile = FileUtils.getFile(outPath);
                if( entry.isDirectory() && !outFile.exists()){
                	outFile.mkdirs();
                }
                if (outFile.isDirectory()) {
                    continue;
                }
                try(final InputStream in = zip.getInputStream(entry);
                		OutputStream out = new FileOutputStream(outFile)){
                    final byte[] buf1 = new byte[1024];
                    int len;
                    while ((len = in.read(buf1)) > 0) {
                        out.write(buf1, 0, len);
                    }
                    fileNameList.add(outFile.getAbsolutePath());
    			}
            }
        }

        log.info("******************解压完毕********************");
        return fileNameList;
    }

    /**
     * 归档
     * @param entry
     * @return
     */
    private static String archive(String path) throws Exception {
        File file = FileUtils.getFile(path);

        String baseName = file.getName();
        String tarName = file.getAbsolutePath() + ".tar";
        try(FileOutputStream fos = FileUtils.openOutputStream(FileUtils.getFile(tarName));
        		TarArchiveOutputStream tos = new TarArchiveOutputStream(fos)){

            if(file.isDirectory()){
                archiveDir(file, tos, baseName);
            }else{
                archiveHandle(tos, file, baseName);
            }
        } catch (Exception e) {
            throw e;
        }

        return tarName;
    }

    /**
     * 递归处理，准备好路径
     * @param file
     * @param tos
     * @param base
     */
    private static void archiveDir(File file, TarArchiveOutputStream tos, String basePath) throws Exception {
        File[] listFiles = file.listFiles();
        for(File fi : listFiles){
            if(fi.isDirectory()){
                archiveDir(fi, tos, basePath + File.separator + fi.getName());
            }else{
                archiveHandle(tos, fi, basePath);
            }
        }
    }

    /**
     * 具体归档处理（文件）
     * @param tos
     * @param fi
     * @param base
     */
    private static void archiveHandle(TarArchiveOutputStream tos, File fi, String basePath) throws Exception {
        TarArchiveEntry tEntry = new TarArchiveEntry(basePath + File.separator + fi.getName());
        tEntry.setSize(fi.length());

        tos.putArchiveEntry(tEntry);
        try(FileInputStream fis = new FileInputStream(fi);
        		BufferedInputStream bis = new BufferedInputStream(fis)){
            byte[] buffer = new byte[1024];
            int read = -1;
            while((read = bis.read(buffer)) != -1){
                tos.write(buffer, 0 , read);
            }
        } catch (Exception e) {
            throw e;
        }

        tos.closeArchiveEntry();//这里必须写，否则会失败
    }

    /**
     * 把tar包压缩成gz
     * @param path
     * @throws IOException
     * @return
     */
    public static String compressArchive(String path) throws IOException{
        try(BufferedInputStream bis = new BufferedInputStream(FileUtils.openInputStream(FileUtils.getFile(path)));
        		GzipCompressorOutputStream gcos = new GzipCompressorOutputStream(new BufferedOutputStream(
        				FileUtils.openOutputStream(FileUtils.getFile(path + ".gz"))))){
        	 byte[] buffer = new byte[1024];
             int read = -1;
             while((read = bis.read(buffer)) != -1){
                 gcos.write(buffer, 0, read);
             }
             return path + ".gz";

        }
    }

    /**
     * 解压成tar
     * @param archive
     */
    public static List<String> unCompressTarGz(String gzPath) throws Exception {
        File file = FileUtils.getFile(gzPath);
        String fileName = file.getName().substring(0, file.getName().lastIndexOf("."));
        String finalName = file.getParent() + File.separator + fileName;

        try (FileInputStream fis = new FileInputStream(file);
        		BufferedInputStream bis = new BufferedInputStream(fis);
        		FileOutputStream fos = new FileOutputStream(FileUtils.getFile(finalName));
        		BufferedOutputStream bos = new BufferedOutputStream(fos);
        		GzipCompressorInputStream gcis = new GzipCompressorInputStream(bis)){

            byte[] buffer = new byte[1024];
            int read = -1;
            while((read = gcis.read(buffer)) != -1){
                bos.write(buffer, 0, read);
            }
        }

        return unCompressTar(finalName);
    }

    /**
     * 解压tar
     * @param finalName
     * @throws IOException
     */
    private static List<String> unCompressTar(String finalName) throws Exception {
        File file = FileUtils.getFile(finalName);
        String parentPath = finalName.replace(".tar","");
        List<String> fileNameList = new ArrayList<>();
        try(FileInputStream fis = new FileInputStream(file);
        		TarArchiveInputStream tais = new TarArchiveInputStream(fis)) {

            TarArchiveEntry tarArchiveEntry = null;
            while((tarArchiveEntry = tais.getNextTarEntry()) != null){
                 String name = tarArchiveEntry.getName();
                 File tarFile = FileUtils.getFile(parentPath, name);
                 if(!tarFile.getParentFile().exists()){
                     tarFile.getParentFile().mkdirs();
                 }

                 try(FileOutputStream fos = new FileOutputStream(tarFile);
                		 BufferedOutputStream bos = new BufferedOutputStream(fos)){
                     int read = -1;
                     byte[] buffer = new byte[1024];
                     while((read = tais.read(buffer)) != -1){
                         bos.write(buffer, 0, read);
                     }

                     fileNameList.add(tarFile.getAbsolutePath());
                } catch (Exception e) {
                    throw e;
                }
             }
        } catch (Exception e) {
            throw e;
        }
        if(file.exists() && file.delete()) {
        }
        return fileNameList;
    }

    public static String compressTarGz(String path) throws Exception {
        //生成tar包
        String tarPath = archive(path);
        String gzPath = compressArchive(tarPath);//生成gz包
        File file = FileUtils.getFile(tarPath);
        if (file.exists() && file.delete());//删除tar文件
        return gzPath;
    }

    /**
     * 压缩为gzip
     *
     * @param path
     * @param outFileName
     * @throws FileNotFoundException
     * @throws IOException
     * @version v1.0
     * @date 2023年7月6日下午6:03:12
     */
    public static void compressGzip(String path,String outFileName) throws FileNotFoundException, IOException
    {
        byte[] buffer = new byte[1024];
        try(FileInputStream in = new FileInputStream(FileUtils.getFile(path));
            GZIPOutputStream os =
                        new GZIPOutputStream(new FileOutputStream(FileUtils.getFile(outFileName))))
        {
            int totalSize;
            while((totalSize = in.read(buffer)) > 0 ){
                os.write(buffer, 0, totalSize);
            }
            os.finish();
        }
    }

    /**
     * 解压gz
     * @param fileName
     * @throws FileNotFoundException
     * @throws IOException
     */
    public static String unCompressGzip(String fileName) throws FileNotFoundException, IOException {
    	String unFileName = fileName.replace(".gz","");

        try (FileInputStream fis = new FileInputStream(FileUtils.getFile(fileName));
         		BufferedInputStream bis = new BufferedInputStream(fis);
         		FileOutputStream fos = new FileOutputStream(FileUtils.getFile(unFileName));
         		BufferedOutputStream bos = new BufferedOutputStream(fos);
         		GzipCompressorInputStream gcis = new GzipCompressorInputStream(bis)){

            byte[] buffer = new byte[1024];
             int read = -1;
             while((read = gcis.read(buffer)) != -1){
                 bos.write(buffer, 0, read);
             }
        }

        return unFileName;
    }
}
