package cn.cmcc.cad.region.entity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.aspose.cad.fileformats.cad.cadobjects.Cad2DPoint;

import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadBlockEntity;
import cn.cmcc.cad.entity.XhxkCadPoint;
import cn.cmcc.cad.entity.XhxkCadText;
import cn.cmcc.cad.service.CadUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * baseEntities对象中无组合对象
 * 
 * <AUTHOR>
 *
 */
@Data
@Slf4j
public class CadRegion {

	private int index;
	private Cad2DPoint a;
	private Cad2DPoint b;
	private Cad2DPoint c;
	private Cad2DPoint d;

	private Cad2DPoint minPoint;
	private Cad2DPoint maxPoint;
	private int width;
	private int height;

	private List<XhxkCadBaseEntity> baseEntities;
	private List<XhxkCadBlockEntity> blockEntities;

	private String gpsLon;
	private String gpsLat;

	public CadRegion(int index, Cad2DPoint a, Cad2DPoint b, Cad2DPoint c, Cad2DPoint d) {
		super();
		this.index = index;
		this.a = a;
		this.b = b;
		this.c = c;
		this.d = d;

		double minx = Math.min(Math.min(a.getX(), b.getX()), Math.min(c.getX(), d.getX()));
		double miny = Math.min(Math.min(a.getY(), b.getY()), Math.min(c.getY(), d.getY()));
		double maxx = Math.max(Math.max(a.getX(), b.getX()), Math.max(c.getX(), d.getX()));
		double maxy = Math.max(Math.max(a.getY(), b.getY()), Math.max(c.getY(), d.getY()));
		this.minPoint = new Cad2DPoint(minx, miny);
		this.maxPoint = new Cad2DPoint(maxx, maxy);
		this.width = (int) (maxx - minx) + 1;
		this.height = (int) (maxy - miny) + 1;
	}

	public String getKey() {
		return String.format("(%f,%f) ~ (%f,%f)", minPoint.getX(), minPoint.getY(), maxPoint.getX(), maxPoint.getY());
	}

	public String toLog() {
		return String.format("图框编号:%d 图框区域: (%f,%f) ~ (%f,%f) 图框大小: %d*%d", index, minPoint.getX(), minPoint.getY(), maxPoint.getX(), maxPoint.getY(), width, height);
	}

	public void readGPS() {
		List<XhxkCadText> cadTexts = new ArrayList<XhxkCadText>();
		XhxkCadText lonCadText = null;
		XhxkCadText latCadText = null;
		Double lon = null;
		Double lat = null;
		for (XhxkCadBaseEntity baseEntity : this.baseEntities) {
			if (baseEntity instanceof XhxkCadText) {
				XhxkCadText cadText = (XhxkCadText) baseEntity;
				String text = cadText.getText().replaceAll(" ", "");
				cadText.setText(text);
				cadTexts.add(cadText);
				if (lon == null) {
					String lonReg = "[经度|东经]\\D{1,4}([\\d]{1,}\\.[\\d]{1,})";
					try {
						String value = getMatcherValue(cadText.getText(), lonReg);
						lon = Double.parseDouble(value);
						gpsLon = value;
					} catch (Exception e) {
						log.error(e.getMessage(),e);
					}
				}
				if (lat == null) {
					String latReg = "[纬度|北纬]\\D{1,4}([\\d]{1,}\\.[\\d]{1,})";
					try {
						String value = getMatcherValue(cadText.getText(), latReg);
						lat = Double.parseDouble(value);
						gpsLat = value;
					} catch (Exception e) {
						log.error(e.getMessage(),e);
					}
				}
				if (lonCadText == null) {
					if (text.contains("经度") || text.contains("东经")) {
						lonCadText = cadText;
					}
				}
				if (latCadText == null) {
					if (text.contains("纬度") || text.contains("北纬")) {
						latCadText = cadText;
					}
				}
			}
		}

		if ((lon == null) && (lat == null)) {
			if ((cadTexts != null) && !cadTexts.isEmpty() && (lonCadText != null)) {
				final XhxkCadPoint lonPosition_ = lonCadText.getPosition();
				final XhxkCadPoint latPosition_ = lonCadText.getPosition();
				List<XhxkCadText> lonCadTexts = cadTexts.stream().filter(ent -> {
					XhxkCadPoint position = ent.getPosition();
					double xSetp = this.width * 0.1;
					double ySetp = this.height * 0.1;
					return (position.getY() < lonPosition_.getY()) && (position.getY() >= lonPosition_.getY() - ySetp) && (position.getX() < lonPosition_.getX() + xSetp) && (position.getX() >= lonPosition_.getX() - xSetp) && (ent.getText().length() > ent.getText().replace("[\\d]{1,}\\.[\\d]{1,}", "").length());
				}).collect(Collectors.toList());
				if ((lonCadTexts != null) && !lonCadTexts.isEmpty()) {
					Collections.sort(lonCadTexts, new Comparator<XhxkCadText>() {
						@Override
						public int compare(XhxkCadText p1, XhxkCadText p2) {
							return Double.compare(CadUtil.distince(lonPosition_, p2.getPosition()), CadUtil.distince(lonPosition_, p1.getPosition()));
						}
					});
					
					try {
						
						String value = getMatcherValue(lonCadTexts.get(0).getText(), "([\\d]{1,}\\.[\\d]{1,})");
						lon = Double.parseDouble(value);
						gpsLon = value;
	
						Collections.sort(lonCadTexts, new Comparator<XhxkCadText>() {
							@Override
							public int compare(XhxkCadText p1, XhxkCadText p2) {
								return Double.compare(CadUtil.distince(latPosition_, p2.getPosition()), CadUtil.distince(latPosition_, p1.getPosition()));
							}
						});
						value = getMatcherValue(lonCadTexts.get(0).getText(), "([\\d]{1,}\\.[\\d]{1,})");
						lat = Double.parseDouble(value);
						gpsLat = value;

					} catch (Exception e) {
						// TODO: handle exception
					}
				}
			}
		}
	}

	private String getMatcherValue(String text, String reg) {
		Pattern pattern = Pattern.compile(reg);
		Matcher matcher = pattern.matcher(text);
		if (matcher.find()) {
			return matcher.group(1);
		}
		return null;
	}

	public static void main(String[] args) {
		String input = "东经经度：120.60933°";
		Pattern pattern = Pattern.compile("[经度|东经]\\D{1,4}([\\d]{1,}\\.[\\d]{1,})");
		Matcher matcher = pattern.matcher(input);
		if (matcher.find()) {
			String longitude = matcher.group(1);
			System.out.println(longitude);
		}
		System.out.println(input.replaceAll("[\\d]{1,}\\.[\\d]{1,}", ""));
	}
}
