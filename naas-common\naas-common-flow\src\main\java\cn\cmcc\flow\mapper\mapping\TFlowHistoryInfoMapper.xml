<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.cmcc.flow.mapper.TFlowHistoryInfoMapper">
  <resultMap id="BaseResultMap" type="cn.cmcc.flow.domain.TFlowHistoryInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="form_ins_id" jdbcType="VARCHAR" property="formInsId" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="proc_inst_id" jdbcType="VARCHAR" property="procInstId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="seq_code" jdbcType="VARCHAR" property="seqCode" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="take_up_time" jdbcType="BIGINT" property="takeUpTime" />
    <result column="deal_role" jdbcType="VARCHAR" property="dealRole"/>
    <result column="extend_data" jdbcType="OTHER" property="extendData"/>
    <result column="due_date" jdbcType="TIMESTAMP" property="dueDate"/>
    <result column="time_out_time" jdbcType="BIGINT" property="timeOutTime"/>
  </resultMap>

  <sql id="insertBatchSql">
        (id, form_ins_id, node_id, task_id, comment
        ,proc_inst_id, user_id, create_time, result, user_name
        ,seq_code, node_name, order_no, start_time
        ,take_up_time,deal_role,due_date,time_out_time)
        VALUES
        <foreach collection="list" item="item" open="" separator=", " close="">
            (#{item.id}, #{item.formInsId}, #{item.nodeId}, #{item.taskId}, #{item.comment}
            ,#{item.procInstId}, #{item.userId}, #{item.createTime}, #{item.result}, #{item.userName}
            ,#{item.seqCode}, #{item.nodeName}, #{item.orderNo}, #{item.startTime}
            ,#{item.takeUpTime},#{item.dealRole},#{item.dueDate},#{item.timeOutTime})
        </foreach>
    </sql>

    <insert id="insertBatch">
        INSERT INTO t_flow_history_info <include refid="insertBatchSql"/>
    </insert>
  
  <select id="getFlowHistoryAndCurTaskList" parameterType="String" resultType="map">
  		WITH tab AS ( SELECT task_def_key_, proc_def_id_, assignee_ AS assignee, to_char( create_time_, 'YYYY-MM-DD HH24:MI:SS' ) AS create_time FROM act_ru_task WHERE proc_inst_id_ = #{procInstId} ) 
		  select * from (
			SELECT
				node_name AS taskName,
				user_name AS userName,
				COMMENT AS remark,
				RESULT,
				node_id,
				seq_code,
				to_char( create_time, 'YYYY-MM-DD HH24:MI:SS' ) AS create_time 
			FROM
				t_flow_history_info 
			WHERE
				proc_inst_id = #{procInstId}
			
			UNION ALL
			SELECT
				node_name AS taskname,
				(
				SELECT
					string_agg ( b.NAME, ',' ) 
				FROM
					( SELECT * FROM regexp_split_to_table( node_user_groupid, ',' ) role_code )
					A JOIN sys_role b ON A.role_code = b.role_key 
				) username,
				'当前处理环节' AS remark,
				'待处理' AS RESULT,
				node_id,
				null AS seq_code,
				null AS create_time 
			FROM
				t_flow_def_info dmp
				INNER JOIN tab ON tab.task_def_key_ = dmp.node_id 
				AND tab.proc_def_id_ = dmp.proc_def_id ) t order by t.create_time asc NULLs last
  </select>
  
</mapper>