package cn.cmcc.kernel.service.impl;

import cn.cmcc.common.context.SecurityUtils;
import cn.cmcc.common.pojo.LoginUser;
import cn.cmcc.kernel.domain.TQueryCodePermission;
import cn.cmcc.kernel.domain.TRoleQueryCodePermission;
import cn.cmcc.kernel.mapper.TQueryCodePermissionMapper;
import cn.cmcc.kernel.mapper.TRoleQueryCodePermissionMapper;
import cn.cmcc.kernel.service.IQueryCodePermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通用查询编码权限服务实现
 *
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Service
public class QueryCodePermissionServiceImpl implements IQueryCodePermissionService {

    @Autowired
    private TQueryCodePermissionMapper queryCodePermissionMapper;

    @Autowired
    private TRoleQueryCodePermissionMapper roleQueryCodePermissionMapper;


    @Override
    @Cacheable(value = "queryCodePermission", key = "#username + ':' + #queryCode")
    public boolean hasPermission(String username, String queryCode, Long userId) {
        try {
            // 超级管理员拥有所有权限
            if (SecurityUtils.isAdmin(userId)) {
                log.debug("用户 [{}] 为超级管理员，允许访问查询编码 [{}]", username, queryCode);
                return true;
            }

            boolean hasPermission = checkUserQueryCodePermission(username, queryCode);

            log.debug("用户 [{}] 访问查询编码 [{}] 权限检查结果: {}", username, queryCode, hasPermission);
            return hasPermission;

        } catch (Exception e) {
            log.error("检查用户 [{}] 查询编码 [{}] 权限时发生异常", username, queryCode, e);
            return false;
        }
    }

    /**
     * 检查用户对查询编码的权限
     * 实现多层权限检查：用户直接权限 → 角色权限
     */
    private boolean checkUserQueryCodePermission(String username, String queryCode) {
        try {
            // 第一层：检查用户直接权限
            TQueryCodePermission userPermission = queryCodePermissionMapper.selectByUsernameAndCodeAndType(
                    username, queryCode, TQueryCodePermission.PermissionType.READ);
            if (userPermission != null) {
                log.debug("用户 [{}] 拥有查询编码 [{}] 的直接权限", username, queryCode);
                return true;
            }

            // 第二层：检查角色权限
            return checkUserRolePermission(username, queryCode);

        } catch (Exception e) {
            log.error("检查用户 [{}] 查询编码 [{}] 权限时发生异常", username, queryCode, e);
            return false;
        }
    }

    /**
     * 检查用户的角色权限
     */
    private boolean checkUserRolePermission(String username, String queryCode) {
        try {
            // 获取当前用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                log.warn("无法获取用户 [{}] 的登录信息", username);
                return false;
            }

            // 获取用户角色列表
            Set<String> userRoles = loginUser.getRoles();
            if (CollectionUtils.isEmpty(userRoles)) {
                log.debug("用户 [{}] 没有分配任何角色", username);
                return false;
            }

            // 查询角色权限
            List<String> roleKeyList = new ArrayList<>(userRoles);
            List<TRoleQueryCodePermission> rolePermissions = roleQueryCodePermissionMapper
                    .selectByRoleKeysAndCodeAndType(roleKeyList, queryCode, TRoleQueryCodePermission.PermissionType.READ);

            if (!CollectionUtils.isEmpty(rolePermissions)) {
                log.debug("用户 [{}] 通过角色权限可以访问查询编码 [{}]", username, queryCode);
                return true;
            }

            log.debug("用户 [{}] 没有访问查询编码 [{}] 的权限", username, queryCode);
            return false;

        } catch (Exception e) {
            log.error("检查用户 [{}] 角色权限时发生异常", username, e);
            return false;
        }
    }

    @Override
    @CacheEvict(value = "queryCodePermission", key = "#username + ':*'")
    public void clearUserPermissionCache(String username) {
        log.info("清除用户 [{}] 的查询编码权限缓存", username);
    }

    @Override
    @CacheEvict(value = "queryCodePermission", key = "'*:' + #queryCode")
    public void clearQueryCodePermissionCache(String queryCode) {
        log.info("清除查询编码 [{}] 的权限缓存", queryCode);
    }

    // ==================== 用户权限管理方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void grantUserPermission(String username, String queryCode, String permissionType) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(queryCode)) {
            throw new IllegalArgumentException("用户名和查询编码不能为空");
        }

        // 检查是否已存在相同权限
        TQueryCodePermission existingPermission = queryCodePermissionMapper
                .selectByUsernameAndCodeAndType(username, queryCode, permissionType);
        if (existingPermission != null) {
            log.info("用户 [{}] 已拥有查询编码 [{}] 的 [{}] 权限", username, queryCode, permissionType);
            return;
        }

        // 获取用户ID
        String userId = getUserIdByUsername(username);

        // 创建权限记录
        TQueryCodePermission permission = new TQueryCodePermission(userId, username, queryCode, permissionType);
        permission.setCreateBy(SecurityUtils.getUserName());

        queryCodePermissionMapper.insert(permission);

        // 清除相关缓存
        clearUserPermissionCache(username);
        clearQueryCodePermissionCache(queryCode);

        log.info("成功授予用户 [{}] 查询编码 [{}] 的 [{}] 权限", username, queryCode, permissionType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeUserPermission(String username, String queryCode) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(queryCode)) {
            throw new IllegalArgumentException("用户名和查询编码不能为空");
        }

        int deletedCount = queryCodePermissionMapper.deleteByUsernameAndCode(username, queryCode);

        if (deletedCount > 0) {
            // 清除相关缓存
            clearUserPermissionCache(username);
            clearQueryCodePermissionCache(queryCode);

            log.info("成功撤销用户 [{}] 查询编码 [{}] 的权限，删除 {} 条记录", username, queryCode, deletedCount);
        } else {
            log.info("用户 [{}] 没有查询编码 [{}] 的权限，无需撤销", username, queryCode);
        }
    }

    @Override
    public List<TQueryCodePermission> getUserPermissions(String username) {
        if (!StringUtils.hasText(username)) {
            throw new IllegalArgumentException("用户名不能为空");
        }

        return queryCodePermissionMapper.selectByUsername(username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchGrantUserPermissions(String username, List<String> queryCodes, String permissionType) {
        if (!StringUtils.hasText(username) || CollectionUtils.isEmpty(queryCodes)) {
            throw new IllegalArgumentException("用户名和查询编码列表不能为空");
        }

        String userId = getUserIdByUsername(username);
        String currentUser = SecurityUtils.getUserName();

        List<TQueryCodePermission> permissions = new ArrayList<>();
        for (String queryCode : queryCodes) {
            // 检查是否已存在
            TQueryCodePermission existing = queryCodePermissionMapper
                    .selectByUsernameAndCodeAndType(username, queryCode, permissionType);
            if (existing == null) {
                TQueryCodePermission permission = new TQueryCodePermission(userId, username, queryCode, permissionType);
                permission.setCreateBy(currentUser);
                permissions.add(permission);
            }
        }

        if (!permissions.isEmpty()) {
            queryCodePermissionMapper.batchInsert(permissions);

            // 清除相关缓存
            clearUserPermissionCache(username);
            for (String queryCode : queryCodes) {
                clearQueryCodePermissionCache(queryCode);
            }

            log.info("成功批量授予用户 [{}] {} 个查询编码的 [{}] 权限", username, permissions.size(), permissionType);
        } else {
            log.info("用户 [{}] 已拥有所有指定查询编码的权限", username);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchRevokeUserPermissions(String username, List<String> queryCodes) {
        if (!StringUtils.hasText(username) || CollectionUtils.isEmpty(queryCodes)) {
            throw new IllegalArgumentException("用户名和查询编码列表不能为空");
        }

        int deletedCount = queryCodePermissionMapper.batchDeleteByUsernameAndCodes(username, queryCodes);

        if (deletedCount > 0) {
            // 清除相关缓存
            clearUserPermissionCache(username);
            for (String queryCode : queryCodes) {
                clearQueryCodePermissionCache(queryCode);
            }

            log.info("成功批量撤销用户 [{}] {} 个查询编码的权限，删除 {} 条记录", username, queryCodes.size(), deletedCount);
        } else {
            log.info("用户 [{}] 没有指定查询编码的权限，无需撤销", username);
        }
    }

    // ==================== 角色权限管理方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void grantRolePermission(String roleKey, String queryCode, String permissionType) {
        if (!StringUtils.hasText(roleKey) || !StringUtils.hasText(queryCode)) {
            throw new IllegalArgumentException("角色标识和查询编码不能为空");
        }

        // 检查是否已存在相同权限
        TRoleQueryCodePermission existingPermission = roleQueryCodePermissionMapper
                .selectByRoleKeyAndCodeAndType(roleKey, queryCode, permissionType);
        if (existingPermission != null) {
            log.info("角色 [{}] 已拥有查询编码 [{}] 的 [{}] 权限", roleKey, queryCode, permissionType);
            return;
        }

        // 创建权限记录
        TRoleQueryCodePermission permission = new TRoleQueryCodePermission("1", roleKey, queryCode, permissionType);
        permission.setCreateBy(SecurityUtils.getUserName());

        roleQueryCodePermissionMapper.insert(permission);

        // 清除相关缓存（角色权限变更会影响所有拥有该角色的用户）
        clearQueryCodePermissionCache(queryCode);

        log.info("成功授予角色 [{}] 查询编码 [{}] 的 [{}] 权限", roleKey, queryCode, permissionType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeRolePermission(String roleKey, String queryCode) {
        if (!StringUtils.hasText(roleKey) || !StringUtils.hasText(queryCode)) {
            throw new IllegalArgumentException("角色标识和查询编码不能为空");
        }

        int deletedCount = roleQueryCodePermissionMapper.deleteByRoleKeyAndCode(roleKey, queryCode);

        if (deletedCount > 0) {
            // 清除相关缓存
            clearQueryCodePermissionCache(queryCode);

            log.info("成功撤销角色 [{}] 查询编码 [{}] 的权限，删除 {} 条记录", roleKey, queryCode, deletedCount);
        } else {
            log.info("角色 [{}] 没有查询编码 [{}] 的权限，无需撤销", roleKey, queryCode);
        }
    }

    @Override
    public List<TRoleQueryCodePermission> getRolePermissions(String roleKey) {
        if (!StringUtils.hasText(roleKey)) {
            throw new IllegalArgumentException("角色标识不能为空");
        }

        return roleQueryCodePermissionMapper.selectByRoleKey(roleKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchGrantRolePermissions(String roleKey, List<String> queryCodes, String permissionType) {
        if (!StringUtils.hasText(roleKey) || CollectionUtils.isEmpty(queryCodes)) {
            throw new IllegalArgumentException("角色标识和查询编码列表不能为空");
        }

        String currentUser = SecurityUtils.getUserName();

        List<TRoleQueryCodePermission> permissions = new ArrayList<>();
        for (String queryCode : queryCodes) {
            // 检查是否已存在
            TRoleQueryCodePermission existing = roleQueryCodePermissionMapper
                    .selectByRoleKeyAndCodeAndType(roleKey, queryCode, permissionType);
            if (existing == null) {
                TRoleQueryCodePermission permission = new TRoleQueryCodePermission("1", roleKey, queryCode, permissionType);
                permission.setCreateBy(currentUser);
                permissions.add(permission);
            }
        }

        if (!permissions.isEmpty()) {
            roleQueryCodePermissionMapper.batchInsert(permissions);

            // 清除相关缓存
            for (String queryCode : queryCodes) {
                clearQueryCodePermissionCache(queryCode);
            }

            log.info("成功批量授予角色 [{}] {} 个查询编码的 [{}] 权限", roleKey, permissions.size(), permissionType);
        } else {
            log.info("角色 [{}] 已拥有所有指定查询编码的权限", roleKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchRevokeRolePermissions(String roleKey, List<String> queryCodes) {
        if (!StringUtils.hasText(roleKey) || CollectionUtils.isEmpty(queryCodes)) {
            throw new IllegalArgumentException("角色标识和查询编码列表不能为空");
        }

        int deletedCount = roleQueryCodePermissionMapper.batchDeleteByRoleKeyAndCodes(roleKey, queryCodes);

        if (deletedCount > 0) {
            // 清除相关缓存
            for (String queryCode : queryCodes) {
                clearQueryCodePermissionCache(queryCode);
            }

            log.info("成功批量撤销角色 [{}] {} 个查询编码的权限，删除 {} 条记录", roleKey, queryCodes.size(), deletedCount);
        } else {
            log.info("角色 [{}] 没有指定查询编码的权限，无需撤销", roleKey);
        }
    }

    // ==================== 查询方法实现 ====================

    @Override
    public List<String> getUserAccessibleCodes(String username) {
        if (!StringUtils.hasText(username)) {
            throw new IllegalArgumentException("用户名不能为空");
        }

        List<String> accessibleCodes = new ArrayList<>();

        // 获取用户直接权限的查询编码
        List<String> userCodes = queryCodePermissionMapper
                .selectAccessibleCodesByUsername(username, TQueryCodePermission.PermissionType.READ);
        if (!CollectionUtils.isEmpty(userCodes)) {
            accessibleCodes.addAll(userCodes);
        }

        // 获取用户角色权限的查询编码
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && username.equals(loginUser.getUserName())) {
                Set<String> userRoles = loginUser.getRoles();
                if (!CollectionUtils.isEmpty(userRoles)) {
                    for (String roleKey : userRoles) {
                        List<String> roleCodes = roleQueryCodePermissionMapper
                                .selectAccessibleCodesByRoleKey(roleKey, TRoleQueryCodePermission.PermissionType.READ);
                        if (!CollectionUtils.isEmpty(roleCodes)) {
                            accessibleCodes.addAll(roleCodes);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取用户 [{}] 角色权限时发生异常", username, e);
        }

        // 去重并返回
        return accessibleCodes.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getUsersWithCodeAccess(String queryCode) {
        if (!StringUtils.hasText(queryCode)) {
            throw new IllegalArgumentException("查询编码不能为空");
        }

        List<TQueryCodePermission> permissions = queryCodePermissionMapper.selectByQueryCode(queryCode);
        return permissions.stream()
                .map(TQueryCodePermission::getUsername)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getRolesWithCodeAccess(String queryCode) {
        if (!StringUtils.hasText(queryCode)) {
            throw new IllegalArgumentException("查询编码不能为空");
        }

        List<TRoleQueryCodePermission> permissions = roleQueryCodePermissionMapper.selectByQueryCode(queryCode);
        return permissions.stream()
                .map(TRoleQueryCodePermission::getRoleKey)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据用户名获取用户ID
     */
    private String getUserIdByUsername(String username) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && username.equals(loginUser.getUserName())) {
                return loginUser.getUserId().toString();
            }
            // 如果不是当前用户，可以通过其他方式获取用户ID
            // 这里简化处理，实际项目中可能需要查询用户表
            return username; // 临时使用用户名作为用户ID
        } catch (Exception e) {
            log.warn("获取用户 [{}] 的用户ID失败，使用用户名作为ID", username);
            return username;
        }
    }
}
