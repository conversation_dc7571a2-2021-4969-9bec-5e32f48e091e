package cn.cmcc.system.api.service;

import cn.cmcc.system.api.pojo.dto.SysLogininfoDto;
import cn.cmcc.system.api.pojo.dto.SysOperLogDto;

/**
 * 日志服务
 * 
 * <AUTHOR>
 */
public interface RemoteLogService
{	
    /**
     * 保存系统日志
     *
     * @param sysOperLog 日志实体
     * @param source 请求来源
     * @return 结果
     */
    public Boolean saveLog(SysOperLogDto sysOperLog);

    /**
     * 保存访问记录
     *
     * @param sysLogininfor 访问实体
     * @param source 请求来源
     * @return 结果
     */
    public Boolean saveLogininfor(SysLogininfoDto sysLogininfor);
    
    /**
     * 获取密码连续识别次数
     * 
     * @param account
     * @return 
     * Integer
     */
    public Integer getPasswdErrorCount(String account);
}
