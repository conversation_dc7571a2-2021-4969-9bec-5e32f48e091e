/**
 *
 *
 * @Title: SysOperLogDto.java
 * <AUTHOR>
 * @date 2022年9月19日
 * @version V1.0
 */
package cn.cmcc.system.api.pojo.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class SysOperLogDto implements Serializable{

    /**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 日志类型(字典)
     */
    private String logType;

    /**
     * 日志名称
     */
    private String logName;

    /**
     * 操作者
     */
    private Long userId;

    /**
     * 操作者
     */
    private String userName;

    /**
     * 方法名称
     */
    private String method;

    /**
     * 请求地址
     * */
    private String operUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否成功(字典)
     */
    private String succeed;

    /**
     * 备注
     */
    private String message;

    /**
     * 操作IP
     */
    private String operIp;

    /**
     * 请求参数
     */
    private String operParam;

    /**
     * 返回参数
     */
    private String operResult;
}
