/**
 *
 *
 * @Title: RedisPublishMessage.java
 * <AUTHOR>
 * @date 2022年10月8日
 * @version V1.0
 */
package cn.cmcc.common.cache.listener;

import java.io.Serializable;

import cn.cmcc.common.config.NaasConfig;
import lombok.Data;

@Data
public class RedisPublishMessage implements Serializable{

	/**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 4078196612240194131L;

	/**
	 * 发布消息的服务
	 */
	private String publishServer;

	private String softVersion;

	/**
	 * 发布消息
	 */
	private Object data;

	public RedisPublishMessage(){
		this.publishServer = NaasConfig.SERVER_ID;
		this.softVersion = NaasConfig.getVersion();
	}

	public RedisPublishMessage(Object data){
		this.publishServer = NaasConfig.SERVER_ID;
		this.softVersion = NaasConfig.getVersion();
		this.data = data;
	}
}
