package cn.cmcc.common.utils.ftp;

import java.io.File;
import java.util.List;

import cn.cmcc.common.utils.file.FileUtils;

/**
 * Ftp接口
 * 
 * @version v1.0
 * <AUTHOR>
 * @date 2023年6月30日下午3:55:07
 */
public interface Ftp {

	/**
	 * 登录
	 * @return
	 * @version v1.0
	 * @date 2023年6月30日下午3:55:17
	 */
	public boolean ftpLogin();
	
	/**
	 * 退出
	 * 
	 * @version v1.0
	 * @date 2023年6月30日下午3:57:59
	 */
	public void ftpLogOut();
	
	/**
	 * 下载文件
	 * 
	 * @param remoteFileName
	 * @param localFilePath
	 * @param remoteFilePath
	 * @return
	 * @version v1.0
	 * @date 2023年6月30日下午3:55:24
	 */
	public List<String> downloadFile(String remoteFileName,String localFilePath, String remoteFilePath);
	
	/**
	 * 下载目录
	 * 
	 * @param localDirectoryPath
	 * @param remoteDirectory
	 * @param filter
	 * @return
	 * @version v1.0
	 * @date 2023年6月30日下午4:02:03
	 */
	public List<String> downLoadDirectory(String localDirectoryPath, String remoteDirectory,final FileNameFilter filter);
	
	/**
	 * 下载目录
	 * 
	 * @param localDirectoryPath
	 * @param remoteDirectory
	 * @return
	 * @version v1.0
	 * @date 2023年6月30日下午4:02:03
	 */
	public List<String> downLoadDirectory(String localDirectoryPath, String remoteDirectory);
	
	/**
	 * 上传单个文件
	 * 
	 * @param directory      ：远程下载目录(以路径符号结束)
	 * @param uploadFilePath 要上传的文件 如：D:\\test\\xxx.txt
	 * @param fileName       FTP服务器文件名称 如：xxx.txt ||xxx.txt.zip
	 * @throws Exception
	 */
	public boolean uploadFile(String directory, String uploadFilePath, String fileName);
	
	/**
	 * 上传单个文件
	 * 
	 * @param localFile
	 * @param directory
	 * @return
	 * @version v1.0
	 * @date 2023年7月3日下午6:08:00
	 */
	public boolean uploadFile(File localFile, String directory);
	
	/**
	 * 创建目录
	 * 
	 * @param localFilePath
	 */
	default void createDirectory(String localFilePath) {
		File localFile = FileUtils.getFile(localFilePath);
        if (!localFile.getParentFile().exists()){
            localFile.getParentFile().mkdirs();
        }
		
		if (localFile.exists() && localFile.delete()) {
			System.out.println("文件：" + localFile + "已经存在，先删除！");
		}
	}
	
	/**
	 * 过滤器接口
	 *
	 * <AUTHOR>
	 */
	@FunctionalInterface
	public static interface FileNameFilter{
		/**
		 * 是否接受对象
		 *
		 * @param fileName 文件名
		 * @return 是否接受对象
		 */
		boolean accept(String fileName);
	}
}
