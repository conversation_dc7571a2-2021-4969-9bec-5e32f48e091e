/**
 * @Title: FieldTypeValueBO.java
 * @Package cn.cmcc.base.pojo.bo
 * 
 * <AUTHOR>
 * @date 2022年5月31日
 * @version V1.0
 */
package cn.cmcc.common.datasource.pojo.bo;

import org.apache.commons.lang3.ObjectUtils;

import lombok.Data;

@Data
public class FieldTypeValueBO {

	/**
     * 字段类型
     */
	private String dataType;
	
    /**
     * 值
     */
    private Object fieldValue;
    
    public FieldTypeValueBO() {
    	
    }
    
    public FieldTypeValueBO(String dataType,Object fieldValue) {
    	this.dataType = dataType;
    	if(ObjectUtils.isEmpty(fieldValue)) {
    		this.fieldValue = null;
    	}else {
        	this.fieldValue = fieldValue;
    	}
    }
}
