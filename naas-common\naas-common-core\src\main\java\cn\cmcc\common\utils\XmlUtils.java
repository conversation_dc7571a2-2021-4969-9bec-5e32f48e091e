package cn.cmcc.common.utils;

import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;

import org.apache.commons.lang3.StringEscapeUtils;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.JDOMException;
import org.jdom2.input.SAXBuilder;
import org.jdom2.output.Format;
import org.jdom2.output.XMLOutputter;

import cn.cmcc.common.utils.text.CharsetKit;
import lombok.extern.slf4j.Slf4j;


/**  
 * All rights Reserved, Designed By www.nokia.com
 * @Title:  XmlUtils.java   
 * @Package com.cmcc.ict_bi.util   
 * @Description:    TODO(用一句话描述该文件做什么)   
 * @author: liyy
 * @date:   2020年6月10日 下午2:58:55   
 * @version V1.0 
 */

@Slf4j
public class XmlUtils {

	 /**
     * <P>map集合转化成xml字符串</P>
     * @param map
     * @param rootName
     * @return
     */
    public static String mapToXml(Map<String, Object> map, String rootName)
    {
    	StringBuffer sBuffer = new StringBuffer();
    	sBuffer.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
    	sBuffer.append("<" +  rootName + ">");
    	mapToXML(map,sBuffer);
    	sBuffer.append("</" +  rootName + ">");
    	return sBuffer.toString();
    }
    
    /**
     * 
     * TODO(描述这个方法的作用)   
     *
     * @Title: mapToXML   
     * @param map
     * @param sb void
     * @throws
     */
    private static void mapToXML(Map<?, ?> map, StringBuffer sb) {
        Set<?> set = map.keySet();
        for (Iterator<?> it = set.iterator(); it.hasNext();) {
            String key = (String) it.next();
            Object value = map.get(key);
            if (value instanceof Map) {
                sb.append("<" + key + ">");
                mapToXML((Map<?, ?>) value, sb);
                sb.append("</" + key + ">");
            } else if (value instanceof List) {
                List<?> list = (List<?>) map.get(key);
                for (int i = 0; i < list.size(); i++) {
                    sb.append("<" + key + ">");
                    Map<?, ?> hm = (Map<?, ?>) list.get(i);
                    mapToXML(hm, sb);
                    sb.append("</" + key + ">");
                }
            } else {
                sb.append("<" + key + ">" + (value == null ? StringUtils.EMPTY : value)  + "</" + key + ">");
            }
        }
    }

    /**
     * <P>xml字符串转化成map集合</P>
     * @param xmlStr 字符串
     * @return
     */
    public static Map<String, Object> xmlToMap(String xmlStr)
    {

        SAXBuilder builder = new SAXBuilder();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            xmlStr = URLDecoder.decode(xmlStr, CharsetKit.UTF_8);
            Reader in = new StringReader(xmlStr);
            Document doc = builder.build(in);
            Element root = doc.getRootElement();

            List<Element> list = root.getChildren();
            for (Element element : list) {
                List<Element> childList = element.getChildren();
                if (childList.size() > 0) {
                    map.put(element.getName(), element2Map(element));
                }else {
                    map.put(element.getName(), element.getText());
                }
            }

            return map;
        } catch (JDOMException e) {
        	log.error(e.getMessage(),e);
        } catch (UnsupportedEncodingException e) {
        	log.error(e.getMessage(),e);
        } catch (IOException e) {
        	log.error(e.getMessage(),e);
        } catch (Exception e) {
        	log.error(e.getMessage(),e);
        }
        return map;
    }

    /**
     * <P>xml字符串转化成map集合</P>
     * @param xmlStr 字符串
     * @return
     */
    public static Map<String, Object> xmlToMaps(String xmlStr) {
        SAXBuilder builder = new SAXBuilder();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            xmlStr = URLDecoder.decode(xmlStr, CharsetKit.UTF_8);
            Reader in = new StringReader(xmlStr);
            Document doc = builder.build(in);
            Element root = doc.getRootElement();

            List<Element> list = root.getChildren();
            for (Element element : list) {
                List<Element> childList = element.getChildren();
                String key=element.getName();
                if (childList.size() > 0) {
                    if(map.containsKey(key)){
                        if(!(map.get(key) instanceof List)){
                            Object value=map.get(key);
                            map.put(key,new ArrayList<>());
                            ((List)map.get(key)).add(value);
                        }
                        ((List)map.get(key)).add(element2Map(element));
                    }else{
                        map.put(key, element2Map(element));
                    }
                }else {
                    map.put(key, element.getText());
                }
            }

            return map;
        } catch (JDOMException e) {
        	log.error(e.getMessage(),e);
        } catch (UnsupportedEncodingException e) {
        	log.error(e.getMessage(),e);
        } catch (IOException e) {
        	log.error(e.getMessage(),e);
        } catch (Exception e) {
        	log.error(e.getMessage(),e);
        }
        return map;

    }
    
    private static Map<String, Object> element2Map(Element e) {
    	Map<String, Object> map = new HashMap<String, Object>();
    	List<Element> list = e.getChildren();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                Element iter = (Element) list.get(i);
                List<Object> mapList = new ArrayList<Object>();

                if (iter.getChildren().size() > 0) {
                    Map<String, Object> m = element2Map(iter);
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if( obj instanceof ArrayList ) 
                        if (!(obj instanceof ArrayList)) {
                            mapList = new ArrayList<Object>();
                            mapList.add(obj);
                            mapList.add(m);
                        }
                        if (obj instanceof ArrayList) {
                            mapList = (List) obj;
                            mapList.add(m);
                        }
                        map.put(iter.getName(), mapList);
                    } else
                        map.put(iter.getName(), m);
                } else {
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof ArrayList)) {
                            mapList = new ArrayList<>();
                            mapList.add(obj);
                            mapList.add(iter.getText());
                        }
                        if (obj instanceof ArrayList) {
                            mapList = (List) obj;
                            mapList.add(iter.getText());
                        }
                        map.put(iter.getName(), mapList);
                    } else
                        map.put(iter.getName(), iter.getText());
                }
            }
        } else {
            map.put(e.getName(), e.getText());
        }
        
        return map;

    }


    /**
     * <P>list集合转化成xml字符串</P>
     * @param list
     * @param rootName
     * @param parentName
     * @return
     */
    public static String listToXml(List<Map<String, Object>> list,
                                   String rootName, String parentName)
    {
        Element root = new Element(rootName);
        Element parentElement = null;
        Element child = null;
        if (list == null)
            return xmlToString(root);
        for (Map<String, Object> map : list) {
                parentElement = new Element(parentName);
                root.addContent(parentElement);
                for (String str : map.keySet()) {
                    String value=map.get(str) == null ? "": (map.get(str) + "");
                    value=wrapXmlContent(value);
                    child = new Element(str).setText(value);
                    parentElement.addContent(child);
                }
        }
        return xmlToString(root);
    }


    /**
     * <P>xml字符串转化成list集合</P>
     * @param xmlStr
     * @return
     */
    public static List<Map<String, Object>> xmlToList(String xmlStr)
    {
        SAXBuilder builder = new SAXBuilder();
        List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
        Map<String, Object> map = null;
        boolean flag = true;
        try {
            xmlStr = URLDecoder.decode(xmlStr, CharsetKit.UTF_8);
            Reader in = new StringReader(xmlStr);
            Document doc = builder.build(in);
            Element root = doc.getRootElement();
            List<Element> list = root.getChildren();
            for (Element e : list) {
                if (e.getChildren().size() == 0) {
                    if (flag) {
                        flag = false;
                        map = new HashMap<String, Object>();
                        resultList.add(map);
                    }
                    map.put(e.getName(), e.getText());
                } else {
                    map = new HashMap<String, Object>();
                    List<Element> childrenList = e.getChildren();
                    resultList.add(map);
                    for (Element element : childrenList) {
                        map.put(element.getName(), element.getText());
                    }
                }
            }
            return resultList;
        } catch (JDOMException e) {
        	log.error(e.getMessage(),e);
        } catch (UnsupportedEncodingException e) {
        	log.error(e.getMessage(),e);
        } catch (IOException e) {
        	log.error(e.getMessage(),e);
        } catch (Exception e) {
        	log.error(e.getMessage(),e);
        }
        return resultList;
    }
    /**
     * <AUTHOR>
     * 描述：
     * 字符转意
     */
    public static String wrapXmlContent(String content)
    {
        StringBuffer appender = new StringBuffer("");

        if ((content != null) && (!content.trim().isEmpty())) {
            appender = new StringBuffer(content.length());

            for (int i = 0; i < content.length(); i++) {
                char ch = content.charAt(i);
                if ((ch == '\t') || (ch == '\n') || (ch == '\r') ||
                        ((ch >= ' ') && (ch <= 55295)) ||
                        ((ch >= 57344) && (ch <= 65533)) || (
                        (ch >= 65536) && (ch <= 1114111))) {
                    appender.append(ch);
                }
            }
        }
        String result = appender.toString();

        return result;
    }
    /**
     * 将Element对象转化成字串
     * @param element
     * @return
     */
    public static String xmlToString(Element element)
    {
        XMLOutputter output = new XMLOutputter();
        output.setFormat(Format.getPrettyFormat().setEncoding(CharsetKit.UTF_8));
        Document doc = new Document(element);
        String str = output.outputString(doc);
        return str;
    }


    /**
     * 解析xml字符串获取节点内容
     * @param xmlStr
     *            xml字符串
     * @param nodeStr
     *            需要获取的节点
     * @return 解析信息
     */
    public static Map<String, String> readXml(String xmlStr, String[] nodeStrArr)
    {
        Map<String, String> map = new HashMap<String, String>();
        SAXBuilder builder = new SAXBuilder();
        try {
            xmlStr = URLDecoder.decode(xmlStr, CharsetKit.UTF_8);
            Reader in = new StringReader(xmlStr);
            Document doc = builder.build(in);
            Element root = doc.getRootElement();
            List<Element> l = null;
            String str = "";
            for (String nodeStr : nodeStrArr) {
                l = root.getChildren(nodeStr);
                str = l.get(0).getText();
                if ("".equals(str) || str == null) {
                    map.put("msg", "01: 传入参数为空或格式错误，请检查各节点是否为有效XML节点");
                    return map;
                } else {
                    map.put("msg", "00: 解析xml成功");
                    map.put(nodeStr, str);
                }
            }
        } catch (JDOMException e) {
            map.put("msg", "01: 传入参数为空或格式错误，请检查各节点是否为有效XML节点");
            return map;
        } catch (UnsupportedEncodingException e) {
            map.put("msg", "05：编码格式错误,不支持的字符编码");
            return map;
        } catch (IOException e) {
            map.put("msg", "02: 读取失败");
            return map;
        } catch (Exception e) {
            map.put("msg", "02: 读取失败");
            return map;
        }
        return map;
    }



    /**
     * 生成带内容的节点
     * @param parentElement父节点
     * @param map
     *            数据集
     * @return
     */
    public static Element createNodes(Element parentElement,
                                      Map<String, Object> map)
    {
        String msg = "";
        Iterator<String> it = map.keySet().iterator();
        String tempStr = "";
        Element sonElement = null;
        while (it.hasNext()) {
            tempStr = it.next();
            msg = (map.get(tempStr)) == null ? "" : (map.get(tempStr) + "");
            sonElement = new Element(tempStr);
            parentElement.addContent(sonElement.setText(msg));
        }
        return parentElement;
    }

    /**
     * 生成不带内容的节点
     * @param root根节点
     * @param strArr
     *            节点字符
     */
    public static void createNodes(Element root, String[] strArr)
    {
        Element e = null;
        for (String str : strArr) {
            e = new Element(str);
            root.addContent(e);
        }

    }
    
    /**
     * 
     * @Title: ObjectToXmlStr   
     * @Description: java bean 转xml
     * @param: @param obj
     * @param: @return      
     * @return: String      
     * @throws
     */
    public static String objectToXmlStr(Object obj) {
    	JAXBContext context;  
        try {  
            context = JAXBContext.newInstance(obj.getClass());  
            Marshaller mar = context.createMarshaller();  
            mar.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);  
            mar.setProperty(Marshaller.JAXB_ENCODING, CharsetKit.UTF_8);  
            mar.setProperty("com.sun.xml.bind.xmlDeclaration", Boolean.FALSE);
              
            StringWriter writer = new StringWriter();  
              
            mar.marshal(obj, writer);  
              
            return writer.toString();  
        } catch (JAXBException e) {  
        	log.error(e.getMessage(),e);
            return null;
        }
    }
    
    /**
     * 
     * @Title: ObjectToXmlStr   
     * @Description: xml 转  java bean
     * @param: @param obj
     * @param: @return      
     * @return: String      
     * @throws JAXBException 
     * @throws
     */
    @SuppressWarnings("unchecked")
	public static <T> T xmlStrToObject(String xmlStr,Class<T> cls) throws JAXBException {
    	JAXBContext context;
    	T t = null;
        context = JAXBContext.newInstance(cls);  
        Unmarshaller  uma = context.createUnmarshaller();  
        //uma.setProperty(Unmarshaller.JAXB_ENCODING, CharsetKit.UTF8);
        t = (T)  uma.unmarshal(new StringReader(xmlStr.replace("&","&amp;")));
        return t;
    }

}
