package cn.cmcc.common.constant;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants
{
	public static final String BUSINESS_SYS_NAME = "覆盖端到端分析支撑平台";

	public static final String BUSINESS_SYS_CODE = "NAAS";

	/**
     * 文件分隔符
     */
	public static final String FILE_SEPARATOR = "/";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";
    
    /**
     * 重定向
     */
    public static final String REDIRECT = "redirect:%s";
    
    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/attement";

    /**
     * 导入路径
     */
    public static final String IMPORT_PATH_PREFIX = "/importData/";

    /**
     * 导出路径
     */
    public static final String EXPORT_PATH_PREFIX = "/exportData/";

	/**非导入，非导出，只是生成文件提供给第三方*/
	public static final String GENERATE_PATH_PREFIX = "/generateData/";

    /**
     * 通用查询前缀
     * */
    public static final String COMMON_QUERY_PRX = "cn.cmcc.dynamic.db.qry.";

    /**
     * 通用查询count前缀
     * */
    public static final String COMMON_QUERY_COUNT_PRX = "cn.cmcc.dynamic.db.qry.count.";

    /**
     * 私钥
     */
    public static final String PRIVAGE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMVSULwG+3NPj1nQ" +
    		"FckcR2akPM1nuF4DHnIO8L1dQOC3H0eW/mBczZ1aeUJCSGKAf41cGfmP8Bd2ikLs" +
    		"iCkn+ONhFmBb54I6jMfN/TRDtipRs/bxIai7o9VDjV0EsVDw8L8rHSvVh/f9PLZH" +
    		"PBBkIVYqXnJPiPsaSk0AfArqSiTdAgMBAAECgYEAk4Ka6kWJ+sUSVEDz2Fl0oy3K" +
    		"an/MWzOXsgqrO9UX6CXdk2Y0mhMmfyigl8D3XfPT5vhgTqmlAdVTPP6YN7QzIW7B" +
    		"muk5rmjf65vCKj5uIIr/Jg0Vnm8EU3f4T3gxkIEfiVVAelN651e60obPI+r2UNEJ" +
    		"0em3x2vSDDTqxZjEcIECQQD4Q/3zr8Iwk/6BbjAirgDz1G9hGwfoPATgrrmWSInO" +
    		"DvgfJpOnJqigBYCP/y1A8w7CU6q62SVHIs8xM4nbeUvhAkEAy3gGsH16RMkNH9JF" +
    		"J2ufxRgWVfL5MRGzF4yO32DzuY6AJDGUQdp2mNxiKIh8xkZc+sTFq7innBVCrQbU" +
    		"p1MYfQJAYCXM4yRNW4zlQvEOmU+0NLowDHZwgINGDFH+s/IR1Ii9lSrWgiSZp6eZ" +
    		"26qitwWq0J2ggE56fms/vLlOCX6XYQJAAQMZ1WrqXsk5PX1IRsfsRYi3I7pPrwTN" +
    		"hUJ3u3i5/aRN0uBEQoeZq2z9OAJlzH6xG7Y63X3Pp6Ebcczk5egt6QJAG359z8sx" +
    		"HYAEoUcZRGWv+R+S69BSJDE78rIojuenOUvm5h1iilZDpIuWNrfzemTzZZcztppq" +
    		"Fb5n3ALzovS+cg==";


    public final static String UPGRADE_UPLOAD_FILE = "/upgrade/";

    public final static String UPGRADE_SALT = "kmxhxk";

    public final static String UPGRADE_PMD5 = "f3f17122bfecfcfbe15227e0f45d84d6";

    public final static String DB_OPERATOR_USER = "xhxk";

	public final static String DB_OPERATOR_USERSYSTEM = "system";

    public final static String DB_OPERATOR_PMD5 = "4bc44b7d43dac24549f4f65184c5289d";

	public final static String DB_OPERATOR_PMD52025 = "279993e493978c67cf6f61286499341c";//Qi8$yhTV!13
	//数据库SQL语句解密密钥
	public final static String DB_AES_KEY = "21232f297a57a5a743894a0e4a801fc3";

    /**
     * 外部系统
     */
	public final static List<String> EXCLUDE_SYSTEM_LIST = Stream.of("app","TXCL").collect(Collectors.toList());

    /**
     * 未找到字典、值为空、值格式错误模板
     */
    public static final String NOT_FOUND_DICT_TEMPLATE = "未找到字典【%s】。",
            VALUE_EMPTY_TEMPLATE = "【%s】值为空。",
            VALUE_FORMAT_ERR_TEMPLATE = "【%s】值格式错误。";

    public static final String AES_KEY = "xhxk@5f98H*^cmcc%dfs$r344&df8543*er";

    /**
     * 随机数
     */
    public static final Random rand = new SecureRandom();

    /**
     * WEBSERVICE认证用户名密码
     */
//    public static final String WEBSERVICE_USERNAME = "twes", WEBSERVICE_PASSWORD = "i8aj28Aljn2iayneidNshyw";
    
    /**
     * image
     */
    public static final String IMAGE_CONTENT_TYPE = "image/jpeg, image/jpg, image/png, image/gif";
    
    /**
     * TRACE_ID
     */
    public static final String TRACE_ID = "TRACE_ID";
    
    /**
     * 省份
     */
    public final static Map<String, String> provinceCodeMap = new HashMap<String, String>() {
		private static final long serialVersionUID = 1L;

		{
			put("安徽", "AH");
			put("北京", "BJ");
			put("重庆", "CQ");
			put("福建", "FJ");
			put("广东", "GD");
			put("甘肃", "GS");
			put("广西", "GX");
			put("贵州", "GZ");
			put("河南", "HA");
			put("湖北", "HB");
			put("河北", "HE");
			put("海南", "HI");
			put("黑龙江", "HL");
			put("湖南", "HN");
			put("吉林", "JL");
			put("江苏", "JS");
			put("江西", "JX");
			put("辽宁", "LN");
			put("内蒙", "NM");
			put("宁夏", "NX");
			put("青海", "QH");
			put("四川", "SC");
			put("山东", "SD");
			put("上海", "SH");
			put("陕西", "SN");
			put("山西", "SX");
			put("天津", "TJ");
			put("新疆", "XJ");
			put("西藏", "XZ");
			put("云南", "YN");
			put("浙江", "ZJ");

		}
	};

	public interface Common{
		String REDIRECT = "redirect";
		String UNKNOWN = "unknown";		
	}
}
