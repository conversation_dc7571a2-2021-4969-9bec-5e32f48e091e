package cn.cmcc.cad.cuting.entity;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadBlockEntity;
import cn.cmcc.cad.entity.XhxkCadCircle;
import cn.cmcc.cad.entity.XhxkCadLine;
import cn.cmcc.cad.entity.XhxkCadLwPolyline;
import cn.cmcc.cad.entity.XhxkCadPoint;
import lombok.Data;

/**
 * 自动切割图纸参数
 * 
 * <AUTHOR>
 *
 */
@Data
public class CadCutingConfig {
	private double DEFAULT_MIN = -99999999999.0;
	private double DEFAULT_MAX = 99999999999.0;
	private String id;
	private String name;
	private List<XhxkCadBaseEntity> entities;
	private double xMin;
	private double yMin;
	private double xMax;
	private double yMax;
	private String type;//匹配到的图框
	private String typeValue;//匹配到的值
	private String title;//关键字有“平面图”
	private XhxkCadPoint explainPoint;//说明位置 ，一般图纸右下角都有说明文字，如果存在，则说明以下区域一般为非建筑物数据，可以删除
	
	private List<XhxkCadBaseEntity> cadDevices;//器件
	private List<XhxkCadBaseEntity> cadBaseEntities;//全部对象
	private int rectangleSize;
	private int geomSize;
	
	private String xhxkFile;//切割后保存路径

	/**
	 * 模型数据
	 * 
	 * @param name
	 * @param entity
	 */
	public CadCutingConfig(String name, XhxkCadBlockEntity entity) {
		super();
		this.name = name;
		this.entities = entity.getBlockEntities();
		this.init();
	}

	/**
	 * 模型对应图形数据
	 * 
	 * @param name
	 * @param xMin
	 * @param yMin
	 * @param xMax
	 * @param yMax
	 */
	public CadCutingConfig(String name, double xMin, double yMin, double xMax, double yMax) {
		super();
		this.name = name;
		this.xMin = xMin;
		this.yMin = yMin;
		this.xMax = xMax;
		this.yMax = yMax;
	}

	private void init() {
		// 计算边界，只统计CadLine、CadLwPolyline对象，后期如果出现不准，再添加其他对象的解析

		if ((entities != null) && (entities.size() > 0)) {
			this.xMin = DEFAULT_MAX;
			this.yMin = DEFAULT_MAX;
			this.xMax = DEFAULT_MIN;
			this.yMax = DEFAULT_MIN;
			for (XhxkCadBaseEntity ent : entities) {
				String className = ent.getClass().getSimpleName();
				// 判断类型不用instanceof，父子类逻辑会出错
				if (className.equals("XhxkCadCircle")) {
					XhxkCadCircle circle = (XhxkCadCircle) ent;
					XhxkCadPoint centerPoint = circle.getCenterPoint();
					double radius = circle.getRadius();
					if ((centerPoint.getX() - radius) < this.xMin) {
						this.xMin = centerPoint.getX() - radius;
					}
					if ((centerPoint.getY() - radius) < this.yMin) {
						this.yMin = centerPoint.getY() - radius;
					}
					if ((centerPoint.getX() + radius) > this.xMax) {
						this.xMax = centerPoint.getX() + radius;
					}
					if ((centerPoint.getY() + radius) > this.yMax) {
						this.yMax = centerPoint.getY() + radius;
					}
				} else if (ent instanceof XhxkCadLine) {
					XhxkCadLine line = (XhxkCadLine) ent;
					XhxkCadPoint firstPoint = line.getFirstPoint();
					if (firstPoint.getX() < this.xMin) {
						this.xMin = firstPoint.getX();
					}
					if (firstPoint.getY() < this.yMin) {
						this.yMin = firstPoint.getY();
					}
					if (firstPoint.getX() > this.xMax) {
						this.xMax = firstPoint.getX();
					}
					if (firstPoint.getY() > this.yMax) {
						this.yMax = firstPoint.getY();
					}
					XhxkCadPoint secondPoint = line.getSecondPoint();
					if (secondPoint.getX() < this.xMin) {
						this.xMin = secondPoint.getX();
					}
					if (secondPoint.getY() < this.yMin) {
						this.yMin = secondPoint.getY();
					}
					if (secondPoint.getX() > this.xMax) {
						this.xMax = secondPoint.getX();
					}
					if (secondPoint.getY() > this.yMax) {
						this.yMax = secondPoint.getY();
					}
				} else if (ent instanceof XhxkCadLwPolyline) {// CadLwPolyline
					XhxkCadLwPolyline cad = (XhxkCadLwPolyline) ent;
					List<XhxkCadPoint> list = cad.getCoordinates();
					for (XhxkCadPoint point : list) {
						if (point.getX() < this.xMin) {
							this.xMin = point.getX();
						}
						if (point.getY() < this.yMin) {
							this.yMin = point.getY();
						}
						if (point.getX() > this.xMax) {
							this.xMax = point.getX();
						}
						if (point.getY() > this.yMax) {
							this.yMax = point.getY();
						}
					}
				}
			}
		}
		if (DEFAULT_MIN == xMax || DEFAULT_MIN == yMax || DEFAULT_MAX == xMin || DEFAULT_MAX == yMin) {
			this.xMin = 0;
			this.yMin = 0;
			this.xMax = 10;
			this.yMax = 10;
		}
	}

	
	public void update() {
		for(XhxkCadBaseEntity entity:cadBaseEntities) {
			String text = entity.getText();
			if(StringUtils.isNotBlank(text)) {
				if(text.startsWith("说明")) {
					this.explainPoint = entity.getPosition();
				}
			}
			
		}
	}
}
