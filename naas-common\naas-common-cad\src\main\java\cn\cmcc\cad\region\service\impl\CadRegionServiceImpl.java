package cn.cmcc.cad.region.service.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aspose.cad.fileformats.cad.CadBlockDictionary;
import com.aspose.cad.fileformats.cad.CadImage;
import com.aspose.cad.fileformats.cad.cadobjects.Cad2DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.Cad2LineAngularDimension;
import com.aspose.cad.fileformats.cad.cadobjects.Cad3DPoint;
import com.aspose.cad.fileformats.cad.cadobjects.CadArc;
import com.aspose.cad.fileformats.cad.cadobjects.CadBaseEntity;
import com.aspose.cad.fileformats.cad.cadobjects.CadBlockEntity;
import com.aspose.cad.fileformats.cad.cadobjects.CadCircle;
import com.aspose.cad.fileformats.cad.cadobjects.CadDimensionBase;
import com.aspose.cad.fileformats.cad.cadobjects.CadEllipse;
import com.aspose.cad.fileformats.cad.cadobjects.CadInsertObject;
import com.aspose.cad.fileformats.cad.cadobjects.CadLeader;
import com.aspose.cad.fileformats.cad.cadobjects.CadLine;
import com.aspose.cad.fileformats.cad.cadobjects.CadLwPolyline;
import com.aspose.cad.fileformats.cad.cadobjects.CadMLeader;
import com.aspose.cad.fileformats.cad.cadobjects.CadMText;
import com.aspose.cad.fileformats.cad.cadobjects.CadMultiLine;
import com.aspose.cad.fileformats.cad.cadobjects.CadOle2Frame;
import com.aspose.cad.fileformats.cad.cadobjects.CadSeqend;
import com.aspose.cad.fileformats.cad.cadobjects.CadSolid;
import com.aspose.cad.fileformats.cad.cadobjects.CadSpline;
import com.aspose.cad.fileformats.cad.cadobjects.CadText;
import com.aspose.cad.fileformats.cad.cadobjects.CadViewport;
import com.aspose.cad.fileformats.cad.cadobjects.acadtable.CadTableEntity;
import com.aspose.cad.fileformats.cad.cadobjects.attentities.CadAttDef;
import com.aspose.cad.fileformats.cad.cadobjects.attentities.CadAttrib;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadBoundaryPathCircularArc;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadBoundaryPathCircularEllipse;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadBoundaryPathLine;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadBoundaryPathSpline;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadEdgeBoundaryPath;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadHatch;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadHatchBoundaryPathContainer;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.CadPolylineBoundaryPath;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.ICadBoundaryPath;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.ICadBoundaryPathEntity;
import com.aspose.cad.fileformats.cad.cadobjects.hatch.Point2D;
import com.aspose.cad.fileformats.cad.cadobjects.polylines.CadPolyline;
import com.aspose.cad.fileformats.cad.cadobjects.polylines.CadPolyline3D;
import com.aspose.cad.fileformats.cad.cadobjects.vertices.Cad2DVertex;
import com.aspose.cad.system.collections.Generic.Dictionary.KeyCollection;

import cn.cmcc.cad.entity.XhxkCadBaseEntity;
import cn.cmcc.cad.entity.XhxkCadImage;
import cn.cmcc.cad.entity.XhxkCadInsertObject;
import cn.cmcc.cad.entity.XhxkCadPoint;
import cn.cmcc.cad.region.entity.CadRegion;
import cn.cmcc.cad.region.service.ICadRegionService;
import cn.cmcc.cad.region.util.CadToImageUtil;
import cn.cmcc.cad.service.ICadService;
import cn.cmcc.cad.service.impl.CadServiceImpl;
import cn.cmcc.common.web.domain.AjaxResult;

@Service
public class CadRegionServiceImpl implements ICadRegionService {
	public static Logger logger = LoggerFactory.getLogger(CadRegionServiceImpl.class);

	@Autowired
	public ICadService cadService = new CadServiceImpl();
	
	@Override
	public List<CadRegion> getCadRegions(String dwgFile) {
		long start = System.currentTimeMillis();
		CadImage imageModel = null;
		List<CadRegion> list = new LinkedList<CadRegion>();
		try {
			cadService.cadLicense();
			int index = 0;
			imageModel = (CadImage) CadImage.load(dwgFile);
			long loadEnd = System.currentTimeMillis();
			CadBlockDictionary block = imageModel.getBlockEntities();
			KeyCollection<String, CadBlockEntity> keys = block.getKeys();
			Map<String, CadRegion> cadSquareMap = new HashMap<String, CadRegion>();
			Set<String> set = new HashSet<String>();
			for (String blockKey : keys) {
				if (blockKey.equals("*Model_Space")) {
					continue;
				}
				List<CadRegion> cadItemBlockSquares = new LinkedList<CadRegion>();
				CadBlockEntity entity = block.get_Item(blockKey);
				CadBaseEntity[] entities = entity.getEntities();
				for (CadBaseEntity baseEntity : entities) {
					if (baseEntity instanceof CadLwPolyline) {
						CadRegion cadSquare = isSquare(index++, (CadLwPolyline) baseEntity);
						if (cadSquare != null) {
							cadItemBlockSquares.add(cadSquare);
						}
					}
				}
				if (!cadItemBlockSquares.isEmpty()) {
					List<CadRegion> blockList = getBoundarySquare(cadItemBlockSquares);
					if (!blockList.isEmpty()) {
						cadSquareMap.put(blockKey, blockList.get(0));
					}
				}
			}
			double maxWidth = 0;
			double maxHeight = 0;
			CadBaseEntity[] entities = imageModel.getEntities();
			List<CadRegion> cadSquares = new LinkedList<CadRegion>();
			List<CadRegion> cadBlockSquares = new LinkedList<CadRegion>();
			for (CadBaseEntity entity : entities) {
				Cad3DPoint point = calculateBox(entity);
				if (point == null) {
					set.add(entity.getClass().getName());
				}

				if (entity instanceof CadLwPolyline) {
					CadRegion cadSquare = isSquare(index, (CadLwPolyline) entity);
					if (cadSquare != null) {
						cadSquares.add(cadSquare);
						if (cadSquare.getWidth() > maxWidth) {
							maxWidth = cadSquare.getWidth();
						}
						if (cadSquare.getHeight() > maxHeight) {
							maxHeight = cadSquare.getHeight();
						}
					}
				} else if (entity instanceof CadInsertObject) {
					CadInsertObject insertObject = (CadInsertObject) entity;
					String blockName = insertObject.getName();
					if(blockName.contains("标尺")) {
						System.out.println();
					}
					if (cadSquareMap.containsKey(blockName)) {
						// 不考虑旋转，只考虑缩放
						CadRegion cadSquare = cadSquareMap.get(blockName);
						CadRegion cadSquareScale = new CadRegion(index, cadInsertObjectPoint(insertObject, cadSquare.getA()), cadInsertObjectPoint(insertObject, cadSquare.getB()), cadInsertObjectPoint(insertObject, cadSquare.getC()), cadInsertObjectPoint(insertObject, cadSquare.getD()));
						cadBlockSquares.add(cadSquareScale);
						if (cadSquareScale.getWidth() > maxWidth) {
							maxWidth = cadSquareScale.getWidth();
						}
						if (cadSquareScale.getHeight() > maxHeight) {
							maxHeight = cadSquareScale.getHeight();
						}
					}
				}
				index++;
			}

			
			double maxWidth_ = maxWidth;
			double maxHeight_ = maxHeight;
			cadSquares = cadSquares.stream().filter(squares -> {
				return (squares.getWidth() > maxWidth_ / 2) && (squares.getHeight() > maxHeight_ / 2);
			}).collect(Collectors.toList());
			cadBlockSquares = cadBlockSquares.stream().filter(squares -> {
				return (squares.getWidth() > maxWidth_ / 2) && (squares.getHeight() > maxHeight_ / 2);
			}).collect(Collectors.toList());
			list = getBoundarySquare(cadSquares);
			List<CadRegion> blockList = getBoundarySquare(cadBlockSquares);
			list.addAll(blockList);
			
			list = getBoundarySquare(list);//blockEntity和baseEntity合并去重
			
			//删除重复项
			List<CadRegion> regions = new LinkedList<CadRegion>();
			Set<String> regionKeys = new HashSet<String>();
			for(CadRegion square : list) {
				if(!regionKeys.contains(square.getKey())) {
					regions.add(square);
					regionKeys.add(square.getKey());
				}
			}
			list = regions;
			long regionEnd = System.currentTimeMillis();
			logger.info("读取图纸用时: "+(loadEnd-start)+"ms , 获取图框完成，用时: "+(regionEnd-start)+"ms ");
			
			XhxkCadImage xhxkCadImage = cadService.transformCadImage(imageModel);
			long xhxkLoadEnd = System.currentTimeMillis();
			//debuger时用
//			list.clear();
//			XhxkCadPoint minPoint = xhxkCadImage.getMinPoint();
//			XhxkCadPoint maxPoint = xhxkCadImage.getMaxPoint();
//			CadRegion region = new CadRegion(0, new Cad2DPoint(minPoint.getX()-10, minPoint.getY()-10), new Cad2DPoint(minPoint.getX()-10, maxPoint.getY()+10), new Cad2DPoint(maxPoint.getX()+10, maxPoint.getY()+10), new Cad2DPoint(maxPoint.getX()+10, minPoint.getY()-10));
//			region.setBaseEntities(xhxkCadImage.getEntities());
//			region.setBlockEntities(xhxkCadImage.getBlockEntities());
//			list.add(region);
			
			for (CadRegion square : list) {	
				List<XhxkCadBaseEntity> baseEntities = new LinkedList<XhxkCadBaseEntity>();
				List<XhxkCadBaseEntity> xhxkCadBaseEntities = xhxkCadImage.getEntities();
				
				
				for (XhxkCadBaseEntity entity : xhxkCadBaseEntities) {
					XhxkCadPoint point = entity.getPosition();
					boolean status = squareContain(square,new Cad2DPoint(point.getX(), point.getY()));
					if(status) {
						if (entity instanceof XhxkCadInsertObject) {
							XhxkCadInsertObject icoEntity = (XhxkCadInsertObject) entity;
							
							List<XhxkCadBaseEntity> objectEntities = icoEntity.getIndoorCadEntities();
							for (XhxkCadBaseEntity ice : objectEntities) {
								baseEntities.add(ice);
							}
						} else {
							baseEntities.add(entity);
						}
					}
				}
				square.setBaseEntities(baseEntities);
				square.setBlockEntities(xhxkCadImage.getBlockEntities());
			}
			long end = System.currentTimeMillis();
			logger.info("识别图框任务，图纸:"+dwgFile+", 总识别图框数: "+cadSquares.size()+" 去重后图框数: "+list.size()+" , 用时: "+(end-start)+"ms , 转化图纸用时: "+(xhxkLoadEnd-regionEnd)+ "ms");
			
			return list;
		}catch (Exception e) {
			logger.error(e.getMessage(),e);
			return null;
		}finally {
			try {
				imageModel.close();
				imageModel = null;
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
		}
	}

	@Override
	public AjaxResult cadRegionToImage(CadRegion region, boolean clearText, int lineStroke, String imageFile) {
		List<XhxkCadBaseEntity> baseEntities = region.getBaseEntities();
		if(baseEntities==null || baseEntities.isEmpty()) {
			String log = region.toLog()+ " 图框无对象，无法生成图片!";
			logger.info(log);
			return AjaxResult.error(log);
		}
		CadToImageUtil generator = new CadToImageUtil(cadService,String.valueOf(region.getIndex()),region,imageFile,clearText,lineStroke);
		return generator.buildImage();
	}
	
	/**
	 * 返回cad对象中的任一坐标，主要用于是否在图框内
	 * @param cadEntity
	 * @return
	 */
	private Cad3DPoint calculateBox(CadBaseEntity cadEntity) {
		if (cadEntity instanceof CadText) {
			CadText text = (CadText) cadEntity;
			Cad3DPoint point = text.getFirstAlignment();
			return point;
		} else if (cadEntity instanceof CadViewport) {
			CadViewport viewport = (CadViewport) cadEntity;
			// 返回CadViewport对象的左下角坐标
			Cad3DPoint point = viewport.getCenterPoint();
			return point;
		} else if (cadEntity instanceof CadInsertObject) {
			CadInsertObject insert = (CadInsertObject) cadEntity;
			// 返回CadInsertObject对象的插入点坐标
			return insert.getInsertionPoint();
		} else if (cadEntity instanceof CadDimensionBase) {
			CadDimensionBase dimension = (CadDimensionBase) cadEntity;
			// 返回CadDimensionBase对象的第一个点
			Cad3DPoint point = dimension.getDefinitionPoint();
			return point;
		} else if (cadEntity instanceof CadCircle) {
			CadCircle circle = (CadCircle) cadEntity;

			// 返回CadCircle对象的圆心坐标
			Cad3DPoint point = circle.getCenterPoint();
			return point;
		} else if (cadEntity instanceof Cad2LineAngularDimension) {
			Cad2LineAngularDimension dimension = (Cad2LineAngularDimension) cadEntity;
			// 返回Cad2LineAngularDimension对象的第一个点
			Cad3DPoint point = dimension.getDefinitionPoint1();
			return point;
		} else if (cadEntity instanceof CadMText) {
			CadMText mtext = (CadMText) cadEntity;
			// 返回CadMText对象的第一个点
			Cad3DPoint point = mtext.getInsertionPoint();
			return point;
		} else if (cadEntity instanceof CadLeader) {
			CadLeader leader = (CadLeader) cadEntity;
			// 返回CadLeader对象的第一个点
			List<Cad3DPoint> points = leader.getCoordinates();
			if ((points != null) && !points.isEmpty()) {
				Cad3DPoint point = points.get(0);
				return point;
			}
		} else if (cadEntity instanceof CadLwPolyline) {
			CadLwPolyline polyline = (CadLwPolyline) cadEntity;
			// 返回CadLwPolyline对象的第一个点
			Cad2DPoint point = polyline.getCoordinates().get(0);
			return new Cad3DPoint(point.getX(), point.getY());
		} else if (cadEntity instanceof CadLine) {
			CadLine line = (CadLine) cadEntity;
			// 返回CadLine对象的起点坐标
			Cad3DPoint point = line.getFirstPoint();
			return point;
		} else if (cadEntity instanceof CadMultiLine) {
			CadMultiLine multiline = (CadMultiLine) cadEntity;
			// 返回CadMultiLine对象的第一个点
			Cad3DPoint point = multiline.getStartPoint();
			return point;
		} else if (cadEntity instanceof CadArc) {
			CadArc arc = (CadArc) cadEntity;
			// 返回CadArc对象的起点坐标
			Cad3DPoint point = arc.getCenterPoint();
			return point;
		} else if (cadEntity instanceof CadEllipse) {
			CadEllipse ellipse = (CadEllipse) cadEntity;
			// 返回CadEllipse对象的中心点坐标
			Cad3DPoint point = ellipse.getCenterPoint();
			return point;
		} else if (cadEntity instanceof CadHatch) {
			CadHatch hatch = (CadHatch) cadEntity;
			// 返回CadHatch对象的第一个边界点
			List<CadHatchBoundaryPathContainer> list = hatch.getBoundaryPaths();
			if (list != null && !list.isEmpty()) {
				CadHatchBoundaryPathContainer container = list.get(0);
				List<ICadBoundaryPath> boundaryPaths = container.a();
				if (boundaryPaths != null && !boundaryPaths.isEmpty()) {
					ICadBoundaryPath boundaryPath = boundaryPaths.get(0);
					if (boundaryPath instanceof CadPolylineBoundaryPath) {
						CadPolylineBoundaryPath cadPolylineBoundaryPath = (CadPolylineBoundaryPath) boundaryPath;
						List<Point2D> point2ds = cadPolylineBoundaryPath.getVertices();
						if (point2ds != null && !point2ds.isEmpty()) {
							Point2D point = point2ds.get(0);
							return new Cad3DPoint(point.getX(), point.getY());
						}
					} else if (boundaryPath instanceof CadEdgeBoundaryPath) {
						CadEdgeBoundaryPath cadEdgeBoundaryPath = (CadEdgeBoundaryPath) boundaryPath;
						List<ICadBoundaryPathEntity> cadBoundaryPathEntities = cadEdgeBoundaryPath.getObjects();
						ICadBoundaryPathEntity cadBoundaryPathEntity = cadBoundaryPathEntities.get(0);
						if (cadBoundaryPathEntity instanceof CadBoundaryPathLine) {
							CadBoundaryPathLine cadBoundaryPathLine = (CadBoundaryPathLine) cadBoundaryPathEntity;
							Point2D point = cadBoundaryPathLine.getFirstPoint();
							return new Cad3DPoint(point.getX(), point.getY());
						} else if (cadBoundaryPathEntity instanceof CadBoundaryPathCircularArc) {
							CadBoundaryPathCircularArc cadBoundaryPathCircularArcd = (CadBoundaryPathCircularArc) cadBoundaryPathEntity;
							Point2D point = cadBoundaryPathCircularArcd.getCenterPoint();
							return new Cad3DPoint(point.getX(), point.getY());
						} else if (cadBoundaryPathEntity instanceof CadBoundaryPathCircularEllipse) {
							CadBoundaryPathCircularEllipse cadBoundaryPathCircularArcd = (CadBoundaryPathCircularEllipse) cadBoundaryPathEntity;
							Point2D point = cadBoundaryPathCircularArcd.getCenterPoint();
							return new Cad3DPoint(point.getX(), point.getY());
						} else if (cadBoundaryPathEntity instanceof CadBoundaryPathSpline) {
							CadBoundaryPathSpline cadBoundaryPathSpline = (CadBoundaryPathSpline) cadBoundaryPathEntity;
							Point2D point = cadBoundaryPathSpline.getStartTangent();
							return new Cad3DPoint(point.getX(), point.getY());
						}
					}

				}
			}
		} else if (cadEntity instanceof CadSpline) {
			CadSpline cadSpline = (CadSpline) cadEntity;
			List<Cad3DPoint> points = cadSpline.getFitPoints();
			if ((points != null) && !points.isEmpty()) {
				return points.get(0);
			}

			points = cadSpline.getControlPoints();
			if ((points != null) && !points.isEmpty()) {
				return points.get(0);
			}

		} else if (cadEntity instanceof CadSolid) {
			CadSolid cadSolid = (CadSolid) cadEntity;
			return cadSolid.getFirstCorner();
		} else if (cadEntity instanceof CadTableEntity) {
			CadTableEntity cadTableEntity = (CadTableEntity) cadEntity;
			return cadTableEntity.getInsertionPoint();
		} else if (cadEntity instanceof CadAttDef) {
			CadAttDef cadAttDef = (CadAttDef) cadEntity;
			return cadAttDef.getFirstAlignment();
		} else if (cadEntity instanceof CadOle2Frame) {
			CadOle2Frame cadOle2Frame = (CadOle2Frame) cadEntity;
			return cadOle2Frame.getUpperLeftCorner();
		} else if (cadEntity instanceof CadPolyline) {
			CadPolyline cadPolyline = (CadPolyline) cadEntity;
			List<CadBaseEntity> cadBaseEntities = cadPolyline.getChildObjects();
			for (CadBaseEntity entity : cadBaseEntities) {
				if (entity instanceof Cad2DVertex) {
					Cad2DVertex cad2dVertex = (Cad2DVertex) entity;
					return cad2dVertex.getLocationPoint();
				}
			}
		} else if (cadEntity instanceof CadPolyline3D) {
			CadPolyline3D cadPolyline3D = (CadPolyline3D) cadEntity;
			List<CadBaseEntity> cadBaseEntities = cadPolyline3D.getChildObjects();
			for (CadBaseEntity entity : cadBaseEntities) {
				if (entity instanceof Cad2DVertex) {
					Cad2DVertex cad2dVertex = (Cad2DVertex) entity;
					return cad2dVertex.getLocationPoint();
				}
			}
		} else if (cadEntity instanceof CadSeqend) {
			CadSeqend cadSeqend = (CadSeqend) cadEntity;

		} else if (cadEntity instanceof CadMLeader) {
			CadMLeader cadMLeader = (CadMLeader) cadEntity;

		} else if (cadEntity instanceof CadAttrib) {
			CadAttrib cadAttrib = (CadAttrib) cadEntity;
		} else {//对象较少，不影响整体效果的对象，暂时舍去
			//System.err.println(cadEntity.getClass().getName());
		}

		return null;
	}
	
	/**
	 * 获取CadInsertObject的真实位置：模型+位置+缩放 计算得出
	 * @param insertObject
	 * @param point
	 * @return
	 */
	private Cad2DPoint cadInsertObjectPoint(CadInsertObject insertObject, Cad2DPoint point) {
		double scaleX = insertObject.getScaleX();
		double scaleY = insertObject.getScaleY();
		Cad3DPoint insertionPoint = insertObject.getInsertionPoint();
		return new Cad2DPoint((point.getX() + insertionPoint.getX()) * scaleX, (point.getY() + insertionPoint.getY()) * scaleY);
	}

	/**
	 * 清除外层还有图框的矩形
	 * @param squares
	 * @return
	 */
	private List<CadRegion> getBoundarySquare(List<CadRegion> squares) {
		List<CadRegion> list = new LinkedList<CadRegion>();
		for (CadRegion subSquare : squares) {
			boolean status = false;
			for (CadRegion square : squares) {
				if (subSquare.getIndex() != square.getIndex()) {
					status = squareContain(square, subSquare);
					if (status) {
						break;
					}
				}
			}
			if (!status) {
				list.add(subSquare);
			}
		}
		return list;
	}

	/**
	 * 判断subCadSquare是否在cadSquare内部，只有四个点在内部就算，避免共线问题
	 * 
	 * @param cadSquare
	 * @param subCadSquare
	 * @return
	 */
	private boolean squareContain(CadRegion cadSquare, CadRegion subCadSquare) {
		boolean statusA = squareContain(cadSquare, subCadSquare.getA());
		boolean statusB = squareContain(cadSquare, subCadSquare.getB());
		boolean statusC = squareContain(cadSquare, subCadSquare.getC());
		boolean statusD = squareContain(cadSquare, subCadSquare.getD());
		return statusA&&statusB&&statusC&&statusD;
	}

	/**
	 * 计算坐标是否在图框内
	 * @param cadSquare
	 * @param point
	 * @return
	 */
	private boolean squareContain(CadRegion cadSquare, Cad2DPoint point) {
		Cad2DPoint minPoint = cadSquare.getMinPoint();
		Cad2DPoint maxPoint = cadSquare.getMaxPoint();

		boolean status = false;
		if ((point.getX() > minPoint.getX()) && (point.getX() < maxPoint.getX()) && (point.getY() > minPoint.getY()) && (point.getY() < maxPoint.getY())) {
			status = true;
		}

		return status;
	}

	private CadRegion isSquare(int index, CadLwPolyline polyline) {
		List<Cad2DPoint> points = polyline.getCoordinates();
		if (points.size() != 4) {
			return null;
		}
		return isSquare(index, points.get(0), points.get(1), points.get(2), points.get(3));
	}

	/**
	 * 计算图框简化版本，矩形
	 * @param a
	 * @param b
	 * @return
	 */
	private CadRegion isSquare(int index, Cad2DPoint a, Cad2DPoint b, Cad2DPoint c, Cad2DPoint d) {
		double ab = getDistance(a, b);
		double ac = getDistance(a, c);
		double ad = getDistance(a, d);
		double bc = getDistance(b, c);
		double bd = getDistance(b, d);
		double cd = getDistance(c, d);

		if (ab == cd && ac == bd && ad == bc) {
			return new CadRegion(index, a, b, c, d);
		}
		return null;
	}

	/**
	 * 计算两个坐标直线距离
	 * @param a
	 * @param b
	 * @return
	 */
	private double getDistance(Cad2DPoint a, Cad2DPoint b) {
		double dx = a.getX() - b.getX();
		double dy = a.getY() - b.getY();
		return Math.sqrt(dx * dx + dy * dy);
	}
	
	
}
