package cn.cmcc.auth.service.cas;

import static cn.cmcc.auth.config.CasConfig.CACHE_TYPE;
import static cn.cmcc.auth.config.CasConfig.REDIRECT;
import static cn.cmcc.auth.config.CasConfig.ROOT_SUFFIX;
import static cn.cmcc.auth.config.CasConfig.TICKET_PREFIX;
import static cn.cmcc.auth.config.CasConfig.USER_NAME;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.common.cache.config.CacheConfig;
import cn.cmcc.common.cache.context.SysConfigContext;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.context.SecurityUtils;
import cn.cmcc.common.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 吉林实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Slf4j
public class CasServiceJLImpl extends CacheConfig implements ICasService {

    private final CasConfig config;

    private final String logoutNotifyUrl;

    public CasServiceJLImpl(CasConfig config) {
        this.config = config;
        JSONObject otherParameters = StringUtils.isEmpty(config.getOtherParameters()) ? null : JSON.parseObject(config.getOtherParameters());
        this.logoutNotifyUrl = (otherParameters == null || otherParameters.getString("casServerLogoutNotifySuffix") == null) ?
                null : String.format("%s%s", config.getCasServerUrl(), otherParameters.getString("casServerLogoutNotifySuffix"));
        log.info("casServerLogoutNotifyUrl: {}", this.logoutNotifyUrl);
        try {
            config.setCasLoginUrl(String.format("%s%s", config.getCasServerUrl(), config.getCasServerLoginSuffix()));
            log.info("casLoginUrl: {}", config.getCasLoginUrl());
            config.setCasValidateUrl(String.format("%s%s", config.getCasServerUrl(), config.getCasServerValidateSuffix()));
            log.info("casValidateUrl: {}", config.getCasValidateUrl());
            config.setExpire(SysConfigContext.getJwtSecretExpireSec());
            log.info("expire: {}", config.getExpire());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public AjaxResult login(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>(16);
        try {
            // 已经有登录信息
            if (validToken(request)) {
                data.put(REDIRECT, ROOT_SUFFIX);
                return AjaxResult.success(data);
            }

            String ticket = getTicket(request, config.getCasTicketParameter());
            if (StringUtils.isEmpty(ticket)) {
                log.info("Ticket is empty, jumps to the CAS login page.");
                data.put(REDIRECT, config.getCasLoginUrl());
            } else {
                String xml = getResponse(config.getCasValidateUrl(), ticket, config.getIpAddress(request));
                log.info("Response:\n{}", xml);

                String userName = config.getTextForElement(xml, "ACCOUNT", false);
                if (StringUtils.isNotEmpty(userName)) {
                    // RESULT, RESULT_MSG, RESULT_MSGCODE, ACCOUNT, TICKET
                    String token = (String) config.getLoginService().ssoLogin(userName).get(SecurityConstants.TOKEN);
                    data.put(REDIRECT, ROOT_SUFFIX);
                    data.put(SecurityConstants.TOKEN, token);
                    data.put(USER_NAME, userName);

                    // 票据-TOKEN
                    config.getCacheService().set(TICKET_PREFIX + token, ticket, config.getExpire(), TimeUnit.SECONDS, CACHE_TYPE);
                } else {
                    data.put(REDIRECT, config.getCasLoginUrl());
                    data.put("msg", config.getTextForElement(xml, "RESULT_MSG", false));
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);

            data.put(REDIRECT, config.getCasLoginUrl());
            data.put("msg", ex.getMessage());
        }
        return AjaxResult.success(data);
    }

    @Override
    public void logout(HttpServletRequest request){
        if (StringUtils.isEmpty(this.logoutNotifyUrl)) return;

        String token = SecurityUtils.getToken(request);
        Object ticket = config.getCacheService().get(TICKET_PREFIX + token, CACHE_TYPE);
        if (ticket == null) {
            log.info("Token: [" + token + "], ticket is null.");
            return;
        }

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(this.logoutNotifyUrl);
            List<NameValuePair> pairs = new ArrayList<>();
            pairs.add(new BasicNameValuePair("ticket", (String) ticket));
            pairs.add(new BasicNameValuePair("clientIp", config.getIpAddress(request)));
            post.setEntity(new UrlEncodedFormEntity(pairs, StandardCharsets.UTF_8.name()));
            CloseableHttpResponse response = client.execute(post);
            String xml = EntityUtils.toString(response.getEntity());
            log.info("Response:\n{}", xml);
        } catch (IOException e) {
			log.error(e.getMessage(),e);
		}
    }

    /**
     * 获取响应
     *
     * @param url    URL
     * @param ticket 票据
     * @param ip     IP
     * @return 响应
     */
    public String getResponse(String url, String ticket, String ip) throws IOException {
        log.info("URL: {}.", StringUtils.replace(url, "[\r\n]", ""));

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            List<NameValuePair> pairs = new ArrayList<>();
            pairs.add(new BasicNameValuePair("ticket", ticket));
            pairs.add(new BasicNameValuePair("clientIp", ip));
            post.setEntity(new UrlEncodedFormEntity(pairs, StandardCharsets.UTF_8.name()));
            CloseableHttpResponse response = client.execute(post);
            return EntityUtils.toString(response.getEntity());
        }
    }

}
