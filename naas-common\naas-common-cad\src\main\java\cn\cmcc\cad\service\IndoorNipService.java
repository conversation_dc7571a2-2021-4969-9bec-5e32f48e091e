package cn.cmcc.cad.service;

import java.util.Map;

/**
 * @ClassName IndoorNipService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/13 16:38
 * @Version 1.0
 **/
public interface IndoorNipService {
    /**
     * 参数为文件类型的接口调用示例
     *
     * @param interfaceUrl  接口地址
     * @param tokenUrl      token获取地址
     * @param fileParamCode 文件参数编码
     * @param filePath      文件地址
     * @param otherParams   其他参数
     * @return
     */
    String sendReqByFile(String interfaceUrl, String tokenUrl, String fileParamCode, String filePath, Map<String, Object> otherParams);
    /**
     * 请求参数为json
     *
     * @param interfaceUrl
     * @param tokenUrl
     * @param paramJson
     * @return
     */
    Object sendReqByJson(String interfaceUrl, String tokenUrl, String paramJson);
}
