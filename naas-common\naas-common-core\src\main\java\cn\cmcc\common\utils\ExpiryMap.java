/**
 * @Title: ExpiryMap.java
 * @Package cn.cmcc.common.utils
 * 
 * <AUTHOR>
 * @date 2021年8月12日
 * @version V1.0
 */
package cn.cmcc.common.utils;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class ExpiryMap<K, V> extends ConcurrentHashMap<K, V> {

	private static final long serialVersionUID = 1L;
	
	/**
     * default expiry time ，5分钟
     */
    private long EXPIRY = 300L * 1000 ;
	
	private Map<K, Long> expiryMap = new ConcurrentHashMap<K, Long>(32, 1F);

    public ExpiryMap(){
        super(32);
    }
    
    public ExpiryMap(long defaultExpiryTime){
        this(1 << 4, defaultExpiryTime);
    }
    
    /**
     * 
     * @param initialCapacity
     * @param defaultExpiryTime  单位毫秒
     */
    public ExpiryMap(int initialCapacity, long defaultExpiryTime){
        super(initialCapacity);
        this.EXPIRY = defaultExpiryTime;
    }
    
    @Override
    public V put(K key, V value) {
        expiryMap.put(key, System.currentTimeMillis() + EXPIRY);
        return super.put(key, value);
    }
    
    /**
     * @param key
     * @param value
     * @param expiryTime 键值对有效期 单位秒
     * @return
     */
    public V put(K key, V value, long expiryTime) {
        expiryMap.put(key, System.currentTimeMillis() + expiryTime);
        return super.put(key, value);
    }
    
    @Override
    public V putIfAbsent(K key, V value) {
    	Long expiryTime = expiryMap.get(key);
        if(expiryTime == null ) {
        	return put(key,value);
        }else {
        	boolean flag = System.currentTimeMillis() > expiryTime;
            if(flag){
            	//过期重新放新的值,并返回空
        		put(key,value);
        		return null;
            }
            // 不过期
    		return super.get(key);
        }
    }
    
    public V putIfAbsent(K key, V value, long expiryTime) {
    	Long keyExpiryTime = expiryMap.get(key);
        if(keyExpiryTime == null ) {
        	return put(key, value, expiryTime);
        }else {
        	boolean flag = System.currentTimeMillis() > keyExpiryTime;
            if(flag){
            	//过期重新放新的值,并返回空
        		put(key, value, expiryTime);
        		return null;
            }
            // 不过期
    		return super.get(key);
        }
    }
    
    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        for (Map.Entry<? extends K, ? extends V> e : m.entrySet()){
            expiryMap.put(e.getKey(), System.currentTimeMillis() + EXPIRY);
        }
        super.putAll(m);
    }
    
    @Override
    public V get(Object key) {
        if (key == null){
            return null;
        }

        if(checkExpiry(key, true)){
            return null;
        }
        return super.get(key);
    }
    
    @Override
    public V remove(Object key) {
    	expiryMap.remove(key);
    	return super.remove(key);
    }
    
    @Override
    public boolean remove(Object key, Object value) {
    	expiryMap.remove(key);
    	return super.remove(key, value);
    }
    
    @Override
    public boolean containsKey(Object key) {
        return !checkExpiry(key, true) && super.containsKey(key);
    }
    
    @Override
    public int size() {
        return entrySet().size();
    }
    
    @Override
    public boolean isEmpty() {
        return entrySet().size() == 0;
    }
    
    @Override
    public boolean containsValue(Object value) {
        if (value == null) return Boolean.FALSE;
        Set<Entry<K, V>> set = super.entrySet();
        Iterator<Entry<K, V>> iterator = set.iterator();
        while (iterator.hasNext()) {
            java.util.Map.Entry<K, V> entry = iterator.next();
            if(value.equals(entry.getValue())){
                if(checkExpiry(entry.getKey(), false)) {
                    iterator.remove();
                    return Boolean.FALSE;
                }else{
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }
    
    @Override
    public Collection<V> values() {
        Collection<V> values = super.values();
        if(values == null || values.size() < 1){
            return values;
        }

        Iterator<V> iterator = values.iterator();
        while (iterator.hasNext()) {
            V next = iterator.next();
            if(!containsValue(next)) {
                iterator.remove();
            }
        }
        return values;
    }
    
    /**
     * @Description: 是否过期
     * @return null:不存在或key为null -1:过期  存在且没过期返回value 因为过期的不是实时删除，所以稍微有点作用
     * @param key
     * @return
     */
    public Object isInvalid(Object key) {
        if (key == null) return null;
        
        Long expiryTime = expiryMap.get(key);
        if(expiryTime == null) return null;

        boolean flag = System.currentTimeMillis() > expiryTime;
        if(flag){
            super.remove(key);
            expiryMap.remove(key);
            return -1;
        }
        return super.get(key);
    }
    
    @Override
    public Set<Map.Entry<K,V>> entrySet() {
        Set<java.util.Map.Entry<K, V>> set = super.entrySet();
        Iterator<java.util.Map.Entry<K, V>> iterator = set.iterator();
        while (iterator.hasNext()) {
            java.util.Map.Entry<K, V> entry = iterator.next();
            if(checkExpiry(entry.getKey(), true)) {
                iterator.remove();
            }
        }

        return set;
    }
    
    /**
     * @Description: 是否过期
     **/
    private boolean checkExpiry(Object key, boolean isRemoveSuper){
        Long expiryTime = expiryMap.get(key);
        if(expiryTime == null ) return Boolean.FALSE;
        boolean flag = System.currentTimeMillis() > expiryTime;
        if(flag){
            if(isRemoveSuper){
                super.remove(key);
            }
            expiryMap.remove(key);
        }
        return flag;
    }
}
