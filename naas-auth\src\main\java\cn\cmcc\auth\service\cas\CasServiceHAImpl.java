package cn.cmcc.auth.service.cas;

import static cn.cmcc.auth.config.CasConfig.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.auth.config.CasConfig;
import cn.cmcc.auth.service.ICasService;
import cn.cmcc.common.cache.context.SysConfigContext;
import cn.cmcc.common.constant.SecurityConstants;
import cn.cmcc.common.utils.http.HttpClientUtils;
import cn.cmcc.common.utils.http.bo.HttpReqParam;
import cn.cmcc.common.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * CAS 河南实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Slf4j
public class CasServiceHAImpl implements ICasService {

    private final CasConfig config;

    /**
     * 应用代码
     */
    private final String appCode;

    /**
     * 应用程序密钥
     */
    private final String appKey;

    /**
     * 租户
     */
    private final String tenant;

    public CasServiceHAImpl(CasConfig config) {
        this.config = config;
        Map<String, Object> otherParameters = JSON.parseObject(config.getOtherParameters());
        this.appCode = (String) otherParameters.get("appCode");
        this.appKey = (String) otherParameters.get("appKey");
        this.tenant = (String) otherParameters.get("tenant");
        try {
            String service = URLEncoder.encode(String.format("%s%s", config.getServerUrl(), config.getServerLoginSuffix()),
                    StandardCharsets.UTF_8.name());
            config.setCasLoginUrl(String.format("%s%s?appCode=%s&%s=%s", config.getCasServerUrl(), config.getCasServerLoginSuffix(),
                    this.appCode, config.getCasServerServiceParameter(), service));
            log.info("casLoginUrl: {}", config.getCasLoginUrl());
            config.setCasValidateUrl(String.format("%s%s", config.getCasServerUrl(), config.getCasServerValidateSuffix()));
            log.info("casValidateUrl: {}", config.getCasValidateUrl());
            config.setExpire(SysConfigContext.getJwtSecretExpireSec());
            log.info("expire: {}", config.getExpire());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public AjaxResult login(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>(16);
        try {
            // 已经有登录信息
            if (validToken(request)) {
                data.put(REDIRECT, ROOT_SUFFIX);
                return AjaxResult.success(data);
            }

            String ticket = getTicket(request, config.getCasTicketParameter());
            if (StringUtils.isEmpty(ticket)) {
                log.info("Ticket is empty, jumps to the CAS login page.");
                data.put(REDIRECT, config.getCasLoginUrl());
            } else {
                log.info("Ticket: {} \n.", StringUtils.replace(ticket, "[\r\n]", ""));
            	ticket = URLEncoder.encode(ticket, StandardCharsets.UTF_8.name());
            	log.info("After encode: {}.", StringUtils.replace(ticket, "[\r\n]", ""));
                Map<String, String> headers = new HashMap<>();
                // headers.put("Content-Type", "application/json");
                headers.put("authentication", null);
                headers.put("clientIp", request.getRemoteAddr());
                Map<String, Object> params = new LinkedHashMap<>();
                params.put("appCode", this.appCode);
                params.put("appKey", this.appKey);
                params.put("tenant", this.tenant);
                params.put("data", ticket);
                String requestJson = JSON.toJSONString(params);
                log.info("Request:\n{}", requestJson);

                AjaxResult result = HttpClientUtils.send(HttpReqParam.builder(config.getCasValidateUrl(), requestJson, HttpMethod.POST)
                        .headerParam(headers).build());
                if (result.isSuccess()) {
                    String response = result.getData().toString();
                    log.info("Response:\n{}", response);

                    JSONObject responseJson = JSONObject.parseObject(response);
                    if ("1000".equals(responseJson.getString("retCode"))) {
                        // accountID, name, empNo, idCardNum, phone, mobile, email
                        Map<?, ?> userInfo = (Map<?, ?>) responseJson.get("userInfo");
                        String userName = (String) userInfo.get("accountID");
                        String token = (String) config.getLoginService().ssoLogin(userName).get(SecurityConstants.TOKEN);
                        data.put(REDIRECT, ROOT_SUFFIX);
                        data.put(SecurityConstants.TOKEN, token);
                        data.put(USER_NAME, userName);

                        // 票据-TOKEN
                        config.getCacheService().set(TICKET_PREFIX + ticket, token, config.getExpire(), TimeUnit.SECONDS, CACHE_TYPE);
                    } else {
                        data.put(REDIRECT, config.getCasLoginUrl());
                        data.put("msg", responseJson.getString("msg"));
                    }
                } else {
                    data.put(REDIRECT, config.getCasLoginUrl());
                    data.put("msg", result.getMsg());
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);

            data.put(REDIRECT, config.getCasLoginUrl());
            data.put("msg", ex.getMessage());
        }
        return AjaxResult.success(data);
    }

}
