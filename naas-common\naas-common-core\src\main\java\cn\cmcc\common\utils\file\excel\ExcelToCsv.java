/**
 * @Title: EasyExcelUtils.java
 * @Package cn.cmcc.common.utils.file
 * 
 * <AUTHOR>
 * @date 2022年1月10日
 * @version V1.0
 */

package cn.cmcc.common.utils.file.excel;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Iterator;

import org.apache.commons.io.FilenameUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import cn.cmcc.common.utils.file.FileUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ExcelToCsv {

	/**
	 * 
	 * execl转csv
	 *
	 * @Title: toCsv   
	 * @param inputFile
	 * @param outputFile void
	 * @Param separtor csv分割符
	 * @throws
	 */
    public static void toCsv(File inputFile, File outputFile , String separtor) {
        StringBuffer data = new StringBuffer();
        try(FileOutputStream fos = FileUtils.openOutputStream(outputFile);FileInputStream fis = FileUtils.openInputStream(inputFile)){
            Workbook workbook = null;

            String ext = FilenameUtils.getExtension(inputFile.toString());
            if (ext.equalsIgnoreCase("xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else if (ext.equalsIgnoreCase("xls")) {
                workbook = new HSSFWorkbook(fis);
            }

            //int numberOfSheets = workbook.getNumberOfSheets();
            Row row;
            Cell cell;
            Integer cellNum = null;
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            while (rowIterator.hasNext()) {
                row = rowIterator.next();
                if(cellNum == null) cellNum = Integer.valueOf(row.getLastCellNum());
                for(int index = 0; index < cellNum ; ++ index) {
                	cell = row.getCell(index);
                	switch (cell.getCellType()) {
                    case BOOLEAN:
                         data.append(cell.getBooleanCellValue() );
                         break;
                    case NUMERIC:
//                         data.append(cell.getNumericCellValue() + separtor);//此处不能加分隔符，如果加分隔符(separtor)在下面又加一个，造成空列，找了几小时才找到原因
                        Double value = cell.getNumericCellValue();
                        data.append(value.intValue());
                         break;
                    case STRING:
                     	if(cell.getStringCellValue().contains(separtor)) {
                     		data.append("\"" + cell.getStringCellValue() + "\"" );
                     	}else {
                     		data.append(cell.getStringCellValue());
                     	}
                         break;
                    case BLANK:
                         data.append("");
                         break;
                    default:
//                         data.append(cell + separtor);
                        data.append(cell);
                    }
                	if(index < cellNum-1) {
                        data.append(separtor);
                	}
                    log.info("index:{},cellNum:{},cellType:{},data:{}",index,cellNum,cell.getCellType(),data);
                }
//                log.info("开始下一行");
                data.append('\n');
            }
            /**
             * windows乱码取消注释
             * byte[] UTF8_BOM = {(byte)0xef,(byte)0xbb,(byte)0xbf};
             *  fos.write({(byte)0xef,(byte)0xbb,(byte)0xbf});
             */
           String temStr = data.toString().replaceAll("0x00","");
            fos.write(temStr.getBytes());
        } catch (Exception e) {
        	log.error(e.getMessage(),e);
        }
    }
}
